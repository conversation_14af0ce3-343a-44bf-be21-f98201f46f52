---
description: Repository Information Overview
alwaysApply: true
---

# Moroccan Accounting Learning App Information

## Summary
A Flutter application designed to help users learn Moroccan accounting principles through interactive guides, quizzes, and progress tracking. It covers various topics including General Accounting, Corporate Tax (IS), Personal Income Tax (IR), VAT (TVA), Fixed Assets, Provisions, and Analytical Accounting.

## Structure
- `lib/`: Core application code (models, screens, services, providers, controllers, widgets)
- `assets/`: Static assets (JSON data files, images, sounds, fonts)
- `test/`: Test files for various components
- Platform-specific directories: `android/`, `ios/`, `web/`, `windows/`, `macos/`, `linux/`

## Language & Runtime
**Language**: Dart
**Version**: SDK >=3.1.0 <4.0.0
**Framework**: Flutter >=3.10.0
**Build System**: Flutter build system
**Package Manager**: pub (Flutter/Dart package manager)

## Dependencies
**Main Dependencies**:
- `flutter_riverpod` / `provider`: State management
- `hive` / `hive_flutter`: Local database storage
- `intl`: Internationalization and formatting
- `google_fonts`: Custom fonts
- `fl_chart` / `syncfusion_flutter_charts`: Data visualization
- `just_audio`: Audio playback for quiz feedback
- `flutter_tts` / `speech_to_text`: Accessibility features
- `connectivity_plus` / `flutter_cache_manager`: Offline support

**Development Dependencies**:
- `flutter_test`: Testing framework
- `mockito`: Mocking for tests
- `build_runner`: Code generation
- `flutter_lints`: Linting rules

## Build & Installation
```bash
# Install dependencies
flutter pub get

# Generate code (for Hive, Freezed models)
flutter pub run build_runner build --delete-conflicting-outputs

# Run the application
flutter run

# Build for specific platforms
flutter build apk  # Android
flutter build ios  # iOS
flutter build web  # Web
flutter build windows  # Windows
flutter build macos  # macOS
flutter build linux  # Linux
```

## Testing
**Framework**: Flutter test
**Test Location**: `test/` directory
**Test Types**:
- Widget tests: `test/widget/`
- Service tests: `test/services/`
- Integration tests: `test/integration/`
- Accessibility tests: `test/accessibility/`
- Offline tests: `test/offline/`

**Run Command**:
```bash
flutter test
```

## Key Features
- **Comprehensive Guides**: Detailed explanations of Moroccan accounting topics
- **Interactive Quizzes**: Test knowledge with feedback and sound effects
- **Progress Tracking**: Monitors user progress using Hive for local storage
- **Adaptive Learning**: Personalized learning paths and spaced repetition
- **Accessibility**: Text-to-speech, voice input, and customizable UI
- **Offline Support**: Caching for offline access to content
- **Multi-platform**: Supports mobile, web, and desktop platforms