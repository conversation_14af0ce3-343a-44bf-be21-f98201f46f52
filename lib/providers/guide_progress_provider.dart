import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/guide/guide_progress_data.dart';
import '../services/guide_progress_service.dart';

/// Provider for the guide progress service
final guideProgressServiceProvider = Provider<GuideProgressService>((ref) {
  return GuideProgressService();
});

/// Provider for guide completion percentage
final guideProgressProvider = FutureProvider.family<double, GuideProgressParams>((ref, params) async {
  final service = ref.read(guideProgressServiceProvider);
  return await service.getGuideCompletionPercentage(params.guideId, params.sectionIds);
});

/// Provider for individual section progress
final sectionProgressProvider = FutureProvider.family<GuideProgressData?, SectionProgressParams>((ref, params) async {
  final service = ref.read(guideProgressServiceProvider);
  return await service.getSectionProgress(params.guideId, params.sectionId);
});

/// Provider for all completed sections in a guide
final completedSectionsProvider = FutureProvider.family<List<String>, String>((ref, guideId) async {
  final service = ref.read(guideProgressServiceProvider);
  return await service.getCompletedSections(guideId);
});

/// Provider for all progress data for a guide
final guideProgressDataProvider = FutureProvider.family<List<GuideProgressData>, String>((ref, guideId) async {
  final service = ref.read(guideProgressServiceProvider);
  return await service.getGuideProgress(guideId);
});

/// Provider for recently visited sections
final recentlyVisitedSectionsProvider = FutureProvider.family<List<GuideProgressData>, int>((ref, limit) async {
  final service = ref.read(guideProgressServiceProvider);
  return await service.getRecentlyVisitedSections(limit: limit);
});

/// Provider for total time spent on a guide
final totalTimeSpentProvider = FutureProvider.family<int, String>((ref, guideId) async {
  final service = ref.read(guideProgressServiceProvider);
  return await service.getTotalTimeSpent(guideId);
});

/// Provider for learning statistics across all guides
final learningStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.read(guideProgressServiceProvider);
  return await service.getLearningStatistics();
});

/// Provider for overall progress across all guides
final allGuidesProgressProvider = FutureProvider<GuideOverallProgress>((ref) async {
  final service = ref.read(guideProgressServiceProvider);
  final stats = await service.getLearningStatistics();
  
  final guideStats = stats['guideStats'] as Map<String, Map<String, dynamic>>;
  final totalTimeSpent = stats['totalTimeSpent'] as int;
  final totalSectionsCompleted = stats['totalSectionsCompleted'] as int;
  
  // Calculate overall completion percentage
  int totalSections = 0;
  int completedSections = 0;
  final guideProgressList = <GuideProgress>[];
  
  for (final entry in guideStats.entries) {
    final guideId = entry.key;
    final guideStat = entry.value;
    final sections = guideStat['totalSections'] as int;
    final completed = guideStat['sectionsCompleted'] as int;
    final timeSpent = guideStat['timeSpent'] as int;
    final lastVisited = guideStat['lastVisited'] as DateTime;
    
    totalSections += sections;
    completedSections += completed;
    
    guideProgressList.add(GuideProgress(
      guideId: guideId,
      totalSections: sections,
      completedSections: completed,
      completionPercentage: sections > 0 ? completed / sections : 0.0,
      timeSpent: timeSpent,
      lastVisited: lastVisited,
    ));
  }
  
  final overallPercentage = totalSections > 0 ? completedSections / totalSections : 0.0;
  
  return GuideOverallProgress(
    totalTimeSpent: totalTimeSpent,
    totalSectionsCompleted: totalSectionsCompleted,
    totalSections: totalSections,
    overallCompletionPercentage: overallPercentage,
    guideProgress: guideProgressList,
  );
});

/// Notifier for managing guide progress state changes
class GuideProgressNotifier extends StateNotifier<AsyncValue<void>> {
  final GuideProgressService _service;
  final Ref _ref;

  GuideProgressNotifier(this._service, this._ref) : super(const AsyncValue.data(null));

  /// Marks a section as completed and refreshes related providers
  Future<void> markSectionCompleted(
    String guideId, 
    String sectionId, {
    int timeSpent = 0,
    double scrollProgress = 1.0,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.markSectionCompleted(
        guideId, 
        sectionId, 
        timeSpent: timeSpent,
        scrollProgress: scrollProgress,
      );
      
      // Invalidate related providers to trigger refresh
      _ref.invalidate(sectionProgressProvider);
      _ref.invalidate(guideProgressProvider);
      _ref.invalidate(completedSectionsProvider);
      _ref.invalidate(guideProgressDataProvider);
      _ref.invalidate(learningStatisticsProvider);
      _ref.invalidate(allGuidesProgressProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Updates section progress and refreshes related providers
  Future<void> updateSectionProgress(
    String guideId,
    String sectionId, {
    int? additionalTimeSpent,
    double? scrollProgress,
    bool? markCompleted,
  }) async {
    try {
      await _service.updateSectionProgress(
        guideId,
        sectionId,
        additionalTimeSpent: additionalTimeSpent,
        scrollProgress: scrollProgress,
        markCompleted: markCompleted,
      );
      
      // Invalidate related providers
      _ref.invalidate(sectionProgressProvider);
      if (markCompleted == true) {
        _ref.invalidate(guideProgressProvider);
        _ref.invalidate(completedSectionsProvider);
        _ref.invalidate(learningStatisticsProvider);
        _ref.invalidate(allGuidesProgressProvider);
      }
    } catch (error) {
      // Handle error silently for progress updates
      debugPrint('Error updating section progress: $error');
    }
  }

  /// Resets progress for a guide
  Future<void> resetGuideProgress(String guideId) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.resetGuideProgress(guideId);
      
      // Invalidate all related providers
      _ref.invalidate(sectionProgressProvider);
      _ref.invalidate(guideProgressProvider);
      _ref.invalidate(completedSectionsProvider);
      _ref.invalidate(guideProgressDataProvider);
      _ref.invalidate(learningStatisticsProvider);
      _ref.invalidate(allGuidesProgressProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider for the guide progress notifier
final guideProgressNotifierProvider = StateNotifierProvider<GuideProgressNotifier, AsyncValue<void>>((ref) {
  final service = ref.read(guideProgressServiceProvider);
  return GuideProgressNotifier(service, ref);
});

/// Parameter classes for providers
class GuideProgressParams {
  final String guideId;
  final List<String> sectionIds;

  const GuideProgressParams({
    required this.guideId,
    required this.sectionIds,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GuideProgressParams &&
        other.guideId == guideId &&
        _listEquals(other.sectionIds, sectionIds);
  }

  @override
  int get hashCode => Object.hash(guideId, Object.hashAll(sectionIds));

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}

class SectionProgressParams {
  final String guideId;
  final String sectionId;

  const SectionProgressParams({
    required this.guideId,
    required this.sectionId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SectionProgressParams &&
        other.guideId == guideId &&
        other.sectionId == sectionId;
  }

  @override
  int get hashCode => Object.hash(guideId, sectionId);
}

/// Data classes for progress information
class GuideProgress {
  final String guideId;
  final int totalSections;
  final int completedSections;
  final double completionPercentage;
  final int timeSpent;
  final DateTime lastVisited;

  const GuideProgress({
    required this.guideId,
    required this.totalSections,
    required this.completedSections,
    required this.completionPercentage,
    required this.timeSpent,
    required this.lastVisited,
  });
}

class GuideOverallProgress {
  final int totalTimeSpent;
  final int totalSectionsCompleted;
  final int totalSections;
  final double overallCompletionPercentage;
  final List<GuideProgress> guideProgress;

  const GuideOverallProgress({
    required this.totalTimeSpent,
    required this.totalSectionsCompleted,
    required this.totalSections,
    required this.overallCompletionPercentage,
    required this.guideProgress,
  });
}
