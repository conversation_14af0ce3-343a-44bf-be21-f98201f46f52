import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculators/financial_ratios_data.dart';
import '../models/calculators/enhanced_depreciation_data.dart';
import '../models/calculators/tax_optimization_data.dart';
import '../models/calculators/calculation_history_item.dart';
import '../models/immobilisations/amortization_row.dart';
import '../services/financial_ratios_service.dart';
import '../services/enhanced_depreciation_service.dart';
import '../services/tax_optimization_service.dart';
import '../services/calculation_history_service.dart';
import '../widgets/journal_comptable_widget.dart';
import '../utils/excel_export_utils.dart';
import '../utils/pdf_export_utils.dart';
import '../exceptions/export_exceptions.dart';

// Service providers
final financialRatiosServiceProvider = Provider<FinancialRatiosService>((ref) {
  return FinancialRatiosService();
});

final enhancedDepreciationServiceProvider = Provider<EnhancedDepreciationService>((ref) {
  return EnhancedDepreciationService();
});

final taxOptimizationServiceProvider = Provider<TaxOptimizationService>((ref) {
  return TaxOptimizationService();
});

final calculationHistoryServiceProvider = Provider<CalculationHistoryService>((ref) {
  return CalculationHistoryService();
});

// Financial Ratios Calculator State
final financialRatiosInputProvider = StateProvider<FinancialRatiosInput?>((ref) => null);
final financialRatiosResultProvider = StateProvider<FinancialRatiosResult?>((ref) => null);
final financialRatiosLoadingProvider = StateProvider<bool>((ref) => false);

// Enhanced Depreciation Calculator State
final depreciationInputProvider = StateProvider<EnhancedDepreciationInput?>((ref) => null);
final depreciationResultProvider = StateProvider<EnhancedDepreciationResult?>((ref) => null);
final depreciationComparisonsProvider = StateProvider<List<DepreciationComparison>?>((ref) => null);
final depreciationJournalEntriesProvider = StateProvider<List<JournalEntry>?>((ref) => null);
final depreciationLoadingProvider = StateProvider<bool>((ref) => false);

// Tax Optimization Wizard State
final taxOptimizationInputProvider = StateProvider<TaxOptimizationInput?>((ref) => null);
final taxOptimizationResultProvider = StateProvider<TaxOptimizationResult?>((ref) => null);
final taxOptimizationLoadingProvider = StateProvider<bool>((ref) => false);
final taxOptimizationCurrentStepProvider = StateProvider<int>((ref) => 0);

// Calculator Actions Provider
final calculatorActionsProvider = Provider<CalculatorActions>((ref) {
  return CalculatorActions(ref);
});

class CalculatorActions {
  final Ref _ref;

  CalculatorActions(this._ref);

  // Financial Ratios Actions
  Future<void> calculateFinancialRatios(FinancialRatiosInput input) async {
    _ref.read(financialRatiosLoadingProvider.notifier).state = true;
    _ref.read(financialRatiosInputProvider.notifier).state = input;

    try {
      final service = _ref.read(financialRatiosServiceProvider);
      final result = service.calculateRatios(input);
      
      _ref.read(financialRatiosResultProvider.notifier).state = result;
      
      // Save to history
      await _saveFinancialRatiosToHistory(input, result);
    } catch (e) {
      _ref.read(financialRatiosResultProvider.notifier).state = null;
      rethrow;
    } finally {
      _ref.read(financialRatiosLoadingProvider.notifier).state = false;
    }
  }

  void clearFinancialRatiosResults() {
    _ref.read(financialRatiosInputProvider.notifier).state = null;
    _ref.read(financialRatiosResultProvider.notifier).state = null;
  }

  // Enhanced Depreciation Actions
  Future<void> calculateDepreciation(EnhancedDepreciationInput input, {bool compareAll = false}) async {
    _ref.read(depreciationLoadingProvider.notifier).state = true;
    _ref.read(depreciationInputProvider.notifier).state = input;

    try {
      final service = _ref.read(enhancedDepreciationServiceProvider);
      final result = service.calculateDepreciation(input);
      
      _ref.read(depreciationResultProvider.notifier).state = result;
      
      // Generate journal entries from the calculation result
      final journalEntries = _generateJournalEntries(input, result);
      _ref.read(depreciationJournalEntriesProvider.notifier).state = journalEntries;
      
      // Calculate comparisons if requested
      if (compareAll) {
        final comparisons = service.compareDepreciationMethods(input);
        _ref.read(depreciationComparisonsProvider.notifier).state = comparisons;
      } else {
        _ref.read(depreciationComparisonsProvider.notifier).state = null;
      }
      
      // Save to history
      await _saveDepreciationToHistory(input, result);
    } catch (e) {
      _ref.read(depreciationResultProvider.notifier).state = null;
      _ref.read(depreciationComparisonsProvider.notifier).state = null;
      _ref.read(depreciationJournalEntriesProvider.notifier).state = null;
      rethrow;
    } finally {
      _ref.read(depreciationLoadingProvider.notifier).state = false;
    }
  }

  void clearDepreciationResults() {
    _ref.read(depreciationInputProvider.notifier).state = null;
    _ref.read(depreciationResultProvider.notifier).state = null;
    _ref.read(depreciationComparisonsProvider.notifier).state = null;
    _ref.read(depreciationJournalEntriesProvider.notifier).state = null;
  }

  // Tax Optimization Actions
  Future<void> analyzeTaxOptimization(TaxOptimizationInput input) async {
    _ref.read(taxOptimizationLoadingProvider.notifier).state = true;
    _ref.read(taxOptimizationInputProvider.notifier).state = input;

    try {
      final service = _ref.read(taxOptimizationServiceProvider);
      final result = service.analyzeAndOptimize(input);
      
      _ref.read(taxOptimizationResultProvider.notifier).state = result;
      
      // Save to history
      await _saveTaxOptimizationToHistory(input, result);
    } catch (e) {
      _ref.read(taxOptimizationResultProvider.notifier).state = null;
      rethrow;
    } finally {
      _ref.read(taxOptimizationLoadingProvider.notifier).state = false;
    }
  }

  void clearTaxOptimizationResults() {
    _ref.read(taxOptimizationInputProvider.notifier).state = null;
    _ref.read(taxOptimizationResultProvider.notifier).state = null;
    _ref.read(taxOptimizationCurrentStepProvider.notifier).state = 0;
  }

  void setTaxOptimizationStep(int step) {
    _ref.read(taxOptimizationCurrentStepProvider.notifier).state = step;
  }

  // History saving methods
  Future<void> _saveFinancialRatiosToHistory(FinancialRatiosInput input, FinancialRatiosResult result) async {
    try {
      final historyService = _ref.read(calculationHistoryServiceProvider);
      final historyItem = CalculationHistoryItem.fromFinancialRatiosCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        inputData: input.toJson(),
        resultData: result.toJson(),
        name: 'Analyse Ratios Financiers',
        tags: ['ratios', 'analyse', 'financier'],
      );
      
      await historyService.saveCalculation(historyItem);
    } catch (e) {
      // Log error but don't fail the calculation
      debugPrint('Failed to save financial ratios to history: $e');
    }
  }

  Future<void> _saveDepreciationToHistory(EnhancedDepreciationInput input, EnhancedDepreciationResult result) async {
    try {
      final historyService = _ref.read(calculationHistoryServiceProvider);
      final historyItem = CalculationHistoryItem.fromDepreciationCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        inputData: input.toJson(),
        resultData: result.toJson(),
        name: 'Amortissement ${input.assetName}',
        tags: ['amortissement', input.method.name, input.assetName.toLowerCase()],
      );
      
      await historyService.saveCalculation(historyItem);
    } catch (e) {
      // Log error but don't fail the calculation
      debugPrint('Failed to save depreciation to history: $e');
    }
  }

  Future<void> _saveTaxOptimizationToHistory(TaxOptimizationInput input, TaxOptimizationResult result) async {
    try {
      final historyService = _ref.read(calculationHistoryServiceProvider);
      final historyItem = CalculationHistoryItem.fromTaxOptimizationCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        inputData: input.toJson(),
        resultData: result.toJson(),
        name: 'Optimisation Fiscale ${input.companyProfile.legalForm.displayName}',
        tags: ['optimisation', 'fiscal', input.companyProfile.sector.name],
      );
      
      await historyService.saveCalculation(historyItem);
    } catch (e) {
      // Log error but don't fail the calculation
      debugPrint('Failed to save tax optimization to history: $e');
    }
  }

  // Journal entries generation for depreciation
  List<JournalEntry> _generateJournalEntries(EnhancedDepreciationInput input, EnhancedDepreciationResult result) {
    if (result.amortizationTable.isEmpty) return [];

    // Use the service to generate journal entries with asset type determination
    final inputWithAssetType = input.copyWith(assetType: _determineAssetType(input.assetName));
    final service = EnhancedDepreciationService();
    return service.generateJournalEntries(result.amortizationTable, inputWithAssetType);
  }

  String _determineAssetType(String assetName) {
    final name = assetName.toLowerCase();
    
    // Try to determine asset type from asset name
    if (name.contains('construction') || name.contains('bâtiment')) {
      return 'buildings';
    } else if (name.contains('véhicule') || name.contains('transport')) {
      return 'transport_equipment';
    } else if (name.contains('ordinateur') || name.contains('informatique')) {
      return 'computer_equipment';
    } else if (name.contains('mobilier') || name.contains('bureau')) {
      return 'office_equipment';
    } else if (name.contains('machine') || name.contains('équipement')) {
      return 'technical_installations';
    }
    
    return 'buildings'; // Default asset type
  }

}

// Form validation providers
final financialRatiosFormValidProvider = Provider<bool>((ref) {
  final input = ref.watch(financialRatiosInputProvider);
  return input != null && _isValidFinancialRatiosInput(input);
});

final depreciationFormValidProvider = Provider<bool>((ref) {
  final input = ref.watch(depreciationInputProvider);
  return input != null && input.isValid;
});

final taxOptimizationFormValidProvider = Provider<bool>((ref) {
  final input = ref.watch(taxOptimizationInputProvider);
  return input != null && _isValidTaxOptimizationInput(input);
});

// Helper functions
bool _isValidFinancialRatiosInput(FinancialRatiosInput input) {
  return input.totalAssets > 0 &&
         input.revenue > 0 &&
         input.currentAssets >= 0 &&
         input.currentLiabilities >= 0 &&
         input.totalEquity >= 0;
}

bool _isValidTaxOptimizationInput(TaxOptimizationInput input) {
  return input.companyProfile.annualRevenue > 0 &&
         input.companyProfile.employeeCount >= 0 &&
         input.currentTaxSituation.accountingResult != 0 &&
         input.optimizationGoals.objectives.isNotEmpty;
}

// Calculator state management providers
final activeCalculatorProvider = StateProvider<CalculatorType?>((ref) => null);

final calculatorStateProvider = Provider<CalculatorState>((ref) {
  final activeCalculator = ref.watch(activeCalculatorProvider);
  
  switch (activeCalculator) {
    case CalculatorType.financialRatios:
      return CalculatorState(
        isLoading: ref.watch(financialRatiosLoadingProvider),
        hasInput: ref.watch(financialRatiosInputProvider) != null,
        hasResult: ref.watch(financialRatiosResultProvider) != null,
        isValid: ref.watch(financialRatiosFormValidProvider),
      );
    case CalculatorType.depreciation:
      final result = ref.watch(depreciationResultProvider);
      final hasMultipleTables = result?.hasMultipleTables ?? false;
      final availableTables = <String>[];
      if (result != null) {
        if (result.accountingAmortizationTable != null && result.accountingAmortizationTable!.isNotEmpty) {
          availableTables.add('accounting');
        }
        if (result.fiscalAmortizationTable != null && result.fiscalAmortizationTable!.isNotEmpty) {
          availableTables.add('fiscal');
        }
        if (result.derogatoryAmortizationTable != null && result.derogatoryAmortizationTable!.isNotEmpty) {
          availableTables.add('derogatory');
        }
      }

      return CalculatorState(
        isLoading: ref.watch(depreciationLoadingProvider),
        hasInput: ref.watch(depreciationInputProvider) != null,
        hasResult: ref.watch(depreciationResultProvider) != null,
        isValid: ref.watch(depreciationFormValidProvider),
        hasJournalEntries: ref.watch(depreciationJournalEntriesProvider) != null && 
                          ref.watch(depreciationJournalEntriesProvider)!.isNotEmpty,
        hasMultipleTables: hasMultipleTables,
        canExportIndividualTables: hasMultipleTables,
        availableTables: availableTables,
      );
    case CalculatorType.taxOptimization:
      return CalculatorState(
        isLoading: ref.watch(taxOptimizationLoadingProvider),
        hasInput: ref.watch(taxOptimizationInputProvider) != null,
        hasResult: ref.watch(taxOptimizationResultProvider) != null,
        isValid: ref.watch(taxOptimizationFormValidProvider),
      );
    default:
      return const CalculatorState(
        isLoading: false,
        hasInput: false,
        hasResult: false,
        isValid: false,
      );
  }
});

class CalculatorState {
  final bool isLoading;
  final bool hasInput;
  final bool hasResult;
  final bool isValid;
  final bool hasJournalEntries;

  /// New properties for export and multiple table support
  final bool hasMultipleTables;
  final bool canExportIndividualTables;
  final List<String> availableTables;

  const CalculatorState({
    required this.isLoading,
    required this.hasInput,
    required this.hasResult,
    required this.isValid,
    this.hasJournalEntries = false,
    this.hasMultipleTables = false,
    this.canExportIndividualTables = false,
    this.availableTables = const [],
  });

  bool get canCalculate => hasInput && isValid && !isLoading;

  /// Allow export even when loading for better UX (per requirements)
  bool get canExport => hasResult;

  bool get canSave => hasResult && !isLoading;
  bool get canExportJournal => hasJournalEntries && !isLoading;
}

// Export providers
final calculatorExportProvider = Provider<CalculatorExportActions>((ref) {
  return CalculatorExportActions(ref);
});

class CalculatorExportActions {
  final Ref _ref;

  CalculatorExportActions(this._ref);

  // Simple in-memory export history for UX convenience
  static final List<Map<String, dynamic>> _exportHistory = [];

  /// Build default export settings (could be extended to read user preferences)
  ExportSettings _buildExportSettings({String? companyName, bool includeFormulas = true}) {
    return ExportSettings(
      companyName: companyName,
      includeFormulas: includeFormulas,
      includeCharts: true,
      useAlternatingRowColors: true,
      freezePanes: true,
      includeSummary: true,
      language: 'fr',
    );
  }

  Future<void> exportFinancialRatios({required String format}) async {
    final input = _ref.read(financialRatiosInputProvider);
    final result = _ref.read(financialRatiosResultProvider);
    
    // Enhanced validation with specialized exceptions
    if (input == null || result == null) {
      throw ExportExceptionFactory.missingTable('financial_ratios', 'export_financial_ratios', suggestion: 'Aucun résultat de ratios financiers disponible pour l\'export');
    }

    // Pre-export data validation
    _validateFinancialRatiosData(input, result);

    // Convert financial ratios to table data for export
    final tableData = _financialRatiosToTableData(input, result);
    
    try {
      switch (format.toLowerCase()) {
        case 'pdf':
          // Enable PDF export functionality
          final table = TableData(
            name: tableData['name'] as String,
            title: tableData['title'] as String,
            headers: (tableData['headers'] as List).map((e) => e.toString()).toList(),
            rows: (tableData['rows'] as List).map<List<String>>((r) => (r as List).map((c) => c.toString()).toList()).toList(),
            numericColumns: (tableData['numericColumns'] as List?)?.map((e) => e as int).toList(),
            currencyColumns: (tableData['currencyColumns'] as List?)?.map((e) => e as int).toList(),
          );
          
          final file = await PdfExportUtils.exportCalculationToPdf(
            [table],
            'ratios_financiers_${DateTime.now().millisecondsSinceEpoch}',
            title: 'Analyse des Ratios Financiers',
          );
          await _recordExportHistory(file, ['financial_ratios'], 'pdf');
          break;
        case 'excel':
          // Use ExcelExportUtils to create a single sheet from the tableData
          final table = TableData(
            name: tableData['name'] as String,
            title: tableData['title'] as String,
            headers: (tableData['headers'] as List).map((e) => e.toString()).toList(),
            rows: (tableData['rows'] as List).map<List<String>>((r) => (r as List).map((c) => c.toString()).toList()).toList(),
            numericColumns: (tableData['numericColumns'] as List?)?.map((e) => e as int).toList(),
            currencyColumns: (tableData['currencyColumns'] as List?)?.map((e) => e as int).toList(),
          );

          final file = await ExcelExportUtils.exportMultipleTablesExcel([table], settings: _buildExportSettings(companyName: null));
          await _recordExportHistory(file, ['financial_ratios'], 'excel');
          break;
        default:
          throw ExportExceptionFactory.unsupportedFormat(format, ['excel', 'pdf']);
      }
    } on ExportException {
      // Re-throw export exceptions as-is
      rethrow;
    } on FileSystemException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_financial_ratios', e.message, filePath: e.path);
    } on IOException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_financial_ratios', e.toString());
    } catch (e) {
      throw ExportExceptionFactory.generic('export_financial_ratios', 'Erreur inattendue lors de l\'export des ratios financiers: $e');
    }
  }

  /// Main depreciation export with optional selective tables and journal inclusion
  Future<void> exportDepreciation({
    required String format,
    bool includeJournalEntries = false,
    List<String>? tablesToExport,
  }) async {
    final input = _ref.read(depreciationInputProvider);
    final result = _ref.read(depreciationResultProvider);
    final journalEntries = _ref.read(depreciationJournalEntriesProvider);
    
    // Enhanced validation with specialized exceptions
    if (input == null || result == null) {
      throw ExportExceptionFactory.missingTable('depreciation', 'export_depreciation', suggestion: 'Aucun résultat d\'amortissement disponible pour l\'export');
    }

    // Comprehensive data validation
    _validateDepreciationData(input, result);

    try {
      final tables = _buildDepreciationTables(result, input, journalEntries, tablesToExport, includeJournalEntries);
      
      if (tables.isEmpty) {
        throw ExportExceptionFactory.missingTable('selected_tables', 'export_depreciation', suggestion: 'Aucune table sélectionnée disponible pour l\'export');
      }

      switch (format.toLowerCase()) {
        case 'excel':
          // For multiple tables with complete data, use specialized export
          if (result.hasMultipleTables && (tablesToExport == null || tablesToExport.isEmpty)) {
            final exportData = _buildAmortizationExportData(result, input);
            final file = await ExcelExportUtils.exportAmortizationToExcel(exportData);
            await _recordExportHistory(file, ['accounting', 'fiscal', 'derogatory'], 'excel');
            return;
          }

          // Standard table export
          final file = await ExcelExportUtils.exportMultipleTablesExcel(tables, settings: _buildExportSettings(companyName: input.assetName));
          final recordedTables = tables.map((t) => t.name).toList();
          await _recordExportHistory(file, recordedTables, 'excel');
          break;
        case 'pdf':
          // Enable PDF export functionality
          final fileName = 'amortissement_${input.assetName.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}';
          final file = await PdfExportUtils.exportCalculationToPdf(
            tables,
            fileName,
            title: 'Calcul d\'Amortissement - ${input.assetName}',
          );
          final recordedTables = tables.map((t) => t.name).toList();
          await _recordExportHistory(file, recordedTables, 'pdf');
          break;
        default:
          throw ExportExceptionFactory.unsupportedFormat(format, ['excel', 'pdf']);
      }
    } on ExportException {
      // Re-throw export exceptions as-is
      rethrow;
    } on FileSystemException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_depreciation', e.message, filePath: e.path);
    } on IOException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_depreciation', e.toString());
    } catch (e) {
      throw ExportExceptionFactory.generic('export_depreciation', 'Erreur inattendue lors de l\'export d\'amortissement: $e');
    }
  }

  /// Export accounting table only helper
  Future<void> exportAccountingTable({required String format}) async {
    return exportDepreciation(format: format, tablesToExport: ['accounting']);
  }

  /// Export fiscal table only helper
  Future<void> exportFiscalTable({required String format}) async {
    return exportDepreciation(format: format, tablesToExport: ['fiscal']);
  }

  /// Export derogatory table only helper
  Future<void> exportDerogatoryTable({required String format}) async {
    return exportDepreciation(format: format, tablesToExport: ['derogatory']);
  }

  Future<void> exportDepreciationJournalEntries({required String format}) async {
    final journalEntries = _ref.read(depreciationJournalEntriesProvider);
    
    // Enhanced validation with specialized exceptions
    if (journalEntries == null || journalEntries.isEmpty) {
      throw ExportExceptionFactory.missingTable('journal_entries', 'export_journal_entries', suggestion: 'Aucune écriture comptable disponible pour l\'export');
    }

    // Validate journal entries data
    _validateJournalEntriesData(journalEntries);

    // Convert journal entries to table data for export
    final journalTableData = journalEntriesToTableData(journalEntries);
    
    try {
      final table = TableData(
        name: journalTableData['name'] as String,
        title: journalTableData['title'] as String,
        headers: (journalTableData['headers'] as List).map((e) => e.toString()).toList(),
        rows: (journalTableData['rows'] as List).map<List<String>>((r) => (r as List).map((c) => c.toString()).toList()).toList(),
        numericColumns: (journalTableData['numericColumns'] as List?)?.map((e) => e as int).toList(),
        currencyColumns: (journalTableData['currencyColumns'] as List?)?.map((e) => e as int).toList(),
      );
      
      switch (format.toLowerCase()) {
        case 'pdf':
          // Enable PDF export functionality
          final file = await PdfExportUtils.exportCalculationToPdf(
            [table], 
            'ecritures_comptables_${DateTime.now().millisecondsSinceEpoch}',
            title: 'Écritures Comptables d\'Amortissement',
          );
          await _recordExportHistory(file, ['journal'], 'pdf');
          break;
        case 'excel':
          final file = await ExcelExportUtils.exportMultipleTablesExcel([table], settings: _buildExportSettings());
          await _recordExportHistory(file, ['journal'], 'excel');
          break;
        default:
          throw ExportExceptionFactory.unsupportedFormat(format, ['excel', 'pdf']);
      }
    } on ExportException {
      // Re-throw export exceptions as-is
      rethrow;
    } on FileSystemException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_journal', e.message, filePath: e.path);
    } on IOException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_journal', e.toString());
    } catch (e) {
      throw ExportExceptionFactory.generic('export_journal', 'Erreur inattendue lors de l\'export des écritures comptables: $e');
    }
  }

  Map<String, dynamic> journalEntriesToTableData(List<JournalEntry> journalEntries) {
    final rows = <List<String>>[];
    
    for (final entry in journalEntries) {
      for (final line in entry.lines) {
        rows.add([
          entry.date ?? '',
          line.account,
          line.label,
          line.debit ?? '',
          line.credit ?? '',
        ]);
      }
    }

    return {
      'name': 'journal',
      'title': 'Écritures comptables',
      'headers': ['Date', 'Compte', 'Libellé', 'Débit', 'Crédit'],
      'rows': rows,
      'numericColumns': [3, 4],
      'currencyColumns': [3, 4],
    };
  }

  Future<void> exportTaxOptimization({required String format}) async {
    final input = _ref.read(taxOptimizationInputProvider);
    final result = _ref.read(taxOptimizationResultProvider);
    
    // Enhanced validation with specialized exceptions
    if (input == null || result == null) {
      throw ExportExceptionFactory.missingTable('tax_optimization', 'export_tax_optimization', suggestion: 'Aucun résultat d\'optimisation fiscale disponible pour l\'export');
    }

    // Validate tax optimization data
    _validateTaxOptimizationData(input, result);

    // Convert tax optimization result to table data for export
    final taxTableData = _taxOptimizationToTableData(input, result);
    
    try {
      final table = TableData(
        name: taxTableData['name'] as String,
        title: taxTableData['title'] as String,
        headers: (taxTableData['headers'] as List).map((e) => e.toString()).toList(),
        rows: (taxTableData['rows'] as List).map<List<String>>((r) => (r as List).map((c) => c.toString()).toList()).toList(),
        numericColumns: (taxTableData['numericColumns'] as List?)?.map((e) => e as int).toList(),
        currencyColumns: (taxTableData['currencyColumns'] as List?)?.map((e) => e as int).toList(),
      );
      
      switch (format.toLowerCase()) {
        case 'pdf':
          // Enable PDF export functionality
          final file = await PdfExportUtils.exportCalculationToPdf(
            [table], 
            'optimisation_fiscale_${DateTime.now().millisecondsSinceEpoch}',
            title: 'Recommandations d\'Optimisation Fiscale',
          );
          await _recordExportHistory(file, ['tax_optimization'], 'pdf');
          break;
        case 'excel':
          final file = await ExcelExportUtils.exportMultipleTablesExcel([table], settings: _buildExportSettings(companyName: null));
          await _recordExportHistory(file, ['tax_optimization'], 'excel');
          break;
        default:
          throw ExportExceptionFactory.unsupportedFormat(format, ['excel', 'pdf']);
      }
    } on ExportException {
      // Re-throw export exceptions as-is
      rethrow;
    } on FileSystemException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_tax_optimization', e.message, filePath: e.path);
    } on IOException catch (e) {
      throw ExportExceptionFactory.fileSystemError('export_tax_optimization', e.toString());
    } catch (e) {
      throw ExportExceptionFactory.generic('export_tax_optimization', 'Erreur inattendue lors de l\'export de l\'optimisation fiscale: $e');
    }
  }

  // Helper methods for converting data to table format
  Map<String, dynamic> _financialRatiosToTableData(FinancialRatiosInput input, FinancialRatiosResult result) {
    final rows = <List<String>>[];
    
    // Add key financial ratios to the table using the correct property structure
    rows.addAll([
      ['Ratio de liquidité générale', result.liquidityRatios.currentRatio.toStringAsFixed(2), 'Actif circulant / Passif circulant'],
      ['Ratio de liquidité réduite', result.liquidityRatios.quickRatio.toStringAsFixed(2), '(Actif circulant - Stocks) / Passif circulant'],
      ['Ratio d\'endettement', result.leverageRatios.debtRatio.toStringAsFixed(2), 'Total dettes / Total actif'],
      ['Rentabilité des actifs (ROA)', '${(result.profitabilityRatios.returnOnAssets * 100).toStringAsFixed(1)}%', 'Résultat net / Total actif'],
      ['Rentabilité des capitaux propres (ROE)', '${(result.profitabilityRatios.returnOnEquity * 100).toStringAsFixed(1)}%', 'Résultat net / Capitaux propres'],
    ]);

    return {
      'name': 'ratios_financiers',
      'title': 'Analyse des Ratios Financiers',
      'headers': ['Ratio', 'Valeur', 'Description'],
      'rows': rows,
      'numericColumns': [1],
      'currencyColumns': [],
    };
  }


  Map<String, dynamic> _taxOptimizationToTableData(TaxOptimizationInput input, TaxOptimizationResult result) {
    final rows = <List<String>>[];
    
    // Add optimization strategies using the correct property structure
    for (final strategy in result.recommendedStrategies) {
      rows.add([
        strategy.name,
        '${strategy.estimatedSavings.toStringAsFixed(0)} DH',
        strategy.description,
        strategy.riskLevel.name,
      ]);
    }

    return {
      'name': 'optimisation_fiscale',
      'title': 'Recommandations d\'Optimisation Fiscale',
      'headers': ['Stratégie', 'Économie Estimée', 'Description', 'Priorité'],
      'rows': rows,
      'numericColumns': [1],
      'currencyColumns': [1],
    };
  }

  /// Convert EnhancedDepreciationResult + input into AmortizationExportData for ExcelExportUtils
  AmortizationExportData _buildAmortizationExportData(EnhancedDepreciationResult result, EnhancedDepreciationInput input) {
    final amortizationRows = result.accountingAmortizationTable ?? result.amortizationTable;
    final derogatoryRows = result.derogatoryAmortizationTable;

    final mode = input.method.displayName;
    final rate = input.degressiveCoefficient ?? 0.0;

    final settings = _buildExportSettings(companyName: input.assetName, includeFormulas: true);

    return AmortizationExportData(
      assetName: input.assetName,
      originalValue: input.assetCost,
      residualValue: input.residualValue,
      duration: input.usefulLifeYears,
      mode: mode,
      rate: rate,
      acquisitionDate: input.acquisitionDate,
      amortizationRows: amortizationRows,
      derogatoryRows: derogatoryRows,
      settings: settings,
      additionalMetadata: {
        'computedAt': DateTime.now().toIso8601String(),
        'hasMultipleTables': result.hasMultipleTables,
      },
    );
  }


  /// Build tables for depreciation export based on selection
  List<TableData> _buildDepreciationTables(
    EnhancedDepreciationResult result, 
    EnhancedDepreciationInput input, 
    List<JournalEntry>? journalEntries, 
    List<String>? tablesToExport, 
    bool includeJournalEntries
  ) {
    final tables = <TableData>[];
    final selection = tablesToExport ?? ['primary'];

    for (final sel in selection) {
      switch (sel.toLowerCase()) {
        case 'primary':
        case 'accounting':
          final accountingRows = result.accountingAmortizationTable ?? result.amortizationTable;
          if (accountingRows.isNotEmpty) {
            tables.add(_convertToTableData(accountingRows, 'amortissement_comptable', 'Amortissement Comptable', input.method == DepreciationMethod.degressive));
          }
          break;
        case 'fiscal':
          if (result.fiscalAmortizationTable != null && result.fiscalAmortizationTable!.isNotEmpty) {
            tables.add(_convertToTableData(result.fiscalAmortizationTable!, 'amortissement_fiscal', 'Amortissement Fiscal', input.fiscalMethod == DepreciationMethod.degressive));
          }
          break;
        case 'derogatory':
        case 'dérogatoire':
          if (result.derogatoryAmortizationTable != null && result.derogatoryAmortizationTable!.isNotEmpty) {
            final table = TableData.fromDerogatory(result.derogatoryAmortizationTable!, name: 'derogatory', title: 'Amortissement Dérogatoire');
            tables.add(table);
          }
          break;
        case 'journal':
          if (includeJournalEntries && journalEntries != null && journalEntries.isNotEmpty) {
            final journalRows = <List<String>>[];
            for (final entry in journalEntries) {
              for (final line in entry.lines) {
                journalRows.add([
                  entry.date ?? '',
                  line.account,
                  line.label,
                  line.debit ?? '',
                  line.credit ?? '',
                ]);
              }
            }
            tables.add(TableData(
              name: 'journal',
              title: 'Écritures comptables',
              headers: ['Date', 'Compte', 'Libellé', 'Débit', 'Crédit'],
              rows: journalRows,
              numericColumns: [3, 4],
              currencyColumns: [3, 4],
            ));
          }
          break;
      }
    }

    return tables;
  }

  // Data validation methods
  void _validateFinancialRatiosData(FinancialRatiosInput input, FinancialRatiosResult result) {
    final errors = <String>[];
    
    if (input.totalAssets <= 0) errors.add('Total des actifs invalide');
    if (input.revenue <= 0) errors.add('Chiffre d\'affaires invalide');
    if (result.liquidityRatios.currentRatio.isNaN || result.liquidityRatios.currentRatio.isInfinite) {
      errors.add('Ratios de liquidité invalides');
    }
    
    if (errors.isNotEmpty) {
      throw ExportExceptionFactory.invalidData('financial_ratios_validation', errors);
    }
  }

  void _validateDepreciationData(EnhancedDepreciationInput input, EnhancedDepreciationResult result) {
    final errors = <String>[];
    
    if (input.assetCost <= 0) errors.add('Coût d\'acquisition invalide');
    if (input.usefulLifeYears <= 0) errors.add('Durée d\'utilité invalide');
    if (input.assetName.trim().isEmpty) errors.add('Nom de l\'actif requis');
    if (result.amortizationTable.isEmpty && (result.accountingAmortizationTable?.isEmpty ?? true)) {
      errors.add('Tableau d\'amortissement vide');
    }
    
    // Validate calculation consistency
    if (result.summary.totalDepreciation < 0) errors.add('Amortissement total négatif');
    if (result.summary.totalDepreciation > input.assetCost) errors.add('Amortissement supérieur au coût');
    
    if (errors.isNotEmpty) {
      throw ExportExceptionFactory.invalidData('depreciation_validation', errors);
    }
  }

  void _validateJournalEntriesData(List<JournalEntry> journalEntries) {
    final errors = <String>[];
    
    for (int i = 0; i < journalEntries.length; i++) {
      final entry = journalEntries[i];
      if (entry.lines.isEmpty) {
        errors.add('Écriture ${i + 1}: aucune ligne comptable');
      }
      
      double totalDebit = 0.0;
      double totalCredit = 0.0;
      
      for (final line in entry.lines) {
        if (line.account.trim().isEmpty) {
          errors.add('Écriture ${i + 1}: compte manquant');
        }
        if (line.debit != null) {
          final debitValue = double.tryParse(line.debit!.replaceAll(',', '.'));
          if (debitValue != null) totalDebit += debitValue;
        }
        if (line.credit != null) {
          final creditValue = double.tryParse(line.credit!.replaceAll(',', '.'));
          if (creditValue != null) totalCredit += creditValue;
        }
      }
      
      // Check balance (allow small rounding differences)
      if ((totalDebit - totalCredit).abs() > 0.01) {
        errors.add('Écriture ${i + 1}: déséquilibrée (Débit: $totalDebit, Crédit: $totalCredit)');
      }
    }
    
    if (errors.isNotEmpty) {
      throw ExportExceptionFactory.invalidData('journal_entries_validation', errors);
    }
  }

  void _validateTaxOptimizationData(TaxOptimizationInput input, TaxOptimizationResult result) {
    final errors = <String>[];
    
    if (input.companyProfile.annualRevenue <= 0) errors.add('Chiffre d\'affaires invalide');
    if (input.companyProfile.employeeCount < 0) errors.add('Nombre d\'employés invalide');
    if (result.recommendedStrategies.isEmpty) errors.add('Aucune stratégie recommandée');
    
    for (final strategy in result.recommendedStrategies) {
      if (strategy.estimatedSavings < 0) errors.add('Économies estimées négatives pour: ${strategy.name}');
      if (strategy.name.trim().isEmpty) errors.add('Nom de stratégie manquant');
    }
    
    if (errors.isNotEmpty) {
      throw ExportExceptionFactory.invalidData('tax_optimization_validation', errors);
    }
  }

  /// Convert amortization rows to TableData using ExcelExportUtils.TableData.fromAmortizationRows
  TableData _convertToTableData(List<AmortizationRow> rows, String name, String title, bool isDegressive) {
    return TableData.fromAmortizationRows(rows, name: name, title: title, isDegressive: isDegressive);
  }

  /// Record export history (simple in-memory tracking)
  Future<void> _recordExportHistory(File file, List<String> tables, String format) async {
    final entry = {
      'timestamp': DateTime.now().toIso8601String(),
      'filePath': file.path,
      'tables': tables,
      'format': format,
    };
    _exportHistory.add(entry);

    // Attempt to persist to calculation history service (best-effort)
    try {
      final historyService = _ref.read(calculationHistoryServiceProvider);
      final historyItem = CalculationHistoryItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        calculatorType: CalculatorType.depreciation,
        inputDataJson: json.encode({'exportedTables': tables, 'format': format}),
        resultDataJson: json.encode({'filePath': file.path, 'timestamp': entry['timestamp']}),
        createdAt: DateTime.now(),
        name: 'Export ${tables.join(', ')}',
        tags: ['export', format],
      );
      await historyService.saveCalculation(historyItem);
    } catch (e) {
      // Non-fatal - keep in-memory history even if persistence fails
      debugPrint('Failed to persist export history: $e');
    }
  }
}

// Journal entries specific providers
final depreciationJournalStateProvider = Provider<JournalEntriesState>((ref) {
  final journalEntries = ref.watch(depreciationJournalEntriesProvider);
  final isLoading = ref.watch(depreciationLoadingProvider);
  
  return JournalEntriesState(
    entries: journalEntries ?? [],
    isLoading: isLoading,
    hasEntries: journalEntries != null && journalEntries.isNotEmpty,
  );
});

class JournalEntriesState {
  final List<JournalEntry> entries;
  final bool isLoading;
  final bool hasEntries;

  const JournalEntriesState({
    required this.entries,
    required this.isLoading,
    required this.hasEntries,
  });

  bool get canExport => hasEntries && !isLoading;
  
  double get totalDebit => entries.fold(0.0, (sum, entry) => sum + entry.totalDebit);
  double get totalCredit => entries.fold(0.0, (sum, entry) => sum + entry.totalCredit);
}

// Calculator presets provider
final calculatorPresetsProvider = Provider<Map<String, List<CalculatorPreset>>>((ref) {
  return {
    'depreciation': [
      CalculatorPreset(
        name: 'Véhicule de tourisme',
        description: 'Configuration standard pour véhicule',
        data: {
          'usefulLife': 5,
          'method': 'degressive',
          'residualValuePercent': 10,
        },
      ),
      CalculatorPreset(
        name: 'Matériel informatique',
        description: 'Configuration pour équipement IT',
        data: {
          'usefulLife': 3,
          'method': 'degressive',
          'residualValuePercent': 5,
        },
      ),
    ],
    'financialRatios': [
      CalculatorPreset(
        name: 'PME Commerce',
        description: 'Ratios typiques pour PME commerciale',
        data: {
          'sector': 'retail',
          'benchmarks': 'pme_commerce',
        },
      ),
    ],
  };
});

class CalculatorPreset {
  final String name;
  final String description;
  final Map<String, dynamic> data;

  const CalculatorPreset({
    required this.name,
    required this.description,
    required this.data,
  });
}