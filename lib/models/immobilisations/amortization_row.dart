class AmortizationRow {
  // Core Properties
  final int year;
  final String yearLabel;
  final double baseAmount;
  final double rate;
  final double annuity;
  final double cumulativeAnnuity;
  final double netBookValue;

  // Degressive Mode Properties
  final double? degressiveRate;
  final double? linearRate;
  final bool isLinearSwitchYear;

  // Derogatory Properties
  final double? accountingAmortization;
  final double? fiscalAmortization;
  final double? derogationProvision;
  final double? derogationReprise;

  // Provision balance for derogatory accounting (account 1351 running balance)
  final double? provisionBalance;

  const AmortizationRow({
    required this.year,
    required this.yearLabel,
    required this.baseAmount,
    required this.rate,
    required this.annuity,
    required this.cumulativeAnnuity,
    required this.netBookValue,
    this.degressiveRate,
    this.linearRate,
    this.isLinearSwitchYear = false,
    this.accountingAmortization,
    this.fiscalAmortization,
    this.derogationProvision,
    this.derogationReprise,
    this.provisionBalance,
  });

  /// Convert from existing Map format used in the calculator
  factory AmortizationRow.fromTableMap(Map<String, String> map) {
    // Extract year from yearLabel (e.g., "2024 (8 mois)" -> 2024)
    final yearLabel = map['Année'] ?? '';
    final yearMatch = RegExp(r'(\d{4})').firstMatch(yearLabel);
    final year = yearMatch != null ? int.parse(yearMatch.group(1)!) : 0;

    // Parse base amount
    final baseStr = map['Base'] ?? '0';
    final baseAmount = double.tryParse(baseStr) ?? 0.0;

    // Parse rate (handle both "Taux" for linear and "Taux dégressif" for degressive)
    final rateStr = map['Taux'] ?? map['Taux dégressif'] ?? '0';
    final rateMatch = RegExp(r'([\d.]+)').firstMatch(rateStr);
    final rate = rateMatch != null ? double.parse(rateMatch.group(1)!) : 0.0;

    // Parse annuity
    final annuityStr = map['Annuités'] ?? '0';
    final annuity = double.tryParse(annuityStr) ?? 0.0;

    // Parse cumulative annuity
    final cumulativeStr = map['Annuités cumulées'] ?? '0';
    final cumulativeAnnuity = double.tryParse(cumulativeStr) ?? 0.0;

    // Parse net book value (handle special case with "(valeur résiduelle)")
    final vncStr = map['VNC'] ?? '0';
    final vncMatch = RegExp(r'([\d.]+)').firstMatch(vncStr);
    final netBookValue = vncMatch != null ? double.parse(vncMatch.group(1)!) : 0.0;

    // Parse degressive rate if present
    double? degressiveRate;
    if (map.containsKey('Taux dégressif')) {
      final degressiveStr = map['Taux dégressif'] ?? '';
      final degressiveMatch = RegExp(r'([\d.]+)').firstMatch(degressiveStr);
      degressiveRate = degressiveMatch != null ? double.parse(degressiveMatch.group(1)!) : null;
    }

    // Parse linear rate if present
    double? linearRate;
    if (map.containsKey('Taux linéaire')) {
      final linearStr = map['Taux linéaire'] ?? '';
      final linearMatch = RegExp(r'([\d.]+)').firstMatch(linearStr);
      linearRate = linearMatch != null ? double.parse(linearMatch.group(1)!) : null;
    }

    // Parse derogatory amounts if present
    double? accountingAmortization;
    if (map.containsKey('Amortissement comptable')) {
      final accountingStr = map['Amortissement comptable'] ?? '0';
      accountingAmortization = double.tryParse(accountingStr);
    }

    double? fiscalAmortization;
    if (map.containsKey('Amortissement fiscal')) {
      final fiscalStr = map['Amortissement fiscal'] ?? '0';
      fiscalAmortization = double.tryParse(fiscalStr);
    }

    double? derogationProvision;
    if (map.containsKey('Dotations')) {
      final dotationStr = map['Dotations'] ?? '';
      if (dotationStr != '-' && dotationStr.isNotEmpty) {
        derogationProvision = double.tryParse(dotationStr);
      }
    }

    double? derogationReprise;
    if (map.containsKey('Reprises')) {
      final repriseStr = map['Reprises'] ?? '';
      if (repriseStr != '-' && repriseStr.isNotEmpty) {
        derogationReprise = double.tryParse(repriseStr);
      }
    }

    // Parse provision balance if present (support multiple possible keys)
    double? provisionBalance;
    final provisionKeys = ['Solde provision', 'Solde de provision', 'Provision', 'Provisions'];
    for (final key in provisionKeys) {
      if (map.containsKey(key)) {
        final pbStr = map[key] ?? '';
        if (pbStr.isNotEmpty && pbStr != '-') {
          final pbMatch = RegExp(r'([\d.]+)').firstMatch(pbStr);
          if (pbMatch != null) {
            provisionBalance = double.tryParse(pbMatch.group(1)!);
            break;
          } else {
            provisionBalance = double.tryParse(pbStr);
            if (provisionBalance != null) break;
          }
        }
      }
    }

    // Determine if this is a linear switch year (when linear rate > degressive rate)
    final isLinearSwitchYear = linearRate != null &&
        degressiveRate != null &&
        linearRate > degressiveRate;

    return AmortizationRow(
      year: year,
      yearLabel: yearLabel,
      baseAmount: baseAmount,
      rate: rate,
      annuity: annuity,
      cumulativeAnnuity: cumulativeAnnuity,
      netBookValue: netBookValue,
      degressiveRate: degressiveRate,
      linearRate: linearRate,
      isLinearSwitchYear: isLinearSwitchYear,
      accountingAmortization: accountingAmortization,
      fiscalAmortization: fiscalAmortization,
      derogationProvision: derogationProvision,
      derogationReprise: derogationReprise,
      provisionBalance: provisionBalance,
    );
  }

  /// Convert to existing Map format for backward compatibility
  Map<String, String> toTableMap() {
    final map = <String, String>{};

    // Always include core fields
    map['Année'] = yearLabel;
    map['Base'] = baseAmount.toStringAsFixed(2);
    map['Annuités'] = annuity.toStringAsFixed(2);
    map['Annuités cumulées'] = cumulativeAnnuity.toStringAsFixed(2);

    // Handle VNC with special formatting for residual value
    if (yearLabel.contains('(') && yearLabel.contains('mois)') &&
        netBookValue < baseAmount * 0.1) { // Likely residual value
      map['VNC'] = '${netBookValue.toStringAsFixed(2)} (valeur résiduelle)';
    } else {
      map['VNC'] = netBookValue.toStringAsFixed(2);
    }

    // Add rate fields based on available data
    if (degressiveRate != null && linearRate != null) {
      // Degressive mode
      map['Taux dégressif'] = '${degressiveRate!.toStringAsFixed(2)} %';
      map['Taux linéaire'] = '${linearRate!.toStringAsFixed(2)} %';
    } else {
      // Linear mode
      map['Taux'] = '${rate.toString()} %';
    }

    // Add derogatory fields if present
    if (accountingAmortization != null) {
      map['Amortissement comptable'] = accountingAmortization!.toStringAsFixed(2);
    }
    if (fiscalAmortization != null) {
      map['Amortissement fiscal'] = fiscalAmortization!.toStringAsFixed(2);
    }
    if (derogationProvision != null && derogationProvision! > 0) {
      map['Dotations'] = derogationProvision!.toStringAsFixed(2);
    } else {
      map['Dotations'] = '-';
    }
    if (derogationReprise != null && derogationReprise! > 0) {
      map['Reprises'] = derogationReprise!.toStringAsFixed(2);
    } else {
      map['Reprises'] = '-';
    }

    // Include provision balance when present
    if (provisionBalance != null) {
      map['Solde provision'] = provisionBalance!.toStringAsFixed(2);
    }

    return map;
  }

  /// Create from JSON data
  factory AmortizationRow.fromJson(Map<String, dynamic> json) {
    return AmortizationRow(
      year: json['year'] as int,
      yearLabel: json['yearLabel'] as String,
      baseAmount: (json['baseAmount'] as num).toDouble(),
      rate: (json['rate'] as num).toDouble(),
      annuity: (json['annuity'] as num).toDouble(),
      cumulativeAnnuity: (json['cumulativeAnnuity'] as num).toDouble(),
      netBookValue: (json['netBookValue'] as num).toDouble(),
      degressiveRate: json['degressiveRate'] != null
          ? (json['degressiveRate'] as num).toDouble()
          : null,
      linearRate: json['linearRate'] != null
          ? (json['linearRate'] as num).toDouble()
          : null,
      isLinearSwitchYear: json['isLinearSwitchYear'] as bool? ?? false,
      accountingAmortization: json['accountingAmortization'] != null
          ? (json['accountingAmortization'] as num).toDouble()
          : null,
      fiscalAmortization: json['fiscalAmortization'] != null
          ? (json['fiscalAmortization'] as num).toDouble()
          : null,
      derogationProvision: json['derogationProvision'] != null
          ? (json['derogationProvision'] as num).toDouble()
          : null,
      derogationReprise: json['derogationReprise'] != null
          ? (json['derogationReprise'] as num).toDouble()
          : null,
      provisionBalance: json['provisionBalance'] != null
          ? (json['provisionBalance'] as num).toDouble()
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'year': year,
      'yearLabel': yearLabel,
      'baseAmount': baseAmount,
      'rate': rate,
      'annuity': annuity,
      'cumulativeAnnuity': cumulativeAnnuity,
      'netBookValue': netBookValue,
      'degressiveRate': degressiveRate,
      'linearRate': linearRate,
      'isLinearSwitchYear': isLinearSwitchYear,
      'accountingAmortization': accountingAmortization,
      'fiscalAmortization': fiscalAmortization,
      'derogationProvision': derogationProvision,
      'derogationReprise': derogationReprise,
      'provisionBalance': provisionBalance,
    };
  }

  /// Check if this is a partial year (contains months in label)
  bool get isPartialYear {
    final hasExplicitMonths = RegExp(r'\(\s*\d+\s*mois\s*\)', caseSensitive: false).hasMatch(yearLabel);
    final containsMois = yearLabel.toLowerCase().contains('mois');
    return hasExplicitMonths || containsMois;
  }

  /// Extract calendar year (e.g., 2025 from "2025 (5 mois)")
  int? get calendarYear {
    final match = RegExp(r'(\d{4})').firstMatch(yearLabel);
    if (match != null) {
      return int.tryParse(match.group(1)!);
    }
    return null;
  }

  /// Extract months in year from label like "2025 (5 mois)" or return 12 for full year
  int get monthsInYear {
    final match = RegExp(r'(\d+)\s*mois', caseSensitive: false).firstMatch(yearLabel);
    if (match != null) {
      final parsed = int.tryParse(match.group(1)!);
      if (parsed != null && parsed > 0 && parsed <= 12) return parsed;
    }
    return 12;
  }

  /// Check if this row has derogatory amounts or a provision balance
  bool get hasDerogation {
    return accountingAmortization != null ||
        fiscalAmortization != null ||
        derogationProvision != null ||
        derogationReprise != null ||
        (provisionBalance != null && provisionBalance != 0);
  }

  /// Net derogatory movement (positive means provision, negative means reprise)
  double get derogatoryMovement {
    final provision = derogationProvision ?? 0.0;
    final reprise = derogationReprise ?? 0.0;
    return provision - reprise;
  }

  /// Whether this year is a provision year (dotation)
  bool get isProvisionYear {
    return (derogationProvision ?? 0) > 0;
  }

  /// Whether this year is a reprise year
  bool get isRepriseYear {
    return (derogationReprise ?? 0) > 0;
  }

  /// Convert a list of Map to List of AmortizationRow
  static List<AmortizationRow> fromTableList(List<Map<String, String>> tables) {
    return tables.map((table) => AmortizationRow.fromTableMap(table)).toList();
  }

  /// Convert a list of AmortizationRow back to List of Map
  static List<Map<String, String>> toTableList(List<AmortizationRow> rows) {
    return rows.map((row) => row.toTableMap()).toList();
  }

  /// Create a copy with modified values
  AmortizationRow copyWith({
    int? year,
    String? yearLabel,
    double? baseAmount,
    double? rate,
    double? annuity,
    double? cumulativeAnnuity,
    double? netBookValue,
    double? degressiveRate,
    double? linearRate,
    bool? isLinearSwitchYear,
    double? accountingAmortization,
    double? fiscalAmortization,
    double? derogationProvision,
    double? derogationReprise,
    double? provisionBalance,
  }) {
    return AmortizationRow(
      year: year ?? this.year,
      yearLabel: yearLabel ?? this.yearLabel,
      baseAmount: baseAmount ?? this.baseAmount,
      rate: rate ?? this.rate,
      annuity: annuity ?? this.annuity,
      cumulativeAnnuity: cumulativeAnnuity ?? this.cumulativeAnnuity,
      netBookValue: netBookValue ?? this.netBookValue,
      degressiveRate: degressiveRate ?? this.degressiveRate,
      linearRate: linearRate ?? this.linearRate,
      isLinearSwitchYear: isLinearSwitchYear ?? this.isLinearSwitchYear,
      accountingAmortization: accountingAmortization ?? this.accountingAmortization,
      fiscalAmortization: fiscalAmortization ?? this.fiscalAmortization,
      derogationProvision: derogationProvision ?? this.derogationProvision,
      derogationReprise: derogationReprise ?? this.derogationReprise,
      provisionBalance: provisionBalance ?? this.provisionBalance,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AmortizationRow &&
        other.year == year &&
        other.yearLabel == yearLabel &&
        other.baseAmount == baseAmount &&
        other.rate == rate &&
        other.annuity == annuity &&
        other.cumulativeAnnuity == cumulativeAnnuity &&
        other.netBookValue == netBookValue &&
        other.degressiveRate == degressiveRate &&
        other.linearRate == linearRate &&
        other.isLinearSwitchYear == isLinearSwitchYear &&
        other.accountingAmortization == accountingAmortization &&
        other.fiscalAmortization == fiscalAmortization &&
        other.derogationProvision == derogationProvision &&
        other.derogationReprise == derogationReprise &&
        other.provisionBalance == provisionBalance;
  }

  @override
  int get hashCode {
    return Object.hash(
      year,
      yearLabel,
      baseAmount,
      rate,
      annuity,
      cumulativeAnnuity,
      netBookValue,
      degressiveRate,
      linearRate,
      isLinearSwitchYear,
      accountingAmortization,
      fiscalAmortization,
      derogationProvision,
      derogationReprise,
      provisionBalance,
    );
  }

  @override
  String toString() {
    return 'AmortizationRow(year: $year, yearLabel: $yearLabel, '
        'baseAmount: $baseAmount, rate: $rate, annuity: $annuity, '
        'cumulativeAnnuity: $cumulativeAnnuity, netBookValue: $netBookValue, '
        'degressiveRate: $degressiveRate, linearRate: $linearRate, '
        'isLinearSwitchYear: $isLinearSwitchYear, '
        'accountingAmortization: $accountingAmortization, '
        'fiscalAmortization: $fiscalAmortization, '
        'derogationProvision: $derogationProvision, '
        'derogationReprise: $derogationReprise, '
        'provisionBalance: $provisionBalance)';
  }

  /// Generate a calendar year label for a given acquisition date and year index.
  /// yearIndex is 1-based (1 = first year). If monthsInYear < 12 it will return
  /// labels like "2025 (5 mois)" otherwise "2026".
  static String generateCalendarYearLabel(DateTime acquisitionDate, int yearIndex, int monthsInYear) {
    // Determine the calendar year for this yearIndex
    // The first year corresponds to acquisitionDate.year
    final yearOffset = yearIndex - 1;
    final labelYear = acquisitionDate.year + yearOffset;
    if (monthsInYear >= 12) {
      return '$labelYear';
    } else {
      return '$labelYear ($monthsInYear mois)';
    }
  }

  /// Calculate number of months in the specified yearIndex given acquisitionDate and totalYears.
  /// This method will compute prorata for the first year and distribute remaining months
  /// across subsequent years, returning up to 12 months for full years and possibly
  /// fewer for the last year.
  /// 
  /// Handles edge cases including:
  /// - December acquisitions (prorata temporis)
  /// - Leap years (February 29th acquisitions)
  /// - Invalid date ranges and parameters
  /// - Boundary conditions for year indices
  static int calculateMonthsInYear(DateTime acquisitionDate, int yearIndex, int totalYears) {
    // Comprehensive validation for edge cases
    
    // Validate year index bounds
    if (yearIndex < 1) {
      throw ArgumentError('yearIndex must be at least 1, got: $yearIndex');
    }
    
    // Validate acquisition date is not in the future (reasonable business rule)
    final now = DateTime.now();
    final maxFutureDate = DateTime(now.year + 1, 12, 31); // Allow 1 year in future for planning
    if (acquisitionDate.isAfter(maxFutureDate)) {
      throw ArgumentError('Acquisition date cannot be more than 1 year in the future: ${acquisitionDate.toIso8601String()}');
    }
    
    // Validate minimum reasonable acquisition date (1900 for historical assets)
    final minDate = DateTime(1900, 1, 1);
    if (acquisitionDate.isBefore(minDate)) {
      throw ArgumentError('Acquisition date cannot be before 1900: ${acquisitionDate.toIso8601String()}');
    }
    
    // Special handling for leap year February 29th acquisitions
    if (acquisitionDate.month == 2 && acquisitionDate.day == 29) {
      // For leap year acquisitions on Feb 29, ensure we handle non-leap years properly
      // This mainly affects multi-year calculations where subsequent years may not have Feb 29
      if (!_isLeapYear(acquisitionDate.year)) {
        throw ArgumentError('Invalid leap year date: February 29th in non-leap year ${acquisitionDate.year}');
      }
    }
    
    // Handle unknown or invalid totalYears
    if (totalYears <= 0) {
      if (totalYears < 0) {
        throw ArgumentError('totalYears cannot be negative, got: $totalYears');
      }
      
      // If totalYears is 0 or unknown, assume first year prorata then full years
      if (yearIndex == 1) {
        return _calculateFirstYearMonths(acquisitionDate);
      }
      return 12;
    }
    
    // Validate reasonable bounds for totalYears (business rule: max 100 years)
    if (totalYears > 100) {
      throw ArgumentError('totalYears cannot exceed 100 years for reasonable calculations, got: $totalYears');
    }
    
    // Handle December acquisitions specifically
    if (acquisitionDate.month == 12) {
      return _handleDecemberAcquisition(acquisitionDate, yearIndex, totalYears);
    }
    
    final totalMonths = totalYears * 12;
    final firstYearMonths = _calculateFirstYearMonths(acquisitionDate);
    
    // Validate that we have at least some months in the first year
    if (firstYearMonths <= 0) {
      throw StateError('First year months calculation resulted in non-positive value: $firstYearMonths');
    }
    
    // First year - return calculated prorata months
    if (yearIndex == 1) {
      return firstYearMonths.clamp(1, 12); // Ensure we return at least 1 month, max 12
    }
    
    // Calculate remaining months after first year
    var remainingMonths = totalMonths - firstYearMonths;
    if (remainingMonths <= 0) {
      // No more years needed - all amortization fits in first year
      return 0;
    }
    
    // For subsequent years
    final indexAmongRemaining = yearIndex - 2; // 0-based index for years after first
    
    // Validate that we haven't exceeded the amortization period
    if (indexAmongRemaining * 12 >= remainingMonths) {
      return 0; // This year is beyond the amortization period
    }
    
    final monthsForThisYear = remainingMonths - (indexAmongRemaining * 12);
    
    // Return either full year (12 months) or partial final year
    final result = (monthsForThisYear >= 12) ? 12 : monthsForThisYear;
    
    // Final validation - ensure result is within bounds
    if (result < 0) {
      throw StateError('Calculated months resulted in negative value: $result');
    }
    if (result > 12) {
      throw StateError('Calculated months exceeded 12: $result');
    }
    
    return result;
  }
  
  /// Helper method to calculate months in first year with proper prorata temporis
  static int _calculateFirstYearMonths(DateTime acquisitionDate) {
    // For prorata temporis: months from acquisition month to end of calendar year
    return 12 - acquisitionDate.month + 1;
  }
  
  /// Special handling for December acquisitions
  static int _handleDecemberAcquisition(DateTime acquisitionDate, int yearIndex, int totalYears) {
    if (yearIndex == 1) {
      // December acquisition means only 1 month in first year
      return 1;
    }
    
    // For subsequent years, follow normal distribution
    final totalMonths = totalYears * 12;
    final firstYearMonths = 1; // December acquisition = 1 month in first year
    final remainingMonths = totalMonths - firstYearMonths;
    
    if (remainingMonths <= 0) return 0;
    
    final indexAmongRemaining = yearIndex - 2;
    final monthsForThisYear = remainingMonths - (indexAmongRemaining * 12);
    
    return (monthsForThisYear >= 12) ? 12 : (monthsForThisYear > 0 ? monthsForThisYear : 0);
  }
  
  /// Helper method to check if a year is a leap year
  static bool _isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }

  /// Format row content for UI/display; returns a map of human-friendly strings.
  Map<String, String> formatForDisplay() {
    final display = <String, String>{};
    display['Année'] = yearLabel;
    display['Base'] = baseAmount.toStringAsFixed(2);
    display['Taux'] = '${rate.toStringAsFixed(2)} %';
    display['Annuités'] = annuity.toStringAsFixed(2);
    display['Annuités cumulées'] = cumulativeAnnuity.toStringAsFixed(2);
    display['VNC'] = netBookValue.toStringAsFixed(2);
    if (degressiveRate != null) {
      display['Taux dégressif'] = '${degressiveRate!.toStringAsFixed(2)} %';
    }
    if (linearRate != null) {
      display['Taux linéaire'] = '${linearRate!.toStringAsFixed(2)} %';
    }
    if (accountingAmortization != null) {
      display['Amortissement comptable'] = accountingAmortization!.toStringAsFixed(2);
    }
    if (fiscalAmortization != null) {
      display['Amortissement fiscal'] = fiscalAmortization!.toStringAsFixed(2);
    }
    if (derogationProvision != null && derogationProvision! > 0) {
      display['Dotations'] = derogationProvision!.toStringAsFixed(2);
    }
    if (derogationReprise != null && derogationReprise! > 0) {
      display['Reprises'] = derogationReprise!.toStringAsFixed(2);
    }
    if (provisionBalance != null) {
      display['Solde provision'] = provisionBalance!.toStringAsFixed(2);
    }
    return display;
  }
}