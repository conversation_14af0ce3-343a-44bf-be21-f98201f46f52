// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_depreciation_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnhancedDepreciationInput _$EnhancedDepreciationInputFromJson(
        Map<String, dynamic> json) =>
    EnhancedDepreciationInput(
      assetName: json['assetName'] as String,
      assetCost: (json['assetCost'] as num).toDouble(),
      residualValue: (json['residualValue'] as num).toDouble(),
      usefulLifeYears: (json['usefulLifeYears'] as num).toInt(),
      method: $enumDecode(_$DepreciationMethodEnumMap, json['method']),
      acquisitionDate: DateTime.parse(json['acquisitionDate'] as String),
      degressiveCoefficient:
          (json['degressiveCoefficient'] as num?)?.toDouble(),
      totalUnits: (json['totalUnits'] as num?)?.toDouble(),
      annualUnitsProduced: (json['annualUnitsProduced'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      midYearConvention: json['midYearConvention'] as bool? ?? false,
      assetType: json['assetType'] as String? ?? '',
      fiscalMethod: $enumDecodeNullable(
          _$DepreciationMethodEnumMap, json['fiscalMethod']),
      fiscalDegressiveCoefficient:
          (json['fiscalDegressiveCoefficient'] as num?)?.toDouble(),
      enableDerogatoire: json['enableDerogatoire'] as bool? ?? false,
    );

Map<String, dynamic> _$EnhancedDepreciationInputToJson(
        EnhancedDepreciationInput instance) =>
    <String, dynamic>{
      'assetName': instance.assetName,
      'assetCost': instance.assetCost,
      'residualValue': instance.residualValue,
      'usefulLifeYears': instance.usefulLifeYears,
      'method': _$DepreciationMethodEnumMap[instance.method]!,
      'acquisitionDate': instance.acquisitionDate.toIso8601String(),
      'degressiveCoefficient': instance.degressiveCoefficient,
      'totalUnits': instance.totalUnits,
      'annualUnitsProduced': instance.annualUnitsProduced,
      'midYearConvention': instance.midYearConvention,
      'assetType': instance.assetType,
      'fiscalMethod': _$DepreciationMethodEnumMap[instance.fiscalMethod],
      'fiscalDegressiveCoefficient': instance.fiscalDegressiveCoefficient,
      'enableDerogatoire': instance.enableDerogatoire,
    };

const _$DepreciationMethodEnumMap = {
  DepreciationMethod.linear: 'linear',
  DepreciationMethod.degressive: 'degressive',
  DepreciationMethod.sumOfYearsDigits: 'sumOfYearsDigits',
  DepreciationMethod.unitsOfProduction: 'unitsOfProduction',
};

DerogatoryCalculationResult _$DerogatoryCalculationResultFromJson(
        Map<String, dynamic> json) =>
    DerogatoryCalculationResult(
      accountingRows: (json['accountingRows'] as List<dynamic>)
          .map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
          .toList(),
      fiscalRows: (json['fiscalRows'] as List<dynamic>)
          .map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
          .toList(),
      derogatoryRows: (json['derogatoryRows'] as List<dynamic>)
          .map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalProvisionMovement:
          (json['totalProvisionMovement'] as num).toDouble(),
      finalProvisionBalance: (json['finalProvisionBalance'] as num).toDouble(),
      hasProvisions: json['hasProvisions'] as bool,
      hasReprises: json['hasReprises'] as bool,
      calculationMetadata: json['calculationMetadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$DerogatoryCalculationResultToJson(
        DerogatoryCalculationResult instance) =>
    <String, dynamic>{
      'accountingRows': instance.accountingRows,
      'fiscalRows': instance.fiscalRows,
      'derogatoryRows': instance.derogatoryRows,
      'totalProvisionMovement': instance.totalProvisionMovement,
      'finalProvisionBalance': instance.finalProvisionBalance,
      'hasProvisions': instance.hasProvisions,
      'hasReprises': instance.hasReprises,
      'calculationMetadata': instance.calculationMetadata,
    };

EnhancedDepreciationResult _$EnhancedDepreciationResultFromJson(
        Map<String, dynamic> json) =>
    EnhancedDepreciationResult(
      amortizationTable: (json['amortizationTable'] as List<dynamic>)
          .map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary:
          DepreciationSummary.fromJson(json['summary'] as Map<String, dynamic>),
      methodComparisons: (json['methodComparisons'] as List<dynamic>?)
          ?.map(
              (e) => DepreciationComparison.fromJson(e as Map<String, dynamic>))
          .toList(),
      taxAdvice: json['taxAdvice'] == null
          ? null
          : TaxOptimizationAdvice.fromJson(
              json['taxAdvice'] as Map<String, dynamic>),
      calculationContext: json['calculationContext'] == null
          ? null
          : CalculationContext.fromJson(
              json['calculationContext'] as Map<String, dynamic>),
      calculationWarnings: (json['calculationWarnings'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      journalEntries: _journalEntriesFromJson(json['journalEntries']),
      accountingAmortizationTable:
          (json['accountingAmortizationTable'] as List<dynamic>?)
              ?.map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
              .toList(),
      fiscalAmortizationTable:
          (json['fiscalAmortizationTable'] as List<dynamic>?)
              ?.map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
              .toList(),
      derogatoryAmortizationTable:
          (json['derogatoryAmortizationTable'] as List<dynamic>?)
              ?.map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$EnhancedDepreciationResultToJson(
        EnhancedDepreciationResult instance) =>
    <String, dynamic>{
      'amortizationTable': instance.amortizationTable,
      'summary': instance.summary,
      'methodComparisons': instance.methodComparisons,
      'taxAdvice': instance.taxAdvice,
      'calculationContext': instance.calculationContext,
      'calculationWarnings': instance.calculationWarnings,
      'journalEntries': _journalEntriesToJson(instance.journalEntries),
      'accountingAmortizationTable': instance.accountingAmortizationTable,
      'fiscalAmortizationTable': instance.fiscalAmortizationTable,
      'derogatoryAmortizationTable': instance.derogatoryAmortizationTable,
    };

CalculationContext _$CalculationContextFromJson(Map<String, dynamic> json) =>
    CalculationContext(
      calculationDate: DateTime.parse(json['calculationDate'] as String),
      calculationVersion: json['calculationVersion'] as String,
      calculationDuration:
          Duration(microseconds: (json['calculationDuration'] as num).toInt()),
      iterationsPerformed: (json['iterationsPerformed'] as num).toInt(),
      converged: json['converged'] as bool,
      finalPrecision: (json['finalPrecision'] as num).toDouble(),
      debugInfo: json['debugInfo'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CalculationContextToJson(CalculationContext instance) =>
    <String, dynamic>{
      'calculationDate': instance.calculationDate.toIso8601String(),
      'calculationVersion': instance.calculationVersion,
      'calculationDuration': instance.calculationDuration.inMicroseconds,
      'iterationsPerformed': instance.iterationsPerformed,
      'converged': instance.converged,
      'finalPrecision': instance.finalPrecision,
      'debugInfo': instance.debugInfo,
    };

DepreciationSummary _$DepreciationSummaryFromJson(Map<String, dynamic> json) =>
    DepreciationSummary(
      totalDepreciation: (json['totalDepreciation'] as num).toDouble(),
      remainingValue: (json['remainingValue'] as num).toDouble(),
      averageAnnualDepreciation:
          (json['averageAnnualDepreciation'] as num).toDouble(),
      totalYears: (json['totalYears'] as num).toInt(),
      method: $enumDecode(_$DepreciationMethodEnumMap, json['method']),
      firstYearDepreciation: (json['firstYearDepreciation'] as num).toDouble(),
      lastYearDepreciation: (json['lastYearDepreciation'] as num).toDouble(),
    );

Map<String, dynamic> _$DepreciationSummaryToJson(
        DepreciationSummary instance) =>
    <String, dynamic>{
      'totalDepreciation': instance.totalDepreciation,
      'remainingValue': instance.remainingValue,
      'averageAnnualDepreciation': instance.averageAnnualDepreciation,
      'totalYears': instance.totalYears,
      'method': _$DepreciationMethodEnumMap[instance.method]!,
      'firstYearDepreciation': instance.firstYearDepreciation,
      'lastYearDepreciation': instance.lastYearDepreciation,
    };

DepreciationComparison _$DepreciationComparisonFromJson(
        Map<String, dynamic> json) =>
    DepreciationComparison(
      method: $enumDecode(_$DepreciationMethodEnumMap, json['method']),
      totalDepreciation: (json['totalDepreciation'] as num).toDouble(),
      firstYearDepreciation: (json['firstYearDepreciation'] as num).toDouble(),
      netPresentValue: (json['netPresentValue'] as num).toDouble(),
      taxBenefit: (json['taxBenefit'] as num).toDouble(),
      recommendation: json['recommendation'] as String,
      calculationSuccessful: json['calculationSuccessful'] as bool? ?? true,
      calculationError: json['calculationError'] as String?,
    );

Map<String, dynamic> _$DepreciationComparisonToJson(
        DepreciationComparison instance) =>
    <String, dynamic>{
      'method': _$DepreciationMethodEnumMap[instance.method]!,
      'totalDepreciation': instance.totalDepreciation,
      'firstYearDepreciation': instance.firstYearDepreciation,
      'netPresentValue': instance.netPresentValue,
      'taxBenefit': instance.taxBenefit,
      'recommendation': instance.recommendation,
      'calculationSuccessful': instance.calculationSuccessful,
      'calculationError': instance.calculationError,
    };

TaxOptimizationAdvice _$TaxOptimizationAdviceFromJson(
        Map<String, dynamic> json) =>
    TaxOptimizationAdvice(
      recommendedMethod: json['recommendedMethod'] as String,
      estimatedTaxSavings: (json['estimatedTaxSavings'] as num).toDouble(),
      reasoning: json['reasoning'] as String,
      considerations: (json['considerations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      implementationTiming: json['implementationTiming'] as String,
    );

Map<String, dynamic> _$TaxOptimizationAdviceToJson(
        TaxOptimizationAdvice instance) =>
    <String, dynamic>{
      'recommendedMethod': instance.recommendedMethod,
      'estimatedTaxSavings': instance.estimatedTaxSavings,
      'reasoning': instance.reasoning,
      'considerations': instance.considerations,
      'implementationTiming': instance.implementationTiming,
    };

AssetPreset _$AssetPresetFromJson(Map<String, dynamic> json) => AssetPreset(
      name: json['name'] as String,
      category: json['category'] as String,
      defaultUsefulLife: (json['defaultUsefulLife'] as num).toInt(),
      defaultResidualValuePercent:
          (json['defaultResidualValuePercent'] as num).toDouble(),
      recommendedMethod:
          $enumDecode(_$DepreciationMethodEnumMap, json['recommendedMethod']),
      description: json['description'] as String,
      defaultDegressiveCoefficient:
          (json['defaultDegressiveCoefficient'] as num?)?.toDouble(),
      estimatedTotalUnits: (json['estimatedTotalUnits'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$AssetPresetToJson(AssetPreset instance) =>
    <String, dynamic>{
      'name': instance.name,
      'category': instance.category,
      'defaultUsefulLife': instance.defaultUsefulLife,
      'defaultResidualValuePercent': instance.defaultResidualValuePercent,
      'recommendedMethod':
          _$DepreciationMethodEnumMap[instance.recommendedMethod]!,
      'description': instance.description,
      'defaultDegressiveCoefficient': instance.defaultDegressiveCoefficient,
      'estimatedTotalUnits': instance.estimatedTotalUnits,
    };
