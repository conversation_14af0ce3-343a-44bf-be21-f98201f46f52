import 'package:json_annotation/json_annotation.dart';
import '../immobilisations/amortization_row.dart';
import 'dart:math' as math;
import '../../widgets/journal_comptable_widget.dart';

part 'enhanced_depreciation_data.g.dart';

// Note: The following methods will be generated by json_annotation
// _$DerogatoryCalculationResultFromJson and _$DerogatoryCalculationResultToJson
// These are referenced in the DerogatoryCalculationResult class above

/// Calculation limits to prevent extreme values
class CalculationLimits {
  static const double maxAssetCost = 1000000000.0; // 1 billion
  static const double minAssetCost = 0.01; // 1 centime
  static const int maxUsefulLife = 100; // 100 years
  static const int minUsefulLife = 1; // 1 year
  static const double maxDegressiveCoefficient = 10.0;
  static const double minDegressiveCoefficient = 1.01;
  static const double maxTotalUnits = 1000000000.0; // 1 billion units
  static const double minTotalUnits = 1.0;
  static const double maxAnnualUnits = 100000000.0; // 100 million units per year
  static const double floatingPointEpsilon = 1e-10;
}

/// Utility class for safe floating-point operations
class FloatingPointUtils {
  /// Safe comparison of two double values with epsilon tolerance
  static bool areEqual(double a, double b, {double epsilon = CalculationLimits.floatingPointEpsilon}) {
    return (a - b).abs() < epsilon;
  }

  /// Check if a value is effectively zero
  static bool isZero(double value, {double epsilon = CalculationLimits.floatingPointEpsilon}) {
    return value.abs() < epsilon;
  }

  /// Check if a value is positive (greater than epsilon)
  static bool isPositive(double value, {double epsilon = CalculationLimits.floatingPointEpsilon}) {
    return value > epsilon;
  }

  /// Safe division that handles division by zero
  static double safeDivide(double numerator, double denominator, {double fallback = 0.0}) {
    if (isZero(denominator)) {
      return fallback;
    }
    return numerator / denominator;
  }

  /// Round to specified decimal places to avoid floating-point precision issues
  static double roundToPrecision(double value, int decimalPlaces) {
    final factor = math.pow(10, decimalPlaces);
    return (value * factor).round() / factor;
  }

  /// Check if a value is within reasonable bounds for financial calculations
  static bool isReasonableFinancialValue(double value) {
    return value.isFinite && 
           !value.isNaN && 
           value >= -CalculationLimits.maxAssetCost && 
           value <= CalculationLimits.maxAssetCost;
  }
}

/// Validation result with specific error messages
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  ValidationResult.valid() : this(isValid: true);

  ValidationResult.invalid(List<String> errors, {List<String> warnings = const []})
      : this(isValid: false, errors: errors, warnings: warnings);

  String get primaryError => errors.isNotEmpty ? errors.first : '';
  
  String get allErrors => errors.join('\n');
  
  String get allWarnings => warnings.join('\n');

  bool get hasWarnings => warnings.isNotEmpty;
}

@JsonSerializable()
class EnhancedDepreciationInput {
  final String assetName;
  final double assetCost;
  final double residualValue;
  final int usefulLifeYears;
  final DepreciationMethod method;
  final DateTime acquisitionDate;
  final double? degressiveCoefficient;
  final double? totalUnits;
  final List<double>? annualUnitsProduced;
  final bool midYearConvention;

  /// New field to support accounting mapping based on asset type/category
  final String assetType;

  /// Fiscal method for derogatory calculations (when different from accounting method)
  final DepreciationMethod? fiscalMethod;

  /// Fiscal degressive coefficient (when fiscal method is degressive)
  final double? fiscalDegressiveCoefficient;

  /// Enable derogatory amortization calculations
  final bool enableDerogatoire;

  const EnhancedDepreciationInput({
    required this.assetName,
    required this.assetCost,
    required this.residualValue,
    required this.usefulLifeYears,
    required this.method,
    required this.acquisitionDate,
    this.degressiveCoefficient,
    this.totalUnits,
    this.annualUnitsProduced,
    this.midYearConvention = false,
    this.assetType = '',
    this.fiscalMethod,
    this.fiscalDegressiveCoefficient,
    this.enableDerogatoire = false,
  });

  factory EnhancedDepreciationInput.fromJson(Map<String, dynamic> json) =>
      _$EnhancedDepreciationInputFromJson(json);

  Map<String, dynamic> toJson() => _$EnhancedDepreciationInputToJson(this);

  EnhancedDepreciationInput copyWith({
    String? assetName,
    double? assetCost,
    double? residualValue,
    int? usefulLifeYears,
    DepreciationMethod? method,
    DateTime? acquisitionDate,
    double? degressiveCoefficient,
    double? totalUnits,
    List<double>? annualUnitsProduced,
    bool? midYearConvention,
    String? assetType,
    DepreciationMethod? fiscalMethod,
    double? fiscalDegressiveCoefficient,
    bool? enableDerogatoire,
  }) {
    return EnhancedDepreciationInput(
      assetName: assetName ?? this.assetName,
      assetCost: assetCost ?? this.assetCost,
      residualValue: residualValue ?? this.residualValue,
      usefulLifeYears: usefulLifeYears ?? this.usefulLifeYears,
      method: method ?? this.method,
      acquisitionDate: acquisitionDate ?? this.acquisitionDate,
      degressiveCoefficient: degressiveCoefficient ?? this.degressiveCoefficient,
      totalUnits: totalUnits ?? this.totalUnits,
      annualUnitsProduced: annualUnitsProduced ?? this.annualUnitsProduced,
      midYearConvention: midYearConvention ?? this.midYearConvention,
      assetType: assetType ?? this.assetType,
      fiscalMethod: fiscalMethod ?? this.fiscalMethod,
      fiscalDegressiveCoefficient: fiscalDegressiveCoefficient ?? this.fiscalDegressiveCoefficient,
      enableDerogatoire: enableDerogatoire ?? this.enableDerogatoire,
    );
  }

  double get depreciableAmount => assetCost - residualValue;

  /// Enhanced validation with comprehensive checks
  bool get isValid => validate().isValid;

  /// Comprehensive validation with detailed error messages
  ValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    // Asset name validation
    if (assetName.trim().isEmpty) {
      errors.add('Le nom de l\'immobilisation ne peut pas être vide');
    } else if (assetName.trim().length < 2) {
      warnings.add('Le nom de l\'immobilisation est très court');
    }

    // Asset cost validation
    if (!FloatingPointUtils.isReasonableFinancialValue(assetCost)) {
      errors.add('Le coût de l\'immobilisation contient une valeur invalide');
    } else if (assetCost < CalculationLimits.minAssetCost) {
      errors.add('Le coût de l\'immobilisation doit être d\'au moins ${CalculationLimits.minAssetCost} DH');
    } else if (assetCost > CalculationLimits.maxAssetCost) {
      errors.add('Le coût de l\'immobilisation ne peut pas dépasser ${CalculationLimits.maxAssetCost} DH');
    }

    // Residual value validation
    if (!FloatingPointUtils.isReasonableFinancialValue(residualValue)) {
      errors.add('La valeur résiduelle contient une valeur invalide');
    } else if (residualValue < 0) {
      errors.add('La valeur résiduelle ne peut pas être négative');
    } else if (residualValue >= assetCost) {
      errors.add('La valeur résiduelle doit être inférieure au coût de l\'immobilisation');
    } else if (residualValue > assetCost * 0.9) {
      warnings.add('La valeur résiduelle est très élevée (>90% du coût)');
    }

    // Useful life validation
    if (usefulLifeYears < CalculationLimits.minUsefulLife) {
      errors.add('La durée de vie utile doit être d\'au moins ${CalculationLimits.minUsefulLife} an');
    } else if (usefulLifeYears > CalculationLimits.maxUsefulLife) {
      errors.add('La durée de vie utile ne peut pas dépasser ${CalculationLimits.maxUsefulLife} ans');
    } else if (usefulLifeYears > 50) {
      warnings.add('La durée de vie utile est très longue (>50 ans)');
    }

    // Acquisition date validation
    final now = DateTime.now();
    final maxFutureDate = DateTime(now.year + 1, 12, 31);
    final minPastDate = DateTime(1900, 1, 1);
    
    if (acquisitionDate.isAfter(maxFutureDate)) {
      errors.add('La date d\'acquisition ne peut pas être dans le futur lointain');
    } else if (acquisitionDate.isBefore(minPastDate)) {
      errors.add('La date d\'acquisition est trop ancienne');
    } else if (acquisitionDate.isAfter(now)) {
      warnings.add('La date d\'acquisition est dans le futur');
    }

    // Method-specific validation
    switch (method) {
      case DepreciationMethod.degressive:
        if (degressiveCoefficient == null) {
          errors.add('Le coefficient dégressif est requis pour la méthode dégressive');
        } else if (!FloatingPointUtils.isReasonableFinancialValue(degressiveCoefficient!)) {
          errors.add('Le coefficient dégressif contient une valeur invalide');
        } else if (degressiveCoefficient! < CalculationLimits.minDegressiveCoefficient) {
          errors.add('Le coefficient dégressif doit être supérieur à ${CalculationLimits.minDegressiveCoefficient}');
        } else if (degressiveCoefficient! > CalculationLimits.maxDegressiveCoefficient) {
          errors.add('Le coefficient dégressif ne peut pas dépasser ${CalculationLimits.maxDegressiveCoefficient}');
        } else if (degressiveCoefficient! > 5.0) {
          warnings.add('Le coefficient dégressif est très élevé (>${5.0})');
        }
        break;

      case DepreciationMethod.unitsOfProduction:
        if (totalUnits == null) {
          errors.add('Le nombre total d\'unités est requis pour la méthode des unités de production');
        } else if (!FloatingPointUtils.isReasonableFinancialValue(totalUnits!)) {
          errors.add('Le nombre total d\'unités contient une valeur invalide');
        } else if (totalUnits! < CalculationLimits.minTotalUnits) {
          errors.add('Le nombre total d\'unités doit être d\'au moins ${CalculationLimits.minTotalUnits}');
        } else if (totalUnits! > CalculationLimits.maxTotalUnits) {
          errors.add('Le nombre total d\'unités ne peut pas dépasser ${CalculationLimits.maxTotalUnits}');
        }

        // Validate annual units if provided
        if (annualUnitsProduced != null) {
          if (annualUnitsProduced!.isEmpty) {
            warnings.add('Aucune donnée de production annuelle fournie');
          } else {
            double totalAnnualUnits = 0;
            for (int i = 0; i < annualUnitsProduced!.length; i++) {
              final units = annualUnitsProduced![i];
              if (!FloatingPointUtils.isReasonableFinancialValue(units)) {
                errors.add('Les unités produites pour l\'année ${i + 1} contiennent une valeur invalide');
              } else if (units < 0) {
                errors.add('Les unités produites pour l\'année ${i + 1} ne peuvent pas être négatives');
              } else if (units > CalculationLimits.maxAnnualUnits) {
                errors.add('Les unités produites pour l\'année ${i + 1} dépassent la limite maximale');
              }
              totalAnnualUnits += units;
            }

            if (totalUnits != null && totalAnnualUnits > totalUnits! * 1.1) {
              warnings.add('La production annuelle totale dépasse le nombre total d\'unités prévu');
            }

            if (annualUnitsProduced!.length > usefulLifeYears) {
              warnings.add('Plus de données de production que d\'années de vie utile');
            }
          }
        }
        break;

      case DepreciationMethod.sumOfYearsDigits:
        // Check for potential overflow in sum of years calculation
        final sumOfYears = usefulLifeYears * (usefulLifeYears + 1) / 2;
        if (sumOfYears > 10000) {
          warnings.add('La durée de vie utile est très longue pour la méthode somme des chiffres des années');
        }
        break;

      case DepreciationMethod.linear:
        // No specific validation needed for linear method
        break;
    }

    // Fiscal method validation when derogatory calculation is enabled
    if (enableDerogatoire) {
      if (fiscalMethod == null) {
        errors.add('La méthode fiscale est requise quand l\'amortissement dérogatoire est activé');
      } else if (fiscalMethod == method) {
        warnings.add('La méthode fiscale est identique à la méthode comptable - aucun amortissement dérogatoire ne sera généré');
      } else {
        // Validate fiscal method parameters
        switch (fiscalMethod!) {
          case DepreciationMethod.degressive:
            if (fiscalDegressiveCoefficient == null) {
              errors.add('Le coefficient dégressif fiscal est requis pour la méthode dégressive fiscale');
            } else if (!FloatingPointUtils.isReasonableFinancialValue(fiscalDegressiveCoefficient!)) {
              errors.add('Le coefficient dégressif fiscal contient une valeur invalide');
            } else if (fiscalDegressiveCoefficient! < CalculationLimits.minDegressiveCoefficient) {
              errors.add('Le coefficient dégressif fiscal doit être supérieur à ${CalculationLimits.minDegressiveCoefficient}');
            } else if (fiscalDegressiveCoefficient! > CalculationLimits.maxDegressiveCoefficient) {
              errors.add('Le coefficient dégressif fiscal ne peut pas dépasser ${CalculationLimits.maxDegressiveCoefficient}');
            }
            break;
          case DepreciationMethod.unitsOfProduction:
            if (totalUnits == null) {
              errors.add('Le nombre total d\'unités est requis pour la méthode fiscale des unités de production');
            }
            break;
          default:
            // Linear and sum of years digits don't need additional parameters
            break;
        }

        // Cross-validation between accounting and fiscal methods
        if (method == DepreciationMethod.unitsOfProduction && fiscalMethod != DepreciationMethod.unitsOfProduction) {
          if (totalUnits == null) {
            errors.add('Le nombre total d\'unités est requis pour la méthode comptable des unités de production');
          }
        }
      }
    } else {
      // When derogatory is disabled, fiscal parameters should not be set
      if (fiscalMethod != null) {
        warnings.add('La méthode fiscale est définie mais l\'amortissement dérogatoire n\'est pas activé');
      }
      if (fiscalDegressiveCoefficient != null) {
        warnings.add('Le coefficient dégressif fiscal est défini mais l\'amortissement dérogatoire n\'est pas activé');
      }
    }

    // Cross-validation checks
    if (errors.isEmpty) {
      // Check if depreciable amount is reasonable
      final depreciableAmount = this.depreciableAmount;
      if (FloatingPointUtils.isZero(depreciableAmount)) {
        warnings.add('Le montant amortissable est nul (coût = valeur résiduelle)');
      } else if (depreciableAmount < 0) {
        errors.add('Le montant amortissable ne peut pas être négatif');
      }

      // Check for potential calculation overflow
      final maxAnnualDepreciation = depreciableAmount;
      if (maxAnnualDepreciation > CalculationLimits.maxAssetCost / 2) {
        warnings.add('L\'amortissement annuel pourrait être très élevé');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Get validation errors as a formatted string
  String get validationErrors => validate().allErrors;

  /// Get validation warnings as a formatted string  
  String get validationWarnings => validate().allWarnings;

  /// Check if the input has any validation warnings
  bool get hasValidationWarnings => validate().hasWarnings;
}

/// Helper functions to convert JournalEntry objects to/from JSON-compatible maps.
/// JournalEntry and JournalLine are defined in widgets/journal_comptable_widget.dart.
/// These converters are used with JsonSerializable to serialize the journal entries
List<JournalEntry>? _journalEntriesFromJson(dynamic json) {
  if (json == null) return null;
  if (json is! List) return null;
  return json.map<JournalEntry?>((e) {
    if (e == null) return null;
    final map = Map<String, dynamic>.from(e as Map);
    final date = map['date'] as String?;
    final groupLabel = map['groupLabel'] as String?;
    final showSubtotal = map['showSubtotal'] as bool? ?? false;
    final linesRaw = map['lines'] as List<dynamic>? ?? [];
    final lines = linesRaw.map<JournalLine>((lr) {
      final lm = Map<String, dynamic>.from(lr as Map);
      return JournalLine(
        account: lm['account']?.toString() ?? '',
        label: lm['label']?.toString() ?? '',
        debit: lm['debit']?.toString(),
        credit: lm['credit']?.toString(),
        isHighlighted: lm['isHighlighted'] as bool? ?? false,
        isBold: lm['isBold'] as bool? ?? false,
      );
    }).toList();
    return JournalEntry(
      date: date,
      lines: lines,
      groupLabel: groupLabel,
      showSubtotal: showSubtotal,
    );
  }).whereType<JournalEntry>().toList();
}

dynamic _journalEntriesToJson(List<JournalEntry>? entries) {
  if (entries == null) return null;
  return entries.map((e) {
    return {
      'date': e.date,
      'groupLabel': e.groupLabel,
      'showSubtotal': e.showSubtotal,
      'lines': e.lines.map((l) {
        return {
          'account': l.account,
          'label': l.label,
          'debit': l.debit,
          'credit': l.credit,
          'isHighlighted': l.isHighlighted,
          'isBold': l.isBold,
        };
      }).toList(),
    };
  }).toList();
}

/// Helper class for derogatory calculation results
@JsonSerializable()
class DerogatoryCalculationResult {
  final List<AmortizationRow> accountingRows;
  final List<AmortizationRow> fiscalRows;
  final List<AmortizationRow> derogatoryRows;
  final double totalProvisionMovement;
  final double finalProvisionBalance;
  final bool hasProvisions;
  final bool hasReprises;
  final Map<String, dynamic>? calculationMetadata;

  const DerogatoryCalculationResult({
    required this.accountingRows,
    required this.fiscalRows,
    required this.derogatoryRows,
    required this.totalProvisionMovement,
    required this.finalProvisionBalance,
    required this.hasProvisions,
    required this.hasReprises,
    this.calculationMetadata,
  });

  factory DerogatoryCalculationResult.fromJson(Map<String, dynamic> json) =>
      _$DerogatoryCalculationResultFromJson(json);

  Map<String, dynamic> toJson() => _$DerogatoryCalculationResultToJson(this);

  /// Validate mathematical consistency between tables
  ValidationResult validateConsistency() {
    final errors = <String>[];
    final warnings = <String>[];

    if (accountingRows.length != fiscalRows.length) {
      errors.add('Les tableaux comptable et fiscal doivent avoir le même nombre de lignes');
    }

    if (derogatoryRows.length != accountingRows.length) {
      errors.add('Le tableau dérogatoire doit avoir le même nombre de lignes que les autres tableaux');
    }

    // Check year-by-year consistency
    for (int i = 0; i < accountingRows.length && i < fiscalRows.length && i < derogatoryRows.length; i++) {
      final accounting = accountingRows[i];
      final fiscal = fiscalRows[i];
      final derogatory = derogatoryRows[i];

      if (accounting.year != fiscal.year || accounting.year != derogatory.year) {
        errors.add('Les années ne correspondent pas à la ligne ${i + 1}');
      }

      // Check derogatory calculation
      final expectedDerogatory = fiscal.annuity - accounting.annuity;
      final actualDerogatory = derogatory.derogatoryMovement;
      
      if (!FloatingPointUtils.areEqual(expectedDerogatory, actualDerogatory, epsilon: 0.01)) {
        warnings.add('Différence dérogatoire incohérente à l\'année ${accounting.year}: attendu ${expectedDerogatory.toStringAsFixed(2)}, calculé ${actualDerogatory.toStringAsFixed(2)}');
      }
    }

    // Check provision balance consistency
    double runningBalance = 0.0;
    for (final row in derogatoryRows) {
      runningBalance += row.derogatoryMovement;
      if (row.provisionBalance != null && !FloatingPointUtils.areEqual(runningBalance, row.provisionBalance!, epsilon: 0.01)) {
        warnings.add('Solde de provision incohérent à l\'année ${row.year}: attendu ${runningBalance.toStringAsFixed(2)}, trouvé ${row.provisionBalance!.toStringAsFixed(2)}');
      }
    }

    if (!FloatingPointUtils.areEqual(runningBalance, finalProvisionBalance, epsilon: 0.01)) {
      errors.add('Le solde final de provision ne correspond pas au calcul cumulé');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Check if the derogatory calculation is mathematically sound
  bool get isConsistent => validateConsistency().isValid;

  /// Get summary of derogatory movements
  String get movementSummary {
    final provisions = derogatoryRows.where((r) => r.isProvisionYear).length;
    final reprises = derogatoryRows.where((r) => r.isRepriseYear).length;
    return 'Provisions: $provisions années, Reprises: $reprises années';
  }
}

@JsonSerializable()
class EnhancedDepreciationResult {
  final List<AmortizationRow> amortizationTable;
  final DepreciationSummary summary;
  final List<DepreciationComparison>? methodComparisons;
  final TaxOptimizationAdvice? taxAdvice;
  final CalculationContext? calculationContext;
  final List<String>? calculationWarnings;

  /// Optional generated accounting journal entries for the calculation
  @JsonKey(fromJson: _journalEntriesFromJson, toJson: _journalEntriesToJson)
  final List<JournalEntry>? journalEntries;

  /// Accounting amortization table (when derogatory calculation is used)
  final List<AmortizationRow>? accountingAmortizationTable;

  /// Fiscal amortization table (when derogatory calculation is used)
  final List<AmortizationRow>? fiscalAmortizationTable;

  /// Derogatory amortization table with provision movements
  final List<AmortizationRow>? derogatoryAmortizationTable;

  const EnhancedDepreciationResult({
    required this.amortizationTable,
    required this.summary,
    this.methodComparisons,
    this.taxAdvice,
    this.calculationContext,
    this.calculationWarnings,
    this.journalEntries,
    this.accountingAmortizationTable,
    this.fiscalAmortizationTable,
    this.derogatoryAmortizationTable,
  });

  factory EnhancedDepreciationResult.fromJson(Map<String, dynamic> json) =>
      _$EnhancedDepreciationResultFromJson(json);

  Map<String, dynamic> toJson() => _$EnhancedDepreciationResultToJson(this);

  /// Check if derogatory amortization tables are available
  bool get hasDerogatoire => derogatoryAmortizationTable != null && derogatoryAmortizationTable!.isNotEmpty;

  /// Check if multiple tables are available (accounting, fiscal, derogatory)
  bool get hasMultipleTables => hasDerogatoire;

  /// Check if the calculation had any warnings
  bool get hasWarnings => calculationWarnings?.isNotEmpty ?? false;

  /// Get formatted warnings string
  String get formattedWarnings => calculationWarnings?.join('\n') ?? '';

  /// Create a copy with additional warnings
  EnhancedDepreciationResult copyWithWarnings(List<String> additionalWarnings) {
    final allWarnings = <String>[
      ...(calculationWarnings ?? []),
      ...additionalWarnings,
    ];
    
    return EnhancedDepreciationResult(
      amortizationTable: amortizationTable,
      summary: summary,
      methodComparisons: methodComparisons,
      taxAdvice: taxAdvice,
      calculationContext: calculationContext,
      calculationWarnings: allWarnings,
      journalEntries: journalEntries,
      accountingAmortizationTable: accountingAmortizationTable,
      fiscalAmortizationTable: fiscalAmortizationTable,
      derogatoryAmortizationTable: derogatoryAmortizationTable,
    );
  }

  /// Validate the result for consistency when multiple tables are present
  ValidationResult validateMultipleTablesConsistency() {
    final errors = <String>[];
    final warnings = <String>[];

    if (hasMultipleTables) {
      if (accountingAmortizationTable == null) {
        errors.add('Le tableau comptable est requis quand l\'amortissement dérogatoire est présent');
      }
      if (fiscalAmortizationTable == null) {
        errors.add('Le tableau fiscal est requis quand l\'amortissement dérogatoire est présent');
      }
      if (derogatoryAmortizationTable == null) {
        errors.add('Le tableau dérogatoire ne peut pas être null quand hasDerogatoire est true');
      }

      // Check table lengths consistency
      if (accountingAmortizationTable != null && fiscalAmortizationTable != null) {
        if (accountingAmortizationTable!.length != fiscalAmortizationTable!.length) {
          errors.add('Les tableaux comptable et fiscal doivent avoir la même longueur');
        }
      }

      if (derogatoryAmortizationTable != null && accountingAmortizationTable != null) {
        if (derogatoryAmortizationTable!.length != accountingAmortizationTable!.length) {
          errors.add('Le tableau dérogatoire doit avoir la même longueur que le tableau comptable');
        }
      }

      // Validate that primary table matches accounting table when derogatory is present
      if (accountingAmortizationTable != null && amortizationTable.length != accountingAmortizationTable!.length) {
        warnings.add('Le tableau principal devrait correspondre au tableau comptable quand l\'amortissement dérogatoire est utilisé');
      }
    } else {
      // When no derogatory calculation, additional tables should be null
      if (accountingAmortizationTable != null) {
        warnings.add('Le tableau comptable est défini mais l\'amortissement dérogatoire n\'est pas activé');
      }
      if (fiscalAmortizationTable != null) {
        warnings.add('Le tableau fiscal est défini mais l\'amortissement dérogatoire n\'est pas activé');
      }
      if (derogatoryAmortizationTable != null) {
        warnings.add('Le tableau dérogatoire est défini mais hasDerogatoire retourne false');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Check if the result is mathematically consistent
  bool get isConsistent => validateMultipleTablesConsistency().isValid;
}

/// Context information about the calculation process
@JsonSerializable()
class CalculationContext {
  final DateTime calculationDate;
  final String calculationVersion;
  final Duration calculationDuration;
  final int iterationsPerformed;
  final bool converged;
  final double finalPrecision;
  final Map<String, dynamic>? debugInfo;

  const CalculationContext({
    required this.calculationDate,
    required this.calculationVersion,
    required this.calculationDuration,
    required this.iterationsPerformed,
    required this.converged,
    required this.finalPrecision,
    this.debugInfo,
  });

  factory CalculationContext.fromJson(Map<String, dynamic> json) {
    return CalculationContext(
      calculationDate: DateTime.parse(json['calculationDate'] as String),
      calculationVersion: json['calculationVersion'] as String,
      calculationDuration: Duration(microseconds: json['calculationDuration'] as int),
      iterationsPerformed: json['iterationsPerformed'] as int,
      converged: json['converged'] as bool,
      finalPrecision: (json['finalPrecision'] as num).toDouble(),
      debugInfo: json['debugInfo'] != null 
          ? Map<String, dynamic>.from(json['debugInfo'] as Map)
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'calculationDate': calculationDate.toIso8601String(),
        'calculationVersion': calculationVersion,
        'calculationDuration': calculationDuration.inMicroseconds,
        'iterationsPerformed': iterationsPerformed,
        'converged': converged,
        'finalPrecision': finalPrecision,
        'debugInfo': debugInfo,
      };

  /// Create a basic calculation context
  factory CalculationContext.basic({
    String version = '1.0.0',
    Duration? duration,
    int iterations = 0,
    bool converged = true,
    double precision = 0.01,
  }) {
    return CalculationContext(
      calculationDate: DateTime.now(),
      calculationVersion: version,
      calculationDuration: duration ?? Duration.zero,
      iterationsPerformed: iterations,
      converged: converged,
      finalPrecision: precision,
    );
  }

  /// Check if the calculation was successful
  bool get isSuccessful => converged && finalPrecision <= 0.01;

  /// Get performance summary
  String get performanceSummary {
    final ms = calculationDuration.inMilliseconds;
    return 'Calculé en ${ms}ms avec $iterationsPerformed itérations';
  }
}

@JsonSerializable()
class DepreciationSummary {
  final double totalDepreciation;
  final double remainingValue;
  final double averageAnnualDepreciation;
  final int totalYears;
  final DepreciationMethod method;
  final double firstYearDepreciation;
  final double lastYearDepreciation;

  const DepreciationSummary({
    required this.totalDepreciation,
    required this.remainingValue,
    required this.averageAnnualDepreciation,
    required this.totalYears,
    required this.method,
    required this.firstYearDepreciation,
    required this.lastYearDepreciation,
  });

  factory DepreciationSummary.fromJson(Map<String, dynamic> json) =>
      _$DepreciationSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$DepreciationSummaryToJson(this);

  String get methodDisplayName => method.displayName;

  String get formattedTotalDepreciation => 
      '${FloatingPointUtils.roundToPrecision(totalDepreciation, 2).toStringAsFixed(2)} DH';

  String get formattedRemainingValue => 
      '${FloatingPointUtils.roundToPrecision(remainingValue, 2).toStringAsFixed(2)} DH';

  String get formattedAverageAnnual => 
      '${FloatingPointUtils.roundToPrecision(averageAnnualDepreciation, 2).toStringAsFixed(2)} DH';

  /// Validate the summary for consistency
  ValidationResult validate(double originalAssetCost, double residualValue) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check if values are reasonable
    if (!FloatingPointUtils.isReasonableFinancialValue(totalDepreciation)) {
      errors.add('L\'amortissement total contient une valeur invalide');
    }

    if (!FloatingPointUtils.isReasonableFinancialValue(remainingValue)) {
      errors.add('La valeur résiduelle finale contient une valeur invalide');
    }

    if (!FloatingPointUtils.isReasonableFinancialValue(averageAnnualDepreciation)) {
      errors.add('L\'amortissement annuel moyen contient une valeur invalide');
    }

    // Check consistency
    final expectedTotal = originalAssetCost - residualValue;
    if (!FloatingPointUtils.areEqual(totalDepreciation, expectedTotal, epsilon: 0.01)) {
      warnings.add('L\'amortissement total ne correspond pas exactement au montant amortissable');
    }

    if (!FloatingPointUtils.areEqual(remainingValue, residualValue, epsilon: 0.01)) {
      warnings.add('La valeur résiduelle finale ne correspond pas à la valeur résiduelle prévue');
    }

    // Check for negative values
    if (totalDepreciation < 0) {
      errors.add('L\'amortissement total ne peut pas être négatif');
    }

    if (remainingValue < 0) {
      errors.add('La valeur résiduelle finale ne peut pas être négative');
    }

    if (averageAnnualDepreciation < 0) {
      errors.add('L\'amortissement annuel moyen ne peut pas être négatif');
    }

    // Check for reasonable ranges
    if (totalYears <= 0) {
      errors.add('Le nombre total d\'années doit être positif');
    }

    if (firstYearDepreciation < 0) {
      errors.add('L\'amortissement de la première année ne peut pas être négatif');
    }

    if (lastYearDepreciation < 0) {
      errors.add('L\'amortissement de la dernière année ne peut pas être négatif');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Check if the summary appears to be mathematically consistent
  bool get isConsistent {
    // Basic consistency checks
    if (totalYears <= 0) return false;
    if (totalDepreciation < 0 || remainingValue < 0) return false;
    if (averageAnnualDepreciation < 0) return false;
    
    // Check if average makes sense
    final expectedAverage = totalDepreciation / totalYears;
    return FloatingPointUtils.areEqual(averageAnnualDepreciation, expectedAverage, epsilon: 0.01);
  }
}

@JsonSerializable()
class DepreciationComparison {
  final DepreciationMethod method;
  final double totalDepreciation;
  final double firstYearDepreciation;
  final double netPresentValue;
  final double taxBenefit;
  final String recommendation;
  final bool calculationSuccessful;
  final String? calculationError;

  const DepreciationComparison({
    required this.method,
    required this.totalDepreciation,
    required this.firstYearDepreciation,
    required this.netPresentValue,
    required this.taxBenefit,
    required this.recommendation,
    this.calculationSuccessful = true,
    this.calculationError,
  });

  factory DepreciationComparison.fromJson(Map<String, dynamic> json) =>
      _$DepreciationComparisonFromJson(json);

  Map<String, dynamic> toJson() => _$DepreciationComparisonToJson(this);

  String get methodDisplayName => method.displayName;

  /// Create a failed comparison result
  factory DepreciationComparison.failed({
    required DepreciationMethod method,
    required String error,
  }) {
    return DepreciationComparison(
      method: method,
      totalDepreciation: 0.0,
      firstYearDepreciation: 0.0,
      netPresentValue: 0.0,
      taxBenefit: 0.0,
      recommendation: 'Calcul échoué: $error',
      calculationSuccessful: false,
      calculationError: error,
    );
  }

  /// Validate the comparison data
  ValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    if (!calculationSuccessful) {
      errors.add(calculationError ?? 'Calcul échoué pour la méthode ${method.displayName}');
      return ValidationResult(isValid: false, errors: errors);
    }

    // Check for reasonable values
    if (!FloatingPointUtils.isReasonableFinancialValue(totalDepreciation)) {
      errors.add('L\'amortissement total pour ${method.displayName} contient une valeur invalide');
    }

    if (!FloatingPointUtils.isReasonableFinancialValue(firstYearDepreciation)) {
      errors.add('L\'amortissement de première année pour ${method.displayName} contient une valeur invalide');
    }

    if (!FloatingPointUtils.isReasonableFinancialValue(netPresentValue)) {
      errors.add('La valeur actuelle nette pour ${method.displayName} contient une valeur invalide');
    }

    if (!FloatingPointUtils.isReasonableFinancialValue(taxBenefit)) {
      errors.add('L\'avantage fiscal pour ${method.displayName} contient une valeur invalide');
    }

    // Check for negative values where they shouldn't be
    if (totalDepreciation < 0) {
      errors.add('L\'amortissement total ne peut pas être négatif pour ${method.displayName}');
    }

    if (firstYearDepreciation < 0) {
      errors.add('L\'amortissement de première année ne peut pas être négatif pour ${method.displayName}');
    }

    // Warnings for unusual values
    if (firstYearDepreciation > totalDepreciation * 0.8) {
      warnings.add('L\'amortissement de première année est très élevé pour ${method.displayName}');
    }

    if (FloatingPointUtils.isZero(totalDepreciation)) {
      warnings.add('Aucun amortissement calculé pour ${method.displayName}');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Check if this comparison is valid and usable
  bool get isValid => calculationSuccessful && validate().isValid;
}

@JsonSerializable()
class TaxOptimizationAdvice {
  final String recommendedMethod;
  final double estimatedTaxSavings;
  final String reasoning;
  final List<String> considerations;
  final String implementationTiming;

  const TaxOptimizationAdvice({
    required this.recommendedMethod,
    required this.estimatedTaxSavings,
    required this.reasoning,
    required this.considerations,
    required this.implementationTiming,
  });

  factory TaxOptimizationAdvice.fromJson(Map<String, dynamic> json) =>
      _$TaxOptimizationAdviceFromJson(json);

  Map<String, dynamic> toJson() => _$TaxOptimizationAdviceToJson(this);
}

enum DepreciationMethod {
  linear,
  degressive,
  sumOfYearsDigits,
  unitsOfProduction;

  String get displayName {
    switch (this) {
      case DepreciationMethod.linear:
        return 'Linéaire';
      case DepreciationMethod.degressive:
        return 'Dégressive';
      case DepreciationMethod.sumOfYearsDigits:
        return 'Somme des chiffres des années';
      case DepreciationMethod.unitsOfProduction:
        return 'Unités de production';
    }
  }

  String get description {
    switch (this) {
      case DepreciationMethod.linear:
        return 'Amortissement constant sur toute la durée de vie';
      case DepreciationMethod.degressive:
        return 'Amortissement plus important les premières années';
      case DepreciationMethod.sumOfYearsDigits:
        return 'Amortissement dégressif basé sur la somme des années';
      case DepreciationMethod.unitsOfProduction:
        return 'Amortissement basé sur l\'utilisation réelle';
    }
  }

  String get detailedDescription {
    switch (this) {
      case DepreciationMethod.linear:
        return 'Méthode d\'amortissement linéaire où le montant est constant chaque année. '
               'Simple à calculer et prévisible.';
      case DepreciationMethod.degressive:
        return 'Méthode d\'amortissement dégressif où l\'amortissement est plus important '
               'les premières années. Nécessite un coefficient dégressif.';
      case DepreciationMethod.sumOfYearsDigits:
        return 'Méthode d\'amortissement dégressif basée sur la somme des chiffres des années. '
               'L\'amortissement diminue chaque année selon une formule mathématique.';
      case DepreciationMethod.unitsOfProduction:
        return 'Méthode d\'amortissement basée sur l\'utilisation réelle de l\'actif. '
               'Nécessite le nombre total d\'unités et la production annuelle.';
    }
  }

  bool get requiresCoefficient => this == DepreciationMethod.degressive;
  bool get requiresUnits => this == DepreciationMethod.unitsOfProduction;

  /// Get the typical coefficient range for degressive method
  String get coefficientRange {
    if (this == DepreciationMethod.degressive) {
      return '${CalculationLimits.minDegressiveCoefficient} - ${CalculationLimits.maxDegressiveCoefficient}';
    }
    return 'N/A';
  }

  /// Check if this method is suitable for the given asset characteristics
  bool isSuitableFor({
    required double assetCost,
    required int usefulLife,
    String? assetCategory,
  }) {
    switch (this) {
      case DepreciationMethod.linear:
        return true; // Always suitable
      case DepreciationMethod.degressive:
        return usefulLife >= 3 && assetCost >= 1000; // Typically for significant assets
      case DepreciationMethod.sumOfYearsDigits:
        return usefulLife <= 20; // Avoid very long useful lives
      case DepreciationMethod.unitsOfProduction:
        return (assetCategory?.toLowerCase().contains('production') ?? false) ||
               (assetCategory?.toLowerCase().contains('machine') ?? false);
    }
  }

  /// Get validation requirements for this method
  List<String> get validationRequirements {
    switch (this) {
      case DepreciationMethod.linear:
        return ['Coût de l\'actif', 'Durée de vie utile', 'Valeur résiduelle'];
      case DepreciationMethod.degressive:
        return ['Coût de l\'actif', 'Durée de vie utile', 'Valeur résiduelle', 'Coefficient dégressif'];
      case DepreciationMethod.sumOfYearsDigits:
        return ['Coût de l\'actif', 'Durée de vie utile', 'Valeur résiduelle'];
      case DepreciationMethod.unitsOfProduction:
        return ['Coût de l\'actif', 'Valeur résiduelle', 'Nombre total d\'unités', 'Production annuelle (optionnel)'];
    }
  }
}

enum AssetType {
  buildings,
  technicalInstallations,
  transportEquipment,
  officeEquipment,
  computerEquipment,
  otherEquipment,
  landImprovements,
  intangibleAssets;

  String get displayName {
    switch (this) {
      case AssetType.buildings:
        return 'Constructions et bâtiments';
      case AssetType.technicalInstallations:
        return 'Installations techniques';
      case AssetType.transportEquipment:
        return 'Matériel de transport';
      case AssetType.officeEquipment:
        return 'Mobilier et matériel de bureau';
      case AssetType.computerEquipment:
        return 'Matériel informatique';
      case AssetType.otherEquipment:
        return 'Autres équipements';
      case AssetType.landImprovements:
        return 'Terrains aménagés';
      case AssetType.intangibleAssets:
        return 'Immobilisations incorporelles';
    }
  }

  String get description {
    switch (this) {
      case AssetType.buildings:
        return 'Bâtiments, constructions, immobilier';
      case AssetType.technicalInstallations:
        return 'Machines, installations techniques, outillage';
      case AssetType.transportEquipment:
        return 'Véhicules, camions, matériel de transport';
      case AssetType.officeEquipment:
        return 'Mobilier de bureau, aménagements divers';
      case AssetType.computerEquipment:
        return 'Ordinateurs, serveurs, équipement IT';
      case AssetType.otherEquipment:
        return 'Autres équipements et machines';
      case AssetType.landImprovements:
        return 'Terrains avec aménagements';
      case AssetType.intangibleAssets:
        return 'Brevets, licences, logiciels';
    }
  }

  String get accountKey {
    switch (this) {
      case AssetType.buildings:
        return 'buildings';
      case AssetType.technicalInstallations:
        return 'technical_installations';
      case AssetType.transportEquipment:
        return 'transport_equipment';
      case AssetType.officeEquipment:
        return 'office_equipment';
      case AssetType.computerEquipment:
        return 'computer_equipment';
      case AssetType.otherEquipment:
        return 'other_equipment';
      case AssetType.landImprovements:
        return 'land_improvements';
      case AssetType.intangibleAssets:
        return 'intangible_assets';
    }
  }

  /// Get the appropriate chart accounts for this asset type
  /// Returns a tuple of (chargeAccount, amortizationAccount)
  (String, String) get chartAccounts {
    switch (this) {
      case AssetType.buildings:
        return ('6193', '28111'); // Constructions
      case AssetType.technicalInstallations:
        return ('6192', '28121'); // Installations techniques, matériel et outillage
      case AssetType.transportEquipment:
        return ('6195', '28131'); // Matériel de transport
      case AssetType.officeEquipment:
        return ('6194', '28141'); // Mobilier, matériel de bureau et aménagements divers
      case AssetType.computerEquipment:
        return ('6196', '28151'); // Matériel informatique
      case AssetType.otherEquipment:
        return ('6192', '28121'); // Other technical equipment
      case AssetType.landImprovements:
        return ('6193', '28101'); // Terrains aménagés
      case AssetType.intangibleAssets:
        return ('6197', '28110'); // Immobilisations incorporelles
    }
  }

  /// Auto-detect asset type from asset name
  static AssetType? detectFromAssetName(String assetName) {
    final name = assetName.toLowerCase();
    
    if (name.contains('construction') || name.contains('bâtiment') || name.contains('batiment') || name.contains('immobilier')) {
      return AssetType.buildings;
    } else if (name.contains('véhicule') || name.contains('vehicule') || name.contains('transport') || name.contains('camion')) {
      return AssetType.transportEquipment;
    } else if (name.contains('ordinateur') || name.contains('informatique') || name.contains('serveur') || name.contains('it')) {
      return AssetType.computerEquipment;
    } else if (name.contains('mobilier') || name.contains('bureau') || name.contains('furniture')) {
      return AssetType.officeEquipment;
    } else if (name.contains('machine') || name.contains('équipement') || name.contains('equipement') || name.contains('installation') || name.contains('technique')) {
      return AssetType.technicalInstallations;
    } else if (name.contains('terrain') || name.contains('aménagement') || name.contains('amenagement')) {
      return AssetType.landImprovements;
    } else if (name.contains('brevet') || name.contains('licence') || name.contains('logiciel') || name.contains('incorporelle')) {
      return AssetType.intangibleAssets;
    }
    
    return null; // Return null for auto-detection, will fallback to buildings
  }
}

@JsonSerializable()
class AssetPreset {
  final String name;
  final String category;
  final int defaultUsefulLife;
  final double defaultResidualValuePercent;
  final DepreciationMethod recommendedMethod;
  final String description;
  final double? defaultDegressiveCoefficient;
  final double? estimatedTotalUnits;

  const AssetPreset({
    required this.name,
    required this.category,
    required this.defaultUsefulLife,
    required this.defaultResidualValuePercent,
    required this.recommendedMethod,
    required this.description,
    this.defaultDegressiveCoefficient,
    this.estimatedTotalUnits,
  });

  factory AssetPreset.fromJson(Map<String, dynamic> json) =>
      _$AssetPresetFromJson(json);

  Map<String, dynamic> toJson() => _$AssetPresetToJson(this);

  /// Validate the preset configuration
  ValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    if (name.trim().isEmpty) {
      errors.add('Le nom du preset ne peut pas être vide');
    }

    if (category.trim().isEmpty) {
      errors.add('La catégorie ne peut pas être vide');
    }

    if (defaultUsefulLife < CalculationLimits.minUsefulLife || 
        defaultUsefulLife > CalculationLimits.maxUsefulLife) {
      errors.add('La durée de vie par défaut doit être entre ${CalculationLimits.minUsefulLife} et ${CalculationLimits.maxUsefulLife} ans');
    }

    if (defaultResidualValuePercent < 0 || defaultResidualValuePercent >= 100) {
      errors.add('Le pourcentage de valeur résiduelle doit être entre 0 et 99%');
    }

    if (recommendedMethod.requiresCoefficient && defaultDegressiveCoefficient == null) {
      warnings.add('Un coefficient dégressif par défaut est recommandé pour la méthode ${recommendedMethod.displayName}');
    }

    if (defaultDegressiveCoefficient != null) {
      if (defaultDegressiveCoefficient! < CalculationLimits.minDegressiveCoefficient ||
          defaultDegressiveCoefficient! > CalculationLimits.maxDegressiveCoefficient) {
        errors.add('Le coefficient dégressif par défaut doit être entre ${CalculationLimits.minDegressiveCoefficient} et ${CalculationLimits.maxDegressiveCoefficient}');
      }
    }

    if (recommendedMethod.requiresUnits && estimatedTotalUnits == null) {
      warnings.add('Un nombre total d\'unités estimé est recommandé pour la méthode ${recommendedMethod.displayName}');
    }

    if (estimatedTotalUnits != null) {
      if (estimatedTotalUnits! < CalculationLimits.minTotalUnits ||
          estimatedTotalUnits! > CalculationLimits.maxTotalUnits) {
        errors.add('Le nombre total d\'unités estimé doit être entre ${CalculationLimits.minTotalUnits} et ${CalculationLimits.maxTotalUnits}');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Apply this preset to create an input template
  EnhancedDepreciationInput applyToInput({
    required String assetName,
    required double assetCost,
    DateTime? acquisitionDate,
  }) {
    final residualValue = assetCost * (defaultResidualValuePercent / 100);
    
    return EnhancedDepreciationInput(
      assetName: assetName,
      assetCost: assetCost,
      residualValue: residualValue,
      usefulLifeYears: defaultUsefulLife,
      method: recommendedMethod,
      acquisitionDate: acquisitionDate ?? DateTime.now(),
      degressiveCoefficient: defaultDegressiveCoefficient,
      totalUnits: estimatedTotalUnits,
      midYearConvention: false,
      assetType: category,
    );
  }

  static List<AssetPreset> get commonPresets => [
    const AssetPreset(
      name: 'Véhicule de tourisme',
      category: 'Transport',
      defaultUsefulLife: 5,
      defaultResidualValuePercent: 10,
      recommendedMethod: DepreciationMethod.degressive,
      description: 'Voiture de fonction ou véhicule commercial',
      defaultDegressiveCoefficient: 2.0,
    ),
    const AssetPreset(
      name: 'Matériel informatique',
      category: 'Informatique',
      defaultUsefulLife: 3,
      defaultResidualValuePercent: 5,
      recommendedMethod: DepreciationMethod.degressive,
      description: 'Ordinateurs, serveurs, équipements IT',
      defaultDegressiveCoefficient: 2.5,
    ),
    const AssetPreset(
      name: 'Mobilier de bureau',
      category: 'Mobilier',
      defaultUsefulLife: 10,
      defaultResidualValuePercent: 10,
      recommendedMethod: DepreciationMethod.linear,
      description: 'Bureaux, chaises, armoires',
    ),
    const AssetPreset(
      name: 'Machine industrielle',
      category: 'Production',
      defaultUsefulLife: 10,
      defaultResidualValuePercent: 15,
      recommendedMethod: DepreciationMethod.unitsOfProduction,
      description: 'Équipement de production industrielle',
      estimatedTotalUnits: 100000,
    ),
    const AssetPreset(
      name: 'Bâtiment commercial',
      category: 'Immobilier',
      defaultUsefulLife: 20,
      defaultResidualValuePercent: 20,
      recommendedMethod: DepreciationMethod.linear,
      description: 'Locaux commerciaux et industriels',
    ),
    const AssetPreset(
      name: 'Équipement médical',
      category: 'Médical',
      defaultUsefulLife: 8,
      defaultResidualValuePercent: 12,
      recommendedMethod: DepreciationMethod.degressive,
      description: 'Appareils et équipements médicaux',
      defaultDegressiveCoefficient: 1.75,
    ),
    const AssetPreset(
      name: 'Outillage industriel',
      category: 'Outillage',
      defaultUsefulLife: 7,
      defaultResidualValuePercent: 8,
      recommendedMethod: DepreciationMethod.sumOfYearsDigits,
      description: 'Outils et équipements industriels',
    ),
  ];

  /// Get presets filtered by category
  static List<AssetPreset> getPresetsByCategory(String category) {
    return commonPresets.where((preset) => 
      preset.category.toLowerCase() == category.toLowerCase()).toList();
  }

  /// Find preset by name
  static AssetPreset? findPresetByName(String name) {
    try {
      return commonPresets.firstWhere((preset) => 
        preset.name.toLowerCase() == name.toLowerCase());
    } catch (e) {
      return null;
    }
  }

  /// Get all available categories
  static List<String> get availableCategories {
    return commonPresets.map((preset) => preset.category).toSet().toList()..sort();
  }
}
