import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

part 'bookmark_data.g.dart';

/// Enumeration for different types of bookmarks
enum BookmarkType {
  section,
  specificContent,
  personalNote,
}

/// Data model for user bookmarks with Hive persistence
@HiveType(typeId: 3)
class BookmarkData extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String guideId;

  @HiveField(2)
  final String sectionId;

  @HiveField(3)
  final String title;

  @HiveField(4)
  final String description;

  @HiveField(5)
  final BookmarkType bookmarkType;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  DateTime lastAccessed;

  @HiveField(8)
  final List<String> tags;

  @HiveField(9)
  final Color color;

  @HiveField(10)
  final String? position; // For bookmarking specific parts within a section

  BookmarkData({
    required this.id,
    required this.guideId,
    required this.sectionId,
    required this.title,
    this.description = '',
    this.bookmarkType = BookmarkType.section,
    DateTime? createdAt,
    DateTime? lastAccessed,
    this.tags = const [],
    this.color = Colors.blue,
    this.position,
  }) : createdAt = createdAt ?? DateTime.now(),
       lastAccessed = lastAccessed ?? DateTime.now();

  /// Creates a BookmarkData from JSON
  factory BookmarkData.fromJson(Map<String, dynamic> json) {
    return BookmarkData(
      id: json['id'] ?? '',
      guideId: json['guideId'] ?? '',
      sectionId: json['sectionId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      bookmarkType: _parseBookmarkType(json['bookmarkType']),
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      lastAccessed: DateTime.tryParse(json['lastAccessed'] ?? '') ?? DateTime.now(),
      tags: List<String>.from(json['tags'] ?? []),
      color: Color(json['color'] ?? Colors.blue.toARGB32),
      position: json['position'],
    );
  }

  /// Converts BookmarkData to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'guideId': guideId,
      'sectionId': sectionId,
      'title': title,
      'description': description,
      'bookmarkType': bookmarkType.name,
      'createdAt': createdAt.toIso8601String(),
      'lastAccessed': lastAccessed.toIso8601String(),
      'tags': tags,
      'color': color.toARGB32,
      'position': position,
    };
  }

  /// Creates a copy with updated fields
  BookmarkData copyWith({
    String? id,
    String? guideId,
    String? sectionId,
    String? title,
    String? description,
    BookmarkType? bookmarkType,
    DateTime? createdAt,
    DateTime? lastAccessed,
    List<String>? tags,
    Color? color,
    String? position,
  }) {
    return BookmarkData(
      id: id ?? this.id,
      guideId: guideId ?? this.guideId,
      sectionId: sectionId ?? this.sectionId,
      title: title ?? this.title,
      description: description ?? this.description,
      bookmarkType: bookmarkType ?? this.bookmarkType,
      createdAt: createdAt ?? this.createdAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      tags: tags ?? this.tags,
      color: color ?? this.color,
      position: position ?? this.position,
    );
  }

  /// Updates the last accessed time
  void updateLastAccessed() {
    lastAccessed = DateTime.now();
    save(); // Save to Hive
  }

  /// Adds a tag to the bookmark
  BookmarkData addTag(String tag) {
    if (!tags.contains(tag)) {
      final newTags = List<String>.from(tags)..add(tag);
      return copyWith(tags: newTags);
    }
    return this;
  }

  /// Removes a tag from the bookmark
  BookmarkData removeTag(String tag) {
    final newTags = List<String>.from(tags)..remove(tag);
    return copyWith(tags: newTags);
  }

  /// Validates bookmark data integrity
  bool isValid() {
    return id.isNotEmpty &&
           guideId.isNotEmpty &&
           sectionId.isNotEmpty &&
           title.isNotEmpty;
  }

  /// Gets display text for bookmark type
  String getBookmarkTypeLabel() {
    switch (bookmarkType) {
      case BookmarkType.section:
        return 'Section';
      case BookmarkType.specificContent:
        return 'Contenu spécifique';
      case BookmarkType.personalNote:
        return 'Note personnelle';
    }
  }

  /// Parses bookmark type from string
  static BookmarkType _parseBookmarkType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'specificcontent':
      case 'specific_content':
        return BookmarkType.specificContent;
      case 'personalnote':
      case 'personal_note':
        return BookmarkType.personalNote;
      default:
        return BookmarkType.section;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookmarkData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'BookmarkData(id: $id, title: $title, guideId: $guideId, sectionId: $sectionId)';
  }
}