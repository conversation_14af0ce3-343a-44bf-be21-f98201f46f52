import 'dart:math';
import 'dart:developer' as developer;
import '../models/calculators/enhanced_depreciation_data.dart';
import '../models/immobilisations/amortization_row.dart';
import '../utils/calculation_utils.dart' as calc_utils;
import '../widgets/journal_comptable_widget.dart';

class EnhancedDepreciationService {
  // Calculation limits to prevent infinite loops and extreme values
  static const int _maxIterations = 1000;
  static const double _convergenceThreshold = 0.01;
  
  /// Calculate months remaining in first year from acquisition date
  int _calculateMonthsInFirstYear(DateTime acquisitionDate) {
    return 12 - acquisitionDate.month + 1;
  }
  
  /// Calculate months in last year for prorata calculations
  int _calculateMonthsInLastYear(DateTime acquisitionDate, int totalYears) {
    // Fixed formula: last year months = acquisition month - 1 (if month > 1) or 12 (if month = 1)
    final lastYearMonths = acquisitionDate.month > 1 ? acquisitionDate.month - 1 : 12;
    return lastYearMonths;
  }
  
  /// Generate calendar year label with month information for partial years
  String _generateCalendarYearLabel(DateTime acquisitionDate, int yearIndex, int monthsInYear) {
    final calendarYear = acquisitionDate.year + yearIndex - 1;
    if (monthsInYear >= 12) {
      return '$calendarYear';
    } else {
      return '$calendarYear ($monthsInYear mois)';
    }
  }
  
  /// Apply prorata temporis to annual amount based on months
  double _applyProrataToAmount(double annualAmount, int months) {
    if (months <= 0) return 0.0;
    if (months >= 12) return annualAmount;
    return annualAmount * months / 12;
  }
  
  /// Calculate depreciation for a single method without generating comparisons
  /// This prevents infinite recursion when called from comparison methods
  EnhancedDepreciationResult calculateDepreciation(
    EnhancedDepreciationInput input, {
    bool includeComparisons = true,
    List<DepreciationComparison>? preCalculatedComparisons,
  }) {
    final stopwatch = Stopwatch()..start();
    
    try {
      // Enhanced validation
      final validationResult = _validateInput(input);
      if (!validationResult.isValid) {
        throw ArgumentError('Invalid depreciation input: ${validationResult.primaryError}');
      }
      
      developer.log('Starting depreciation calculation for method: ${input.method.displayName}');
      
      List<AmortizationRow> amortizationTable;
      List<AmortizationRow>? accountingAmortizationTable;
      List<AmortizationRow>? fiscalAmortizationTable;
      List<AmortizationRow>? derogatoryAmortizationTable;
      final warnings = <String>[];
      
      // Add validation warnings to calculation warnings
      if (validationResult.hasWarnings) {
        warnings.addAll(validationResult.warnings);
      }
      
      // Check if derogatory calculation is needed
      final needsDerogatory = input.enableDerogatoire && 
                             input.fiscalMethod != null && 
                             input.fiscalMethod != input.method;
      
      if (needsDerogatory) {
        // Calculate accounting amortization
        switch (input.method) {
          case DepreciationMethod.linear:
            accountingAmortizationTable = calculateLinearDepreciation(input);
            break;
          case DepreciationMethod.degressive:
            accountingAmortizationTable = calculateDegressiveDepreciation(input);
            break;
          case DepreciationMethod.sumOfYearsDigits:
            accountingAmortizationTable = calculateSumOfYearsDigits(input);
            break;
          case DepreciationMethod.unitsOfProduction:
            accountingAmortizationTable = calculateUnitsOfProduction(input);
            break;
        }
        
        // Calculate fiscal amortization
        fiscalAmortizationTable = calculateFiscalDepreciation(input);
        
        // Calculate derogatory amortization
        derogatoryAmortizationTable = calculateDerogatoryAmortization(
          accountingAmortizationTable, 
          fiscalAmortizationTable
        );
        
        // Primary table is accounting when derogatory is used
        amortizationTable = accountingAmortizationTable;
      } else {
        // Standard single-method calculation
        switch (input.method) {
          case DepreciationMethod.linear:
            amortizationTable = calculateLinearDepreciation(input);
            break;
          case DepreciationMethod.degressive:
            amortizationTable = calculateDegressiveDepreciation(input);
            break;
          case DepreciationMethod.sumOfYearsDigits:
            amortizationTable = calculateSumOfYearsDigits(input);
            break;
          case DepreciationMethod.unitsOfProduction:
            amortizationTable = calculateUnitsOfProduction(input);
            break;
        }
      }
      
      // Validate calculation results
      final tableValidation = _validateAmortizationTable(amortizationTable, input);
      if (!tableValidation.isValid) {
        warnings.add('Problème de calcul détecté: ${tableValidation.primaryError}');
      }
      if (tableValidation.hasWarnings) {
        warnings.addAll(tableValidation.warnings);
      }
      
      final summary = _createSummary(amortizationTable, input);
      
      // Validate summary consistency
      final summaryValidation = summary.validate(input.assetCost, input.residualValue);
      if (summaryValidation.hasWarnings) {
        warnings.addAll(summaryValidation.warnings);
      }
      
      // Generate tax advice - FIXED: Pass pre-calculated comparisons to prevent recursion
      TaxOptimizationAdvice? taxAdvice;
      List<DepreciationComparison>? methodComparisons;
      
      if (includeComparisons) {
        if (preCalculatedComparisons != null) {
          methodComparisons = preCalculatedComparisons;
          taxAdvice = _generateTaxOptimizationAdvice(input, amortizationTable, methodComparisons);
        } else {
          // Only calculate comparisons if not provided and not in a recursive call
          methodComparisons = compareDepreciationMethods(input);
          taxAdvice = _generateTaxOptimizationAdvice(input, amortizationTable, methodComparisons);
        }
      }
      
      // Generate accounting journal entries for the amortization schedule
      List<JournalEntry>? journalEntries;
      try {
        journalEntries = _generateJournalEntriesFromTable(amortizationTable, input);
      } catch (e) {
        developer.log('Journal entries generation failed: $e');
        // Do not fail the entire calculation on journal generation error; record a warning instead
        warnings.add('Échec de génération des écritures comptables: ${e.toString()}');
        journalEntries = null;
      }
      
      stopwatch.stop();
      
      final calculationContext = CalculationContext(
        calculationDate: DateTime.now(),
        calculationVersion: '1.1.0',
        calculationDuration: stopwatch.elapsed,
        iterationsPerformed: amortizationTable.length,
        converged: true,
        finalPrecision: _convergenceThreshold,
        debugInfo: {
          'method': input.method.displayName,
          'assetCost': input.assetCost,
          'usefulLife': input.usefulLifeYears,
          'includeComparisons': includeComparisons,
        },
      );
      
      developer.log('Depreciation calculation completed in ${stopwatch.elapsedMilliseconds}ms');
      
      return EnhancedDepreciationResult(
        amortizationTable: amortizationTable,
        summary: summary,
        methodComparisons: methodComparisons,
        taxAdvice: taxAdvice,
        calculationContext: calculationContext,
        calculationWarnings: warnings.isNotEmpty ? warnings : null,
        journalEntries: journalEntries,
        accountingAmortizationTable: accountingAmortizationTable,
        fiscalAmortizationTable: fiscalAmortizationTable,
        derogatoryAmortizationTable: derogatoryAmortizationTable,
      );
      
    } catch (e, stackTrace) {
      stopwatch.stop();
      developer.log('Depreciation calculation failed: $e', error: e, stackTrace: stackTrace);
      
      rethrow; // Re-throw for proper error handling by caller
    }
  }

  /// Enhanced validation for input parameters
  ValidationResult _validateInput(EnhancedDepreciationInput input) {
    // Use the enhanced validation from the input model
    final baseValidation = input.validate();
    final errors = <String>[...baseValidation.errors];
    final warnings = <String>[...baseValidation.warnings];
    
    // Additional service-level validations
    if (input.method == DepreciationMethod.degressive) {
      if (input.degressiveCoefficient == null) {
        errors.add('Coefficient dégressif requis pour la méthode dégressive');
      } else if (input.degressiveCoefficient! <= 1.0) {
        errors.add('Le coefficient dégressif doit être supérieur à 1.0');
      }
    }
    
    if (input.method == DepreciationMethod.unitsOfProduction) {
      if (input.totalUnits == null) {
        errors.add('Nombre total d\'unités requis pour la méthode des unités de production');
      }
      if (input.annualUnitsProduced == null || input.annualUnitsProduced!.isEmpty) {
        warnings.add('Données de production annuelle manquantes - utilisation de valeurs par défaut');
      }
    }
    
    // Validate acquisition date is always required
    final now = DateTime.now();
    final maxFutureDate = DateTime(now.year + 1, 12, 31);
    final minPastDate = DateTime(1900, 1, 1);
    
    if (input.acquisitionDate.isAfter(maxFutureDate)) {
      errors.add('La date d\'acquisition ne peut pas être dans le futur lointain');
    } else if (input.acquisitionDate.isBefore(minPastDate)) {
      errors.add('La date d\'acquisition est trop ancienne');
    }
    
    // Validate fiscal method parameters when derogatory calculation is enabled
    if (input.enableDerogatoire) {
      if (input.fiscalMethod == null) {
        errors.add('La méthode fiscale est requise quand l\'amortissement dérogatoire est activé');
      } else if (input.fiscalMethod == input.method) {
        warnings.add('La méthode fiscale est identique à la méthode comptable - aucun amortissement dérogatoire ne sera généré');
      } else {
        // Validate fiscal method parameters
        if (input.fiscalMethod == DepreciationMethod.degressive) {
          if (input.fiscalDegressiveCoefficient == null) {
            errors.add('Le coefficient dégressif fiscal est requis pour la méthode dégressive fiscale');
          } else if (input.fiscalDegressiveCoefficient! <= 1.0) {
            errors.add('Le coefficient dégressif fiscal doit être supérieur à 1.0');
          }
        }
        
        if (input.fiscalMethod == DepreciationMethod.unitsOfProduction) {
          if (input.totalUnits == null) {
            errors.add('Le nombre total d\'unités est requis pour la méthode fiscale des unités de production');
          }
        }
      }
    }
    
    // Check for potential calculation overflow
    final maxDepreciation = input.depreciableAmount;
    if (maxDepreciation > CalculationLimits.maxAssetCost) {
      warnings.add('Montant amortissable très élevé - les calculs pourraient être lents');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
  
  /// Validate amortization table for consistency
  ValidationResult _validateAmortizationTable(List<AmortizationRow> table, EnhancedDepreciationInput input) {
    final errors = <String>[];
    final warnings = <String>[];
    
    if (table.isEmpty) {
      errors.add('Tableau d\'amortissement vide');
      return ValidationResult(isValid: false, errors: errors);
    }
    
    // Check for reasonable values
    for (final row in table) {
      if (!FloatingPointUtils.isReasonableFinancialValue(row.annuity)) {
        errors.add('Annuité invalide pour l\'année ${row.year}');
      }
      if (!FloatingPointUtils.isReasonableFinancialValue(row.netBookValue)) {
        errors.add('Valeur nette comptable invalide pour l\'année ${row.year}');
      }
      if (row.annuity < 0) {
        errors.add('Annuité négative pour l\'année ${row.year}');
      }
      if (row.netBookValue < 0) {
        warnings.add('Valeur nette comptable négative pour l\'année ${row.year}');
      }
    }
    
    // Check total depreciation
    final totalDepreciation = table.fold<double>(0, (sum, row) => sum + row.annuity);
    final expectedTotal = input.depreciableAmount;
    
    if (!FloatingPointUtils.areEqual(totalDepreciation, expectedTotal, epsilon: 1.0)) {
      warnings.add('L\'amortissement total ne correspond pas exactement au montant amortissable');
    }
    
    // Check final book value
    final finalBookValue = table.last.netBookValue;
    if (!FloatingPointUtils.areEqual(finalBookValue, input.residualValue, epsilon: 1.0)) {
      warnings.add('La valeur finale ne correspond pas à la valeur résiduelle prévue');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  List<AmortizationRow> calculateLinearDepreciation(EnhancedDepreciationInput input) {
    try {
      final annualDepreciation = FloatingPointUtils.safeDivide(
        input.depreciableAmount, 
        input.usefulLifeYears.toDouble(),
      );
      
      if (FloatingPointUtils.isZero(annualDepreciation)) {
        throw ArgumentError('Amortissement annuel nul - vérifiez le montant amortissable');
      }
      
      final rows = <AmortizationRow>[];
      double cumulativeDepreciation = 0;
      
      for (int year = 1; year <= input.usefulLifeYears; year++) {
        // Check for timeout
        if (year > _maxIterations) {
          throw StateError('Calcul interrompu - trop d\'itérations');
        }
        
        double yearlyDepreciation = annualDepreciation;
        int monthsInYear = 12;
        
        // Apply prorata temporis for first year
        if (year == 1) {
          monthsInYear = _calculateMonthsInFirstYear(input.acquisitionDate);
          yearlyDepreciation = _applyProrataToAmount(annualDepreciation, monthsInYear);
        }
        // Apply prorata temporis for last year if needed
        else if (year == input.usefulLifeYears) {
          monthsInYear = _calculateMonthsInLastYear(input.acquisitionDate, input.usefulLifeYears);
          yearlyDepreciation = _applyProrataToAmount(annualDepreciation, monthsInYear);
        }
        
        // Handle mid-year convention (legacy support)
        if (input.midYearConvention) {
          if (year == 1 || year == input.usefulLifeYears) {
            yearlyDepreciation = annualDepreciation * 0.5;
            monthsInYear = 6;
          }
        }
        
        cumulativeDepreciation += yearlyDepreciation;
        
        // Ensure we don't exceed depreciable amount
        if (cumulativeDepreciation > input.depreciableAmount) {
          yearlyDepreciation -= (cumulativeDepreciation - input.depreciableAmount);
          cumulativeDepreciation = input.depreciableAmount;
        }
        
        final bookValue = input.assetCost - cumulativeDepreciation;
        
        // Round values to avoid floating-point precision issues
        final roundedAnnuity = FloatingPointUtils.roundToPrecision(yearlyDepreciation, 2);
        final roundedCumulative = FloatingPointUtils.roundToPrecision(cumulativeDepreciation, 2);
        final roundedBookValue = FloatingPointUtils.roundToPrecision(bookValue, 2);
        
        // Generate calendar year label
        final yearLabel = _generateCalendarYearLabel(input.acquisitionDate, year, monthsInYear);
        
        rows.add(AmortizationRow(
          year: input.acquisitionDate.year + year - 1,
          yearLabel: yearLabel,
          baseAmount: input.assetCost,
          rate: (1.0 / input.usefulLifeYears) * 100, // Convert to percentage
          annuity: roundedAnnuity,
          cumulativeAnnuity: roundedCumulative,
          netBookValue: roundedBookValue,
        ));
        
        // Stop if fully depreciated
        if (FloatingPointUtils.areEqual(cumulativeDepreciation, input.depreciableAmount)) {
          break;
        }
      }
      
      return rows;
    } catch (e) {
      developer.log('Linear depreciation calculation failed: $e');
      rethrow;
    }
  }

  List<AmortizationRow> calculateDegressiveDepreciation(EnhancedDepreciationInput input) {
    try {
      final coefficient = input.degressiveCoefficient ?? 2.0;
      final degressiveRate = FloatingPointUtils.safeDivide(coefficient, input.usefulLifeYears.toDouble());
      final linearRate = FloatingPointUtils.safeDivide(1.0, input.usefulLifeYears.toDouble());
      
      if (FloatingPointUtils.isZero(degressiveRate) || FloatingPointUtils.isZero(linearRate)) {
        throw ArgumentError('Taux de calcul invalides pour la méthode dégressive');
      }
      
      final rows = <AmortizationRow>[];
      double bookValue = input.assetCost;
      double cumulativeDepreciation = 0;
      int iterations = 0;
      
      // Initialize convergence detector for improved convergence detection
      final convergenceDetector = calc_utils.ConvergenceDetector(
        maxHistorySize: 10,
        convergenceThreshold: _convergenceThreshold,
        oscillationThreshold: _convergenceThreshold * 0.1,
      );
      
      for (int year = 1; year <= input.usefulLifeYears; year++) {
        iterations++;
        
        // Safety check for infinite loops
        if (iterations > _maxIterations) {
          throw StateError('Calcul dégressif interrompu - trop d\'itérations');
        }
        
        final remainingYears = input.usefulLifeYears - year + 1;
        final remainingLinearRate = remainingYears > 0 
            ? FloatingPointUtils.safeDivide(1.0, remainingYears.toDouble())
            : 0.0;
        
        // Choose the higher rate between degressive and linear
        final rate = max(degressiveRate, remainingLinearRate);
        
        double yearlyDepreciation = bookValue * rate;
        int monthsInYear = 12;
        
        // Apply prorata temporis for first year
        if (year == 1) {
          monthsInYear = _calculateMonthsInFirstYear(input.acquisitionDate);
          yearlyDepreciation = _applyProrataToAmount(yearlyDepreciation, monthsInYear);
        }
        // Apply prorata temporis for last year if needed
        else if (year == input.usefulLifeYears) {
          monthsInYear = _calculateMonthsInLastYear(input.acquisitionDate, input.usefulLifeYears);
          yearlyDepreciation = _applyProrataToAmount(yearlyDepreciation, monthsInYear);
        }
        
        // Ensure we don't depreciate below residual value
        final maxDepreciation = max(0.0, bookValue - input.residualValue);
        yearlyDepreciation = min(yearlyDepreciation, maxDepreciation);
        
        // Handle mid-year convention (legacy support)
        if (year == 1 && input.midYearConvention) {
          yearlyDepreciation *= 0.5;
          monthsInYear = 6;
        }
        
        // Prevent negative depreciation
        if (yearlyDepreciation < 0) {
          yearlyDepreciation = 0;
        }
        
        cumulativeDepreciation += yearlyDepreciation;
        bookValue -= yearlyDepreciation;
        
        // Ensure book value doesn't go below residual value due to floating-point errors
        if (bookValue < input.residualValue) {
          final adjustment = input.residualValue - bookValue;
          yearlyDepreciation -= adjustment;
          cumulativeDepreciation -= adjustment;
          bookValue = input.residualValue;
        }
        
        // Add value to convergence detector for enhanced convergence detection
        convergenceDetector.addValue(yearlyDepreciation);
        
        // Round values to avoid floating-point precision issues
        final roundedAnnuity = FloatingPointUtils.roundToPrecision(yearlyDepreciation, 2);
        final roundedCumulative = FloatingPointUtils.roundToPrecision(cumulativeDepreciation, 2);
        final roundedBookValue = FloatingPointUtils.roundToPrecision(bookValue, 2);
        
        // Generate calendar year label
        final yearLabel = _generateCalendarYearLabel(input.acquisitionDate, year, monthsInYear);
        
        rows.add(AmortizationRow(
          year: input.acquisitionDate.year + year - 1,
          yearLabel: yearLabel,
          baseAmount: input.assetCost,
          rate: FloatingPointUtils.roundToPrecision(rate * 100, 2), // Convert to percentage
          annuity: roundedAnnuity,
          cumulativeAnnuity: roundedCumulative,
          netBookValue: roundedBookValue,
          degressiveRate: FloatingPointUtils.roundToPrecision(degressiveRate * 100, 2),
          linearRate: FloatingPointUtils.roundToPrecision(linearRate * 100, 2),
          isLinearSwitchYear: FloatingPointUtils.areEqual(rate, remainingLinearRate),
        ));
        
        // Enhanced convergence detection using ConvergenceDetector
        if (FloatingPointUtils.areEqual(bookValue, input.residualValue, epsilon: 0.01) ||
            FloatingPointUtils.isZero(yearlyDepreciation)) {
          developer.log('Degressive calculation reached natural end at year $year');
          break;
        }
        
        // Check for convergence using the enhanced detector
        if (convergenceDetector.hasConverged()) {
          developer.log('Degressive calculation converged at year $year (convergence rate: ${convergenceDetector.getConvergenceRate().toStringAsFixed(6)})');
          break;
        }
        
        // Check for oscillation patterns that might indicate calculation issues
        if (convergenceDetector.isOscillating()) {
          developer.log('Degressive calculation oscillation detected at year $year - terminating to prevent infinite loop');
          break;
        }
        
        // Check if calculation is stuck (no significant change)
        if (convergenceDetector.isStuck(minSamples: 5)) {
          developer.log('Degressive calculation appears stuck at year $year - terminating');
          break;
        }
      }
      
      return rows;
    } catch (e) {
      developer.log('Degressive depreciation calculation failed: $e');
      rethrow;
    }
  }

  List<AmortizationRow> calculateSumOfYearsDigits(EnhancedDepreciationInput input) {
    try {
      // Check for potential overflow in sum calculation
      if (input.usefulLifeYears > 100) {
        throw ArgumentError('Durée de vie trop longue pour la méthode somme des chiffres des années');
      }
      
      final sumOfYears = (input.usefulLifeYears * (input.usefulLifeYears + 1)) / 2;
      
      if (FloatingPointUtils.isZero(sumOfYears)) {
        throw ArgumentError('Somme des années invalide');
      }
      
      final rows = <AmortizationRow>[];
      double cumulativeDepreciation = 0;
      
      for (int year = 1; year <= input.usefulLifeYears; year++) {
        // Safety check
        if (year > _maxIterations) {
          throw StateError('Calcul somme des chiffres interrompu - trop d\'itérations');
        }
        
        final remainingYears = input.usefulLifeYears - year + 1;
        final fraction = FloatingPointUtils.safeDivide(remainingYears.toDouble(), sumOfYears);
        
        double yearlyDepreciation = input.depreciableAmount * fraction;
        int monthsInYear = 12;
        
        // Apply prorata temporis for first year
        if (year == 1) {
          monthsInYear = _calculateMonthsInFirstYear(input.acquisitionDate);
          yearlyDepreciation = _applyProrataToAmount(yearlyDepreciation, monthsInYear);
        }
        // Apply prorata temporis for last year if needed
        else if (year == input.usefulLifeYears) {
          monthsInYear = _calculateMonthsInLastYear(input.acquisitionDate, input.usefulLifeYears);
          yearlyDepreciation = _applyProrataToAmount(yearlyDepreciation, monthsInYear);
        }
        
        // Handle mid-year convention (legacy support)
        if (year == 1 && input.midYearConvention) {
          yearlyDepreciation *= 0.5;
          monthsInYear = 6;
        }
        
        cumulativeDepreciation += yearlyDepreciation;
        
        // Ensure we don't exceed depreciable amount
        if (cumulativeDepreciation > input.depreciableAmount) {
          yearlyDepreciation -= (cumulativeDepreciation - input.depreciableAmount);
          cumulativeDepreciation = input.depreciableAmount;
        }
        
        final bookValue = input.assetCost - cumulativeDepreciation;
        
        // Round values to avoid floating-point precision issues
        final roundedAnnuity = FloatingPointUtils.roundToPrecision(yearlyDepreciation, 2);
        final roundedCumulative = FloatingPointUtils.roundToPrecision(cumulativeDepreciation, 2);
        final roundedBookValue = FloatingPointUtils.roundToPrecision(bookValue, 2);
        
        final rate = FloatingPointUtils.safeDivide(yearlyDepreciation, input.assetCost) * 100;
        
        // Generate calendar year label
        final yearLabel = _generateCalendarYearLabel(input.acquisitionDate, year, monthsInYear);
        
        rows.add(AmortizationRow(
          year: input.acquisitionDate.year + year - 1,
          yearLabel: yearLabel,
          baseAmount: input.assetCost,
          rate: FloatingPointUtils.roundToPrecision(rate, 2), // Convert to percentage
          annuity: roundedAnnuity,
          cumulativeAnnuity: roundedCumulative,
          netBookValue: roundedBookValue,
        ));
        
        // Stop if fully depreciated
        if (FloatingPointUtils.areEqual(cumulativeDepreciation, input.depreciableAmount)) {
          break;
        }
      }
      
      return rows;
    } catch (e) {
      developer.log('Sum of years digits calculation failed: $e');
      rethrow;
    }
  }

  List<AmortizationRow> calculateUnitsOfProduction(EnhancedDepreciationInput input) {
    try {
      if (input.totalUnits == null) {
        throw ArgumentError('Nombre total d\'unités requis pour la méthode des unités de production');
      }
      
      if (FloatingPointUtils.isZero(input.totalUnits!)) {
        throw ArgumentError('Le nombre total d\'unités ne peut pas être zéro');
      }
      
      final depreciationPerUnit = FloatingPointUtils.safeDivide(
        input.depreciableAmount, 
        input.totalUnits!,
      );
      
      if (FloatingPointUtils.isZero(depreciationPerUnit)) {
        throw ArgumentError('Amortissement par unité invalide');
      }
      
      final rows = <AmortizationRow>[];
      double cumulativeDepreciation = 0;
      
      // Use provided annual units or generate default values
      List<double> annualUnits;
      if (input.annualUnitsProduced != null && input.annualUnitsProduced!.isNotEmpty) {
        annualUnits = input.annualUnitsProduced!;
      } else {
        // Generate default annual production (equal distribution)
        final unitsPerYear = input.totalUnits! / input.usefulLifeYears;
        annualUnits = List.generate(input.usefulLifeYears, (index) => unitsPerYear);
      }
      
      for (int year = 0; year < annualUnits.length && year < _maxIterations; year++) {
        final unitsProduced = annualUnits[year];
        
        // Validate units produced
        if (unitsProduced < 0) {
          throw ArgumentError('Les unités produites ne peuvent pas être négatives pour l\'année ${year + 1}');
        }
        
        double yearlyDepreciation = unitsProduced * depreciationPerUnit;
        int monthsInYear = 12;
        
        // Apply prorata temporis for first year
        if (year == 0) {
          monthsInYear = _calculateMonthsInFirstYear(input.acquisitionDate);
          yearlyDepreciation = _applyProrataToAmount(yearlyDepreciation, monthsInYear);
        }
        // Apply prorata temporis for last year if needed
        else if (year == annualUnits.length - 1) {
          monthsInYear = _calculateMonthsInLastYear(input.acquisitionDate, input.usefulLifeYears);
          yearlyDepreciation = _applyProrataToAmount(yearlyDepreciation, monthsInYear);
        }
        
        // Ensure we don't exceed total depreciable amount
        final remainingDepreciation = max(0.0, input.depreciableAmount - cumulativeDepreciation);
        yearlyDepreciation = min(yearlyDepreciation, remainingDepreciation);
        
        cumulativeDepreciation += yearlyDepreciation;
        final bookValue = input.assetCost - cumulativeDepreciation;
        
        // Round values to avoid floating-point precision issues
        final roundedAnnuity = FloatingPointUtils.roundToPrecision(yearlyDepreciation, 2);
        final roundedCumulative = FloatingPointUtils.roundToPrecision(cumulativeDepreciation, 2);
        final roundedBookValue = FloatingPointUtils.roundToPrecision(bookValue, 2);
        
        final rate = FloatingPointUtils.safeDivide(yearlyDepreciation, input.assetCost) * 100;
        
        // Generate calendar year label
        final yearLabel = _generateCalendarYearLabel(input.acquisitionDate, year + 1, monthsInYear);
        
        rows.add(AmortizationRow(
          year: input.acquisitionDate.year + year,
          yearLabel: yearLabel,
          baseAmount: input.assetCost,
          rate: FloatingPointUtils.roundToPrecision(rate, 2), // Convert to percentage
          annuity: roundedAnnuity,
          cumulativeAnnuity: roundedCumulative,
          netBookValue: roundedBookValue,
        ));
        
        // Stop if fully depreciated or no more units to process
        if (FloatingPointUtils.areEqual(cumulativeDepreciation, input.depreciableAmount) ||
            FloatingPointUtils.isZero(remainingDepreciation)) {
          break;
        }
      }
      
      return rows;
    } catch (e) {
      developer.log('Units of production calculation failed: $e');
      rethrow;
    }
  }

  /// Calculate fiscal depreciation using fiscal method
  List<AmortizationRow> calculateFiscalDepreciation(EnhancedDepreciationInput input) {
    if (input.fiscalMethod == null) {
      throw ArgumentError('Méthode fiscale requise pour le calcul fiscal');
    }
    
    // Create input with fiscal method and parameters
    final fiscalInput = input.copyWith(
      method: input.fiscalMethod!,
      degressiveCoefficient: input.fiscalDegressiveCoefficient ?? input.degressiveCoefficient,
    );
    
    // Calculate using fiscal method
    switch (input.fiscalMethod!) {
      case DepreciationMethod.linear:
        return calculateLinearDepreciation(fiscalInput);
      case DepreciationMethod.degressive:
        return calculateDegressiveDepreciation(fiscalInput);
      case DepreciationMethod.sumOfYearsDigits:
        return calculateSumOfYearsDigits(fiscalInput);
      case DepreciationMethod.unitsOfProduction:
        return calculateUnitsOfProduction(fiscalInput);
    }
  }
  
  /// Calculate derogatory amortization from accounting and fiscal tables
  List<AmortizationRow> calculateDerogatoryAmortization(
    List<AmortizationRow> accountingRows, 
    List<AmortizationRow> fiscalRows
  ) {
    if (accountingRows.length != fiscalRows.length) {
      throw ArgumentError('Les tableaux comptable et fiscal doivent avoir la même longueur');
    }
    
    final derogatoryRows = <AmortizationRow>[];
    double runningProvisionBalance = 0.0;
    
    for (int i = 0; i < accountingRows.length; i++) {
      final accounting = accountingRows[i];
      final fiscal = fiscalRows[i];
      
      // Calculate derogatory movement
      final derogatoryMovement = fiscal.annuity - accounting.annuity;
      
      double? derogationProvision;
      double? derogationReprise;
      
      if (derogatoryMovement > 0) {
        // Fiscal > Accounting: Provision (dotation)
        derogationProvision = derogatoryMovement;
      } else if (derogatoryMovement < 0) {
        // Fiscal < Accounting: Reprise
        derogationReprise = -derogatoryMovement;
      }
      
      // Update running provision balance
      runningProvisionBalance += derogatoryMovement;
      
      derogatoryRows.add(AmortizationRow(
        year: accounting.year,
        yearLabel: accounting.yearLabel,
        baseAmount: accounting.baseAmount,
        rate: 0.0, // Not applicable for derogatory
        annuity: 0.0, // Not applicable for derogatory
        cumulativeAnnuity: 0.0, // Not applicable for derogatory
        netBookValue: accounting.netBookValue, // Same as accounting
        accountingAmortization: accounting.annuity,
        fiscalAmortization: fiscal.annuity,
        derogationProvision: derogationProvision,
        derogationReprise: derogationReprise,
        provisionBalance: FloatingPointUtils.roundToPrecision(runningProvisionBalance, 2),
      ));
    }
    
    return derogatoryRows;
  }

  /// Compare depreciation methods without causing infinite recursion
  List<DepreciationComparison> compareDepreciationMethods(EnhancedDepreciationInput input) {
    final comparisons = <DepreciationComparison>[];
    
    developer.log('Starting depreciation methods comparison');
    
    // Calculate for each method - FIXED: Use includeComparisons: false to prevent recursion
    for (final method in DepreciationMethod.values) {
      try {
        // Skip units of production if data not available
        if (method == DepreciationMethod.unitsOfProduction && 
            input.totalUnits == null) {
          developer.log('Skipping units of production method - no units data');
          continue;
        }
        
        // Skip degressive if coefficient not available
        if (method == DepreciationMethod.degressive && 
            input.degressiveCoefficient == null) {
          developer.log('Skipping degressive method - no coefficient');
          continue;
        }
        
        final methodInput = input.copyWith(method: method);
        
        // CRITICAL FIX: Set includeComparisons to false to prevent infinite recursion
        final result = calculateDepreciation(methodInput, includeComparisons: false);
        
        final firstYearDepreciation = result.amortizationTable.isNotEmpty
            ? result.amortizationTable.first.annuity
            : 0.0;
        
        final npv = _calculateNetPresentValue(result.amortizationTable, 0.08); // 8% discount rate
        final taxBenefit = _calculateTaxBenefit(result.amortizationTable, 0.31); // 31% tax rate
        
        comparisons.add(DepreciationComparison(
          method: method,
          totalDepreciation: result.summary.totalDepreciation,
          firstYearDepreciation: firstYearDepreciation,
          netPresentValue: npv,
          taxBenefit: taxBenefit,
          recommendation: _getMethodRecommendation(method, result),
          calculationSuccessful: true,
        ));
        
        developer.log('Successfully calculated comparison for ${method.displayName}');
        
      } catch (e, stackTrace) {
        developer.log('Failed to calculate ${method.displayName}: $e', error: e, stackTrace: stackTrace);
        
        // Add failed comparison instead of skipping
        comparisons.add(DepreciationComparison.failed(
          method: method,
          error: e.toString(),
        ));
      }
    }
    
    developer.log('Completed depreciation methods comparison with ${comparisons.length} results');
    return comparisons;
  }

  DepreciationSummary _createSummary(List<AmortizationRow> table, EnhancedDepreciationInput input) {
    final totalDepreciation = table.fold<double>(0, (sum, row) => sum + row.annuity);
    final remainingValue = input.assetCost - totalDepreciation;
    final averageAnnual = table.isNotEmpty ? totalDepreciation / table.length : 0.0;
    
    return DepreciationSummary(
      totalDepreciation: totalDepreciation,
      remainingValue: remainingValue,
      averageAnnualDepreciation: averageAnnual,
      totalYears: table.length,
      method: input.method,
      firstYearDepreciation: table.isNotEmpty ? table.first.annuity : 0,
      lastYearDepreciation: table.isNotEmpty ? table.last.annuity : 0,
    );
  }

  /// Generate tax optimization advice - FIXED: Accept pre-calculated comparisons to prevent recursion
  TaxOptimizationAdvice _generateTaxOptimizationAdvice(
    EnhancedDepreciationInput input, 
    List<AmortizationRow> table,
    List<DepreciationComparison>? preCalculatedComparisons,
  ) {
    try {
      List<DepreciationComparison> comparisons;
      
      // CRITICAL FIX: Use pre-calculated comparisons if provided, otherwise calculate them
      if (preCalculatedComparisons != null) {
        comparisons = preCalculatedComparisons;
        developer.log('Using pre-calculated comparisons for tax advice');
      } else {
        developer.log('Calculating comparisons for tax advice');
        comparisons = compareDepreciationMethods(input);
      }
      
      // Filter out failed calculations
      final validComparisons = comparisons.where((c) => c.calculationSuccessful).toList();
      
      if (validComparisons.isEmpty) {
        developer.log('No valid comparisons available for tax advice');
        return const TaxOptimizationAdvice(
          recommendedMethod: 'Linéaire',
          estimatedTaxSavings: 0,
          reasoning: 'Analyse non disponible - aucune méthode calculable',
          considerations: ['Vérifiez les paramètres d\'entrée'],
          implementationTiming: 'Immédiat',
        );
      }
      
      // Find method with highest NPV of tax benefits
      validComparisons.sort((a, b) => b.taxBenefit.compareTo(a.taxBenefit));
      final bestMethod = validComparisons.first;
      
      final considerations = <String>[
        'Conformité avec la réglementation comptable marocaine',
        'Impact sur la trésorerie de l\'entreprise',
        'Cohérence avec la stratégie fiscale globale',
      ];
      
      if (input.method != bestMethod.method) {
        considerations.add('Changement de méthode nécessaire');
      }
      
      // Add method-specific considerations
      switch (bestMethod.method) {
        case DepreciationMethod.degressive:
          considerations.add('Nécessite un coefficient dégressif approprié');
          considerations.add('Plus avantageux pour les actifs technologiques');
          break;
        case DepreciationMethod.unitsOfProduction:
          considerations.add('Nécessite un suivi précis de la production');
          considerations.add('Idéal pour les équipements de production');
          break;
        case DepreciationMethod.sumOfYearsDigits:
          considerations.add('Complexité de calcul plus élevée');
          break;
        case DepreciationMethod.linear:
          considerations.add('Simplicité de gestion et de contrôle');
          break;
      }
      
      return TaxOptimizationAdvice(
        recommendedMethod: bestMethod.method.displayName,
        estimatedTaxSavings: FloatingPointUtils.roundToPrecision(bestMethod.taxBenefit, 2),
        reasoning: _getOptimizationReasoning(bestMethod, input),
        considerations: considerations,
        implementationTiming: 'Début d\'exercice comptable',
      );
      
    } catch (e) {
      developer.log('Tax optimization advice generation failed: $e');
      
      // Return fallback advice
      return TaxOptimizationAdvice(
        recommendedMethod: input.method.displayName,
        estimatedTaxSavings: 0,
        reasoning: 'Erreur lors de l\'analyse: ${e.toString()}',
        considerations: ['Consultez un expert comptable'],
        implementationTiming: 'À déterminer',
      );
    }
  }

  double _calculateNetPresentValue(List<AmortizationRow> table, double discountRate) {
    try {
      if (table.isEmpty) return 0.0;
      
      // Validate discount rate
      if (discountRate < 0 || discountRate > 1) {
        throw ArgumentError('Taux d\'actualisation invalide: $discountRate');
      }
      
      double npv = 0;
      for (final row in table) {
        if (row.year <= 0) continue; // Skip invalid years
        
        final discountFactor = pow(1 + discountRate, row.year);
        if (discountFactor == 0) continue; // Avoid division by zero
        
        final presentValue = FloatingPointUtils.safeDivide(row.annuity, discountFactor.toDouble());
        
        if (FloatingPointUtils.isReasonableFinancialValue(presentValue)) {
          npv += presentValue;
        }
      }
      
      return FloatingPointUtils.roundToPrecision(npv, 2);
    } catch (e) {
      developer.log('NPV calculation failed: $e');
      return 0.0;
    }
  }

  double _calculateTaxBenefit(List<AmortizationRow> table, double taxRate) {
    try {
      if (table.isEmpty) return 0.0;
      
      // Validate tax rate
      if (taxRate < 0 || taxRate > 1) {
        throw ArgumentError('Taux d\'imposition invalide: $taxRate');
      }
      
      double totalBenefit = 0;
      const discountRate = 0.08; // 8% discount rate
      
      for (final row in table) {
        if (row.year <= 0) continue; // Skip invalid years
        
        final yearlyBenefit = row.annuity * taxRate;
        final discountFactor = pow(1 + discountRate, row.year);
        
        if (discountFactor == 0) continue; // Avoid division by zero
        
        final presentValue = FloatingPointUtils.safeDivide(yearlyBenefit, discountFactor.toDouble());
        
        if (FloatingPointUtils.isReasonableFinancialValue(presentValue)) {
          totalBenefit += presentValue;
        }
      }
      
      return FloatingPointUtils.roundToPrecision(totalBenefit, 2);
    } catch (e) {
      developer.log('Tax benefit calculation failed: $e');
      return 0.0;
    }
  }

  String _getMethodRecommendation(DepreciationMethod method, EnhancedDepreciationResult result) {
    switch (method) {
      case DepreciationMethod.linear:
        return 'Simplicité et conformité, idéal pour les actifs à usage régulier';
      case DepreciationMethod.degressive:
        return 'Optimise la trésorerie en début de période, adapté aux actifs technologiques';
      case DepreciationMethod.sumOfYearsDigits:
        return 'Compromis entre linéaire et dégressif, pour actifs à dépréciation rapide';
      case DepreciationMethod.unitsOfProduction:
        return 'Reflète l\'usage réel, idéal pour les équipements de production';
    }
  }

  String _getOptimizationReasoning(DepreciationComparison bestMethod, EnhancedDepreciationInput input) {
    try {
      final buffer = StringBuffer();
      
      buffer.writeln('La méthode ${bestMethod.method.displayName} est recommandée car :');
      
      // Format financial values safely
      final taxBenefit = FloatingPointUtils.roundToPrecision(bestMethod.taxBenefit, 0);
      final npv = FloatingPointUtils.roundToPrecision(bestMethod.netPresentValue, 0);
      final firstYear = FloatingPointUtils.roundToPrecision(bestMethod.firstYearDepreciation, 0);
      
      buffer.writeln('• Avantage fiscal estimé : ${taxBenefit.toStringAsFixed(0)} DH');
      buffer.writeln('• Valeur actuelle nette : ${npv.toStringAsFixed(0)} DH');
      buffer.writeln('• Dépréciation première année : ${firstYear.toStringAsFixed(0)} DH');
      
      // Add method-specific reasoning
      switch (bestMethod.method) {
        case DepreciationMethod.degressive:
          buffer.writeln('• Améliore la trésorerie en début de période');
          buffer.writeln('• Particulièrement adapté aux actifs technologiques');
          break;
        case DepreciationMethod.linear:
          buffer.writeln('• Simplicité de gestion et conformité assurée');
          buffer.writeln('• Prévisibilité des charges d\'amortissement');
          break;
        case DepreciationMethod.sumOfYearsDigits:
          buffer.writeln('• Compromis entre simplicité et optimisation fiscale');
          buffer.writeln('• Adapté aux actifs à dépréciation rapide');
          break;
        case DepreciationMethod.unitsOfProduction:
          buffer.writeln('• Reflète l\'usage réel de l\'actif');
          buffer.writeln('• Idéal pour les équipements de production');
          break;
      }
      
      // Add comparison context if current method is different
      if (input.method != bestMethod.method) {
        final currentMethodName = input.method.displayName;
        buffer.writeln('• Avantage par rapport à la méthode $currentMethodName actuellement utilisée');
      }
      
      return buffer.toString();
    } catch (e) {
      developer.log('Optimization reasoning generation failed: $e');
      return 'Méthode recommandée: ${bestMethod.method.displayName}';
    }
  }

  /// Map an asset type or category string to Moroccan chart of accounts (charge, amortization)
  /// This mapping is tolerant to various input formats (English/French, presets, categories).
  Map<String, String> _getAccountsForAssetType(String assetType) {
    final key = assetType.toLowerCase().trim();

    // Common keyword-based mapping to handle both preset categories and explicit keys
    if (key.contains('bâtiment') || key.contains('batiment') || key.contains('immobilier') || key.contains('construction') || key.contains('build')) {
      return {'charge': '6193', 'amortization': '28111'}; // Constructions
    }

    if (key.contains('installation') || key.contains('technique') || key.contains('installations') || key.contains('tech')) {
      return {'charge': '6192', 'amortization': '28121'}; // Installations techniques, matériel et outillage
    }

    if (key.contains('transport') || key.contains('véhicule') || key.contains('vehicule') || key.contains('auto')) {
      return {'charge': '6195', 'amortization': '28131'}; // Matériel de transport
    }

    if (key.contains('mobilier') || key.contains('bureau') || key.contains('furniture') || key.contains('office')) {
      return {'charge': '6194', 'amortization': '28141'}; // Mobilier, matériel de bureau
    }

    if (key.contains('informatique') || key.contains('computer') || key.contains('it') || key.contains('informat')) {
      return {'charge': '6196', 'amortization': '28151'}; // Matériel informatique
    }

    if (key.contains('production') || key.contains('machine') || key.contains('outillage') || key.contains('manufact')) {
      return {'charge': '6192', 'amortization': '28121'}; // Equipment / machines
    }

    if (key.contains('terrain') || key.contains('aménagement') || key.contains('land')) {
      return {'charge': '6193', 'amortization': '28101'}; // Terrains aménagés
    }

    if (key.contains('incorporelle') || key.contains('intangible')) {
      return {'charge': '6197', 'amortization': '28110'}; // Immobilisations incorporelles (use 2811x)
    }

    // Fallback generic accounts
    return {'charge': '6191', 'amortization': '28111'};
  }

  /// Generate accounting journal entries from the amortization table using Moroccan chart mapping
  /// This is a public method that can be used by both widget and providers
  List<JournalEntry> generateJournalEntries(List<AmortizationRow> table, EnhancedDepreciationInput input) {
    return _generateJournalEntriesFromTable(table, input);
  }

  /// Get the appropriate chart accounts for an asset type
  /// Returns a tuple of (chargeAccount, amortizationAccount)
  (String, String) getAccountsForAsset(String assetType) {
    final accounts = _getAccountsForAssetType(assetType);
    return (accounts['charge'] ?? '6191', accounts['amortization'] ?? '28111');
  }

  /// Generate accounting journal entries from the amortization table using Moroccan chart mapping
  List<JournalEntry> _generateJournalEntriesFromTable(List<AmortizationRow> table, EnhancedDepreciationInput input) {
    if (table.isEmpty) return [];

    final accounts = _getAccountsForAssetType(input.assetType);
    final chargeAccount = accounts['charge'] ?? '6191';
    final amortizationAccount = accounts['amortization'] ?? '28111';

    final entries = <JournalEntry>[];

    for (final row in table) {
      // Use the row's label if available; otherwise fall back to year number
      final dateLabel = row.yearLabel.isNotEmpty ? row.yearLabel : 'Année ${row.year}';
      final lines = <JournalLine>[];

      // Standard amortization entries
      if (row.annuity > 0) {
        final amountStr = row.annuity.toStringAsFixed(2);
        lines.addAll([
          JournalLine(
            account: chargeAccount,
            label: 'Dotation aux amortissements $dateLabel',
            debit: amountStr,
            credit: null,
          ),
          JournalLine(
            account: amortizationAccount,
            label: 'Cumul amortissements $dateLabel',
            debit: null,
            credit: amountStr,
          ),
        ]);
      }

      // Derogatory entries if present
      if (row.hasDerogation) {
        if (row.derogationProvision != null && row.derogationProvision! > 0) {
          final provisionStr = row.derogationProvision!.toStringAsFixed(2);
          lines.addAll([
            JournalLine(
              account: '65941',
              label: 'Dotation amortissement dérogatoire $dateLabel',
              debit: provisionStr,
              credit: null,
            ),
            JournalLine(
              account: '1351',
              label: 'Provision amortissement dérogatoire $dateLabel',
              debit: null,
              credit: provisionStr,
            ),
          ]);
        }

        if (row.derogationReprise != null && row.derogationReprise! > 0) {
          final repriseStr = row.derogationReprise!.toStringAsFixed(2);
          lines.addAll([
            JournalLine(
              account: '1351',
              label: 'Reprise amortissement dérogatoire $dateLabel',
              debit: repriseStr,
              credit: null,
            ),
            JournalLine(
              account: '75941',
              label: 'Reprise amortissement dérogatoire $dateLabel',
              debit: null,
              credit: repriseStr,
            ),
          ]);
        }
      }

      if (lines.isNotEmpty) {
        entries.add(JournalEntry(
          date: dateLabel,
          lines: lines,
        ));
      }
    }

    return entries;
  }
}
