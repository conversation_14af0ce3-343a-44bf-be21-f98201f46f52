import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:developer' as developer;
import '../../models/calculators/enhanced_depreciation_data.dart';
import '../../services/enhanced_depreciation_service.dart';
import '../../utils/calculation_utils.dart' as calc_utils;
import '../../theme/design_tokens.dart';
import '../../theme/responsive_breakpoints.dart';
import '../../providers/calculator_providers.dart';
import '../custom_text_field.dart';
import '../journal_comptable_widget.dart';
import 'guided_wizard_scaffold.dart';

class EnhancedDepreciationCalculator extends ConsumerStatefulWidget {
  const EnhancedDepreciationCalculator({super.key});

  @override
  ConsumerState<EnhancedDepreciationCalculator> createState() => _EnhancedDepreciationCalculatorState();
}

class _EnhancedDepreciationCalculatorState extends ConsumerState<EnhancedDepreciationCalculator>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Form Controllers
  final _assetNameController = TextEditingController();
  final _assetCostController = TextEditingController();
  final _residualValueController = TextEditingController();
  final _usefulLifeController = TextEditingController();
  final _degressiveCoefficientController = TextEditingController();
  final _totalUnitsController = TextEditingController();
  
  // Annual units controllers for units of production method
  final List<TextEditingController> _annualUnitsControllers = [];
  
  DepreciationMethod _selectedMethod = DepreciationMethod.linear;
  DateTime _acquisitionDate = DateTime.now();
  bool _midYearConvention = false;
  bool _comparisonMode = false;
  AssetPreset? _selectedPreset;
  AssetType? _selectedAssetType;
  
  // Derogatory calculation fields
  bool _enableDerogatoire = false;
  DepreciationMethod? _fiscalMethod;
  final _fiscalDegressiveCoefficientController = TextEditingController();
  
  EnhancedDepreciationResult? _result;
  List<DepreciationComparison>? _comparisons;
  List<JournalEntry> _journalEntries = [];
  bool _isCalculating = false;
  String _calculationProgress = '';
  Timer? _calculationTimeout;
  Completer<void>? _calculationCompleter;
  String? _lastError;
  
  // Progressive disclosure states
  bool _showAdvancedOptions = false;
  bool _showDerogatoryOptions = false;
  bool _showAnnualUnitsInput = false;
  
  // Mobile wizard states
  int _currentWizardStep = 0;
  PageController? _wizardPageController;
  
  // Form persistence
  Timer? _autoSaveTimer;
  bool _isFormDirty = false;
  
  // Calculation timeout duration
  static const Duration _maxCalculationDuration = Duration(seconds: 30);
  static const Duration _autoSaveInterval = Duration(seconds: 3);
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _wizardPageController = PageController();
    _loadSavedFormData();
    _setupAutoSave();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _wizardPageController?.dispose();
    _calculationTimeout?.cancel();
    _autoSaveTimer?.cancel();
    _calculationCompleter?.complete();
    _disposeControllers();
    super.dispose();
  }
  
  void _disposeControllers() {
    _assetNameController.dispose();
    _assetCostController.dispose();
    _residualValueController.dispose();
    _usefulLifeController.dispose();
    _degressiveCoefficientController.dispose();
    _totalUnitsController.dispose();
    _fiscalDegressiveCoefficientController.dispose();
    
    // Dispose annual units controllers
    for (final controller in _annualUnitsControllers) {
      controller.dispose();
    }
    _annualUnitsControllers.clear();
  }
  
  // Form persistence methods
  void _setupAutoSave() {
    _autoSaveTimer = Timer.periodic(_autoSaveInterval, (timer) {
      if (_isFormDirty) {
        _saveFormData();
        _isFormDirty = false;
      }
    });
  }
  
  void _markFormDirty() {
    _isFormDirty = true;
  }
  
  Future<void> _saveFormData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('enhanced_calc_asset_name', _assetNameController.text);
      await prefs.setString('enhanced_calc_asset_cost', _assetCostController.text);
      await prefs.setString('enhanced_calc_residual_value', _residualValueController.text);
      await prefs.setString('enhanced_calc_useful_life', _usefulLifeController.text);
      await prefs.setString('enhanced_calc_degressive_coeff', _degressiveCoefficientController.text);
      await prefs.setString('enhanced_calc_total_units', _totalUnitsController.text);
      await prefs.setString('enhanced_calc_method', _selectedMethod.name);
      await prefs.setString('enhanced_calc_acquisition_date', _acquisitionDate.toIso8601String());
      await prefs.setBool('enhanced_calc_mid_year', _midYearConvention);
      await prefs.setBool('enhanced_calc_comparison_mode', _comparisonMode);
      await prefs.setBool('enhanced_calc_enable_derogatoire', _enableDerogatoire);
    } catch (e) {
      developer.log('Error saving form data: $e');
    }
  }
  
  Future<void> _loadSavedFormData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _assetNameController.text = prefs.getString('enhanced_calc_asset_name') ?? '';
        _assetCostController.text = prefs.getString('enhanced_calc_asset_cost') ?? '';
        _residualValueController.text = prefs.getString('enhanced_calc_residual_value') ?? '';
        _usefulLifeController.text = prefs.getString('enhanced_calc_useful_life') ?? '';
        _degressiveCoefficientController.text = prefs.getString('enhanced_calc_degressive_coeff') ?? '';
        _totalUnitsController.text = prefs.getString('enhanced_calc_total_units') ?? '';
        
        final methodName = prefs.getString('enhanced_calc_method');
        if (methodName != null) {
          _selectedMethod = DepreciationMethod.values.firstWhere(
            (method) => method.name == methodName,
            orElse: () => DepreciationMethod.linear,
          );
        }
        
        final dateString = prefs.getString('enhanced_calc_acquisition_date');
        if (dateString != null) {
          _acquisitionDate = DateTime.parse(dateString);
        }
        
        _midYearConvention = prefs.getBool('enhanced_calc_mid_year') ?? false;
        _comparisonMode = prefs.getBool('enhanced_calc_comparison_mode') ?? false;
        _enableDerogatoire = prefs.getBool('enhanced_calc_enable_derogatoire') ?? false;
      });
    } catch (e) {
      developer.log('Error loading saved form data: $e');
    }
  }
  
  // Mobile wizard navigation methods
  void _nextWizardStep() {
    if (_currentWizardStep < 2) {
      setState(() {
        _currentWizardStep++;
      });
      _wizardPageController?.animateToPage(
        _currentWizardStep,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
  
  void _previousWizardStep() {
    if (_currentWizardStep > 0) {
      setState(() {
        _currentWizardStep--;
      });
      _wizardPageController?.animateToPage(
        _currentWizardStep,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final isMobile = context.isMobile;

    return Scaffold(
      body: isMobile ? _buildMobileWizard() : _buildResponsiveLayout(),
      floatingActionButton: _buildCalculateButton(),
    );
  }
  
  Widget _buildMobileWizard() {
    return GuidedWizardScaffold(
      title: 'Calculateur Avancé',
      currentStep: _currentWizardStep,
      totalSteps: 3,
      onNext: _currentWizardStep < 2 ? _nextWizardStep : null,
      onPrevious: _currentWizardStep > 0 ? _previousWizardStep : null,
      onFinish: _currentWizardStep == 2 ? () => _performCalculation() : null,
      steps: [
        WizardStep(
          title: 'Détails de l\'Actif',
          child: _buildAssetDetailsStep(),
        ),
        WizardStep(
          title: 'Méthode et Options',
          child: _buildMethodOptionsStep(),
        ),
        WizardStep(
          title: 'Résultats',
          child: _buildResultsStep(),
        ),
      ],
    );
  }
  
  // Extracted step components for mobile wizard
  Widget _buildAssetDetailsStep() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAssetPresetSection(),
            const SizedBox(height: 16),
            _buildAssetDetailsSection(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMethodOptionsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMethodSelectionSection(),
          const SizedBox(height: 16),
          _buildAdvancedOptionsSection(),
        ],
      ),
    );
  }
  
  Widget _buildResultsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_result != null) _buildResultsDisplay(),
          if (_journalEntries.isNotEmpty) _buildJournalDisplay(),
          if (_comparisons != null) _buildComparisonsDisplay(),
        ],
      ),
    );
  }

  Widget _buildResponsiveLayout() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: _buildInputTab(),
        ),
        Expanded(
          flex: 1,
          child: Column(
            children: [
              Expanded(child: _buildResultsTab()),
              Expanded(child: _buildJournalTab()),
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: _buildComparisonTab(),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: DesignTokens.colorSurface,
      child: TabBar(
        controller: _tabController,
        labelColor: DesignTokens.colorPrimary,
        unselectedLabelColor: DesignTokens.colorOnSurfaceVariant,
        indicatorColor: DesignTokens.colorPrimary,
        tabs: const [
          Tab(
            icon: Icon(Icons.input),
            text: 'Saisie',
          ),
          Tab(
            icon: Icon(Icons.table_chart),
            text: 'Résultats',
          ),
          Tab(
            icon: Icon(Icons.book),
            text: 'Écritures',
          ),
          Tab(
            icon: Icon(Icons.compare),
            text: 'Comparaison',
          ),
        ],
      ),
    );
  }
  
  Widget _buildInputTab() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: context.responsivePadding(
          mobile: const EdgeInsets.all(12),
          tablet: const EdgeInsets.all(16),
          desktop: const EdgeInsets.all(20),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAssetPresetSection(),
            SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
            _buildAssetDetailsSection(),
            SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
            _buildMethodSelectionSection(),
            SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
            _buildAdvancedOptionsSection(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAssetPresetSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Modèles d\'Actifs',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<AssetPreset>(
              value: _selectedPreset,
              decoration: const InputDecoration(
                labelText: 'Sélectionner un modèle (optionnel)',
                border: OutlineInputBorder(),
              ),
              items: AssetPreset.commonPresets.map((preset) {
                return DropdownMenuItem(
                  value: preset,
                  child: Text(
                    '${preset.name} - ${preset.description}',
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                );
              }).toList(),
              onChanged: (preset) {
                setState(() {
                  _selectedPreset = preset;
                  if (preset != null) {
                    _applyPreset(preset);
                  }
                });
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAssetDetailsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Détails de l\'Actif',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _assetNameController,
              label: 'Nom de l\'actif',
              validator: (value) => value?.isEmpty == true ? 'Ce champ est requis' : null,
              onChanged: (value) => _onAssetNameChanged(value),
            ),
            const SizedBox(height: 12),
            CustomTextField(
              controller: _assetCostController,
              label: 'Coût d\'acquisition (DH)',
              keyboardType: TextInputType.number,
              validator: _validateAssetCost,
              onChanged: (value) => _validateFormRealTime(),
            ),
            const SizedBox(height: 12),
            CustomTextField(
              controller: _residualValueController,
              label: 'Valeur résiduelle (DH)',
              keyboardType: TextInputType.number,
              validator: _validateResidualValue,
              onChanged: (value) => _validateFormRealTime(),
            ),
            const SizedBox(height: 12),
            CustomTextField(
              controller: _usefulLifeController,
              label: 'Durée d\'utilité (années)',
              keyboardType: TextInputType.number,
              validator: _validatePositiveInteger,
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<AssetType>(
              value: _selectedAssetType,
              decoration: const InputDecoration(
                labelText: 'Type d\'actif (optionnel)',
                border: OutlineInputBorder(),
                helperText: 'Sélectionner pour un mapping automatique des comptes',
              ),
              items: AssetType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        type.displayName,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        type.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (type) {
                setState(() {
                  _selectedAssetType = type;
                });
              },
            ),
            const SizedBox(height: 12),
            ListTile(
              title: const Text('Date d\'acquisition *'),
              subtitle: Text(_formatDate(_acquisitionDate)),
              trailing: const Icon(Icons.calendar_today),
              onTap: _selectAcquisitionDate,
            ),
            const SizedBox(height: 8),
            Text(
              '* Requis pour le calcul du prorata temporis',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMethodSelectionSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Méthode d\'Amortissement',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            ...DepreciationMethod.values.map((method) {
              return RadioListTile<DepreciationMethod>(
                title: Text(method.displayName),
                subtitle: Text(method.description),
                value: method,
                groupValue: _selectedMethod,
                onChanged: (value) {
                  setState(() {
                    _selectedMethod = value!;
                  });
                },
              );
            }),
            if (_selectedMethod.requiresCoefficient) ...[
              const SizedBox(height: 12),
              CustomTextField(
                controller: _degressiveCoefficientController,
                label: 'Coefficient dégressif',
                keyboardType: TextInputType.number,
                validator: _validateDegressiveCoefficient,
                hint: '2.0 (par défaut)',
              ),
            ],
            if (_selectedMethod.requiresUnits) ...[
              const SizedBox(height: 12),
              CustomTextField(
                controller: _totalUnitsController,
                label: 'Total d\'unités de production',
                keyboardType: TextInputType.number,
                validator: _validateTotalUnits,
                onChanged: (value) => _updateAnnualUnitsFields(),
              ),
              const SizedBox(height: 12),
              _buildAnnualUnitsSection(),
            ],
          ],
        ),
      ),
    );
  }
  
  // Helper methods for progressive disclosure and wizard
  void _setupAnnualUnitsInputs() {
    final usefulLife = int.tryParse(_usefulLifeController.text);
    if (usefulLife != null) {
      // Ensure we have the right number of controllers
      while (_annualUnitsControllers.length < usefulLife) {
        _annualUnitsControllers.add(TextEditingController());
      }
      while (_annualUnitsControllers.length > usefulLife) {
        _annualUnitsControllers.removeLast().dispose();
      }
    }
  }
  
  List<Widget> _buildAnnualUnitsInputs() {
    final usefulLife = int.tryParse(_usefulLifeController.text);
    if (usefulLife == null || usefulLife <= 0 || _annualUnitsControllers.isEmpty) {
      return [
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Veuillez d\'abord définir la durée d\'utilité',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      ];
    }
    
    return List.generate(usefulLife, (index) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: CustomTextField(
          controller: _annualUnitsControllers[index],
          label: 'Année ${index + 1}',
          keyboardType: TextInputType.number,
          onChanged: (value) => _markFormDirty(),
        ),
      );
    });
  }
  
  // Add results display methods for wizard steps
  Widget _buildResultsDisplay() {
    if (_result == null) return const SizedBox();
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résultats du Calcul',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            // Summary information
            _buildSummaryCards(),
            const SizedBox(height: 16),
            // Amortization table
            if (_result!.amortizationTable.isNotEmpty)
              _buildAmortizationTableWidget(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildJournalDisplay() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: JournalComptableWidget(
          title: 'Écritures Comptables',
          entries: _journalEntries,
        ),
      ),
    );
  }
  
  Widget _buildComparisonsDisplay() {
    if (_comparisons == null || _comparisons!.isEmpty) return const SizedBox();
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Comparaison des Méthodes',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            // Build comparison table
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Méthode')),
                  DataColumn(label: Text('Total')),
                  DataColumn(label: Text('1ère année')),
                  DataColumn(label: Text('Avantage fiscal')),
                ],
                rows: _comparisons!.map((comparison) {
                  return DataRow(
                    cells: [
                      DataCell(Text(comparison.method.displayName)),
                      DataCell(Text(calc_utils.CalculationUtils.formatMonetary(comparison.totalDepreciation))),
                      DataCell(Text(calc_utils.CalculationUtils.formatMonetary(comparison.firstYearDepreciation))),
                      DataCell(Text(calc_utils.CalculationUtils.formatMonetary(comparison.taxBenefit))),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAdvancedOptionsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic options - always visible
            SwitchListTile(
              title: const Text('Mode comparaison'),
              subtitle: const Text('Comparer toutes les méthodes d\'amortissement'),
              value: _comparisonMode,
              onChanged: (value) {
                setState(() {
                  _comparisonMode = value;
                  _markFormDirty();
                });
              },
            ),
            
            // Progressive disclosure for advanced options
            ExpansionTile(
              title: Text(
                'Options Avancées',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DesignTokens.colorPrimary,
                ),
              ),
              leading: Icon(
                _showAdvancedOptions ? Icons.expand_less : Icons.expand_more,
                color: DesignTokens.colorPrimary,
              ),
              initiallyExpanded: _showAdvancedOptions,
              onExpansionChanged: (expanded) {
                setState(() {
                  _showAdvancedOptions = expanded;
                });
              },
              children: [
                SwitchListTile(
                  title: const Text('Convention de mi-année'),
                  subtitle: const Text('Amortissement de 6 mois la première année'),
                  value: _midYearConvention,
                  onChanged: (value) {
                    setState(() {
                      _midYearConvention = value;
                      _markFormDirty();
                    });
                  },
                ),
                
                // Units of production annual input (progressive disclosure)
                if (_selectedMethod == DepreciationMethod.unitsOfProduction)
                  ExpansionTile(
                    title: const Text('Unités Annuelles'),
                    subtitle: const Text('Saisir la production prévue par année'),
                    initiallyExpanded: _showAnnualUnitsInput,
                    onExpansionChanged: (expanded) {
                      setState(() {
                        _showAnnualUnitsInput = expanded;
                        if (expanded) {
                          _setupAnnualUnitsInputs();
                        }
                      });
                    },
                    children: _buildAnnualUnitsInputs(),
                  ),
              ],
            ),
            
            // Derogatory amortization - progressive disclosure
            ExpansionTile(
              title: const Text('Amortissement Dérogatoire'),
              subtitle: const Text('Calcul des écarts fiscal/comptable'),
              leading: Icon(
                _showDerogatoryOptions ? Icons.expand_less : Icons.expand_more,
                color: DesignTokens.colorSecondary,
              ),
              initiallyExpanded: _showDerogatoryOptions,
              onExpansionChanged: (expanded) {
                setState(() {
                  _showDerogatoryOptions = expanded;
                  if (!expanded) {
                    _enableDerogatoire = false;
                    _fiscalMethod = null;
                    _fiscalDegressiveCoefficientController.clear();
                  }
                });
              },
              children: [
                SwitchListTile(
                  title: const Text('Activer le calcul dérogatoire'),
                  subtitle: const Text('Différence entre amortissement fiscal et comptable'),
                  value: _enableDerogatoire,
                  onChanged: (value) {
                    setState(() {
                      _enableDerogatoire = value;
                      if (!value) {
                        _fiscalMethod = null;
                        _fiscalDegressiveCoefficientController.clear();
                      }
                      _markFormDirty();
                    });
                  },
                ),
                if (_enableDerogatoire) ...[
                  const SizedBox(height: 12),
                  DropdownButtonFormField<DepreciationMethod>(
                    value: _fiscalMethod,
                    decoration: const InputDecoration(
                      labelText: 'Méthode fiscale *',
                      border: OutlineInputBorder(),
                      helperText: 'Méthode d\'amortissement pour le calcul fiscal',
                    ),
                    items: DepreciationMethod.values.map((method) {
                      return DropdownMenuItem(
                        value: method,
                        child: Text(method.displayName),
                      );
                    }).toList(),
                    onChanged: (method) {
                      setState(() {
                        _fiscalMethod = method;
                        if (method != DepreciationMethod.degressive) {
                          _fiscalDegressiveCoefficientController.clear();
                        }
                        _markFormDirty();
                      });
                    },
                    validator: (value) {
                      if (_enableDerogatoire && value == null) {
                        return 'Sélectionnez une méthode fiscale';
                      }
                      return null;
                    },
                  ),
                  if (_fiscalMethod == DepreciationMethod.degressive) ...[
                    const SizedBox(height: 12),
                    CustomTextField(
                      controller: _fiscalDegressiveCoefficientController,
                      label: 'Coefficient dégressif fiscal',
                      keyboardType: TextInputType.number,
                      validator: _validateFiscalDegressiveCoefficient,
                      hint: '2.0 (par défaut)',
                      onChanged: (value) => _markFormDirty(),
                    ),
                  ],
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildResultsTab() {
    if (_result == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.table_chart_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Configurez l\'amortissement et calculez\npour voir les résultats',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            if (_lastError != null) ...[
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 32),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Column(
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.error, color: Colors.red),
                        SizedBox(width: 8),
                        Text(
                          'Dernière erreur',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _lastError!,
                      style: TextStyle(color: Colors.red[700]),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCard(),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          _buildAmortizationTable(),
          if (_result!.taxAdvice != null) ...[
            SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
            _buildTaxAdviceCard(),
          ],
        ],
      ),
    );
  }
  
  Widget _buildJournalTab() {
    if (_journalEntries.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Effectuez un calcul d\'amortissement\npour voir les écritures comptables',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      child: JournalComptableWidget(
        title: 'Écritures comptables d\'amortissement',
        entries: _journalEntries,
      ),
    );
  }
  
  Widget _buildComparisonTab() {
    if (_comparisons == null || _comparisons!.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.compare_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Activez le mode comparaison\npour voir les différentes méthodes',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Comparaison des Méthodes',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.colorPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          ..._comparisons!.map((comparison) => _buildComparisonCard(comparison)),
        ],
      ),
    );
  }
  
  Widget _buildSummaryCard() {
    final summary = _result!.summary;
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résumé - ${summary.methodDisplayName}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            _buildSummaryItem('Amortissement total', summary.formattedTotalDepreciation),
            _buildSummaryItem('Valeur résiduelle', summary.formattedRemainingValue),
            _buildSummaryItem('Amortissement annuel moyen', summary.formattedAverageAnnual),
            _buildSummaryItem('Durée totale', '${summary.totalYears} années'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSummaryItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: DesignTokens.colorPrimary,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAmortizationTable() {
    if (_result!.hasMultipleTables) {
      return _buildMultipleTablesView();
    } else {
      return _buildSingleTableView();
    }
  }

  Widget _buildSingleTableView() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tableau d\'Amortissement',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            _buildDataTable(_result!.amortizationTable, false),
          ],
        ),
      ),
    );
  }

  Widget _buildMultipleTablesView() {
    return DefaultTabController(
      length: 3,
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tableaux d\'Amortissement',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DesignTokens.colorPrimary,
                ),
              ),
              const SizedBox(height: 16),
              const TabBar(
                labelColor: DesignTokens.colorPrimary,
                unselectedLabelColor: DesignTokens.colorOnSurfaceVariant,
                indicatorColor: DesignTokens.colorPrimary,
                tabs: [
                  Tab(text: 'Comptable'),
                  Tab(text: 'Fiscal'),
                  Tab(text: 'Dérogatoire'),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: TabBarView(
                  children: [
                    _buildLazyAccountingTable(),
                    _buildLazyFiscalTable(),
                    _buildLazyDerogatoryTable(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


  // Lazy loading implementations for performance optimization
  Widget _buildLazyAccountingTable() {
    final table = _result!.accountingAmortizationTable ?? _result!.amortizationTable;
    return _buildOptimizedDataTable(table, false, 'accounting');
  }

  Widget _buildLazyFiscalTable() {
    final table = _result!.fiscalAmortizationTable ?? [];
    if (table.isEmpty) {
      return const Center(
        child: Text('Aucune donnée fiscale disponible'),
      );
    }
    return _buildOptimizedDataTable(table, false, 'fiscal');
  }

  Widget _buildLazyDerogatoryTable() {
    final table = _result!.derogatoryAmortizationTable ?? [];
    if (table.isEmpty) {
      return const Center(
        child: Text('Aucune donnée dérogatoire disponible'),
      );
    }
    return _buildOptimizedDataTable(table, true, 'derogatory');
  }

  // Performance threshold for table optimization
  static const int _performanceThreshold = 20; // Switch to virtualization for tables > 20 rows

  Widget _buildOptimizedDataTable(List<dynamic> rows, bool isDerogatoryTable, String tableKey) {
    if (rows.isEmpty) {
      return const Center(
        child: Text('Aucune donnée disponible'),
      );
    }

    // For small tables, use regular DataTable for better UX
    if (rows.length <= _performanceThreshold) {
      return _buildDataTable(rows, isDerogatoryTable);
    }

    // For large tables, use virtualized solution
    return _buildVirtualizedTable(rows, isDerogatoryTable, tableKey);
  }

  Widget _buildVirtualizedTable(List<dynamic> rows, bool isDerogatoryTable, String tableKey) {
    return Column(
      children: [
        // Header row
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
          ),
          child: Row(
            children: _buildVirtualizedHeaderCells(isDerogatoryTable),
          ),
        ),
        // Virtualized content
        Expanded(
          child: ListView.builder(
            key: ValueKey('virtualized_table_$tableKey'),
            itemCount: rows.length,
            cacheExtent: 500, // Cache 500px worth of items
            itemExtent: 60, // Fixed height for better performance
            itemBuilder: (context, index) {
              return _buildVirtualizedTableRow(rows[index], isDerogatoryTable, index);
            },
          ),
        ),
        // Footer with row count info
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
            borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                '${rows.length} lignes • Tableau virtualisé pour les performances',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildVirtualizedHeaderCells(bool isDerogatoryTable) {
    final columns = isDerogatoryTable
        ? ['Année', 'Comptable', 'Fiscal', 'Dotations', 'Reprises', 'Solde Provision']
        : ['Année', 'Valeur Début', 'Amortissement', 'Cumul', 'Valeur Fin'];
    
    return columns.map((title) => Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    )).toList();
  }

  Widget _buildVirtualizedTableRow(dynamic row, bool isDerogatoryTable, int index) {
    // Apply alternating row colors for better readability
    final isEven = index % 2 == 0;
    final backgroundColor = isEven 
        ? Theme.of(context).colorScheme.surface
        : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3);

    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: isDerogatoryTable
            ? _buildVirtualizedDerogatoryRowCells(row)
            : _buildVirtualizedRegularRowCells(row),
      ),
    );
  }

  List<Widget> _buildVirtualizedDerogatoryRowCells(dynamic row) {
    return [
      _buildVirtualizedYearCell(row, flex: 2),
      _buildVirtualizedAmountCell('${(row.accountingAmortization ?? 0).toStringAsFixed(0)} DH', flex: 2),
      _buildVirtualizedAmountCell('${(row.fiscalAmortization ?? 0).toStringAsFixed(0)} DH', flex: 2),
      _buildVirtualizedDerogatoryCell(row.derogationProvision, true, flex: 2),
      _buildVirtualizedDerogatoryCell(row.derogationReprise, false, flex: 2),
      _buildVirtualizedAmountCell('${(row.provisionBalance ?? 0).toStringAsFixed(0)} DH', flex: 2),
    ];
  }

  List<Widget> _buildVirtualizedRegularRowCells(dynamic row) {
    return [
      _buildVirtualizedYearCell(row, flex: 2),
      _buildVirtualizedAmountCell('${row.baseAmount.toStringAsFixed(0)} DH', flex: 2),
      _buildVirtualizedAmountCell('${row.annuity.toStringAsFixed(0)} DH', flex: 2),
      _buildVirtualizedAmountCell('${row.cumulativeAnnuity.toStringAsFixed(0)} DH', flex: 2),
      _buildVirtualizedAmountCell('${row.netBookValue.toStringAsFixed(0)} DH', flex: 2),
    ];
  }

  Widget _buildVirtualizedYearCell(dynamic row, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          decoration: row.isPartialYear
              ? BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.orange[300]!),
                )
              : null,
          child: Text(
            row.yearLabel,
            style: TextStyle(
              fontWeight: row.isPartialYear ? FontWeight.bold : FontWeight.normal,
              color: row.isPartialYear ? Colors.orange[800] : null,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildVirtualizedAmountCell(String amount, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Text(
          amount,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.right,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildVirtualizedDerogatoryCell(double? amount, bool isProvision, {int flex = 1}) {
    if (amount == null || amount == 0) {
      return Expanded(
        flex: flex,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: const Text(
            '-',
            style: TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Expanded(
      flex: flex,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
          decoration: BoxDecoration(
            color: isProvision ? Colors.red[100] : Colors.green[100],
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${amount.toStringAsFixed(0)} DH',
            style: TextStyle(
              color: isProvision ? Colors.red[800] : Colors.green[800],
              fontWeight: FontWeight.bold,
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildDataTable(List<dynamic> rows, bool isDerogatoryTable) {
    if (rows.isEmpty) {
      return const Center(
        child: Text('Aucune donnée disponible'),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: _buildTableColumns(isDerogatoryTable),
        rows: rows.map((row) => _buildTableRow(row, isDerogatoryTable)).toList(),
      ),
    );
  }

  List<DataColumn> _buildTableColumns(bool isDerogatoryTable) {
    if (isDerogatoryTable) {
      return const [
        DataColumn(label: Text('Année')),
        DataColumn(label: Text('Comptable')),
        DataColumn(label: Text('Fiscal')),
        DataColumn(label: Text('Dotations')),
        DataColumn(label: Text('Reprises')),
        DataColumn(label: Text('Solde Provision')),
      ];
    } else {
      return const [
        DataColumn(label: Text('Année')),
        DataColumn(label: Text('Valeur Début')),
        DataColumn(label: Text('Amortissement')),
        DataColumn(label: Text('Cumul')),
        DataColumn(label: Text('Valeur Fin')),
      ];
    }
  }

  DataRow _buildTableRow(dynamic row, bool isDerogatoryTable) {
    // Handle calendar year display with partial year highlighting
    Widget yearCell = Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: row.isPartialYear
          ? BoxDecoration(
              color: Colors.orange[100],
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.orange[300]!),
            )
          : null,
      child: Text(
        row.yearLabel,
        style: TextStyle(
          fontWeight: row.isPartialYear ? FontWeight.bold : FontWeight.normal,
          color: row.isPartialYear ? Colors.orange[800] : null,
        ),
      ),
    );

    if (isDerogatoryTable) {
      return DataRow(
        cells: [
          DataCell(yearCell),
          DataCell(Text('${(row.accountingAmortization ?? 0).toStringAsFixed(0)} DH')),
          DataCell(Text('${(row.fiscalAmortization ?? 0).toStringAsFixed(0)} DH')),
          DataCell(_buildDerogatoryCell(row.derogationProvision, true)),
          DataCell(_buildDerogatoryCell(row.derogationReprise, false)),
          DataCell(Text('${(row.provisionBalance ?? 0).toStringAsFixed(0)} DH')),
        ],
      );
    } else {
      return DataRow(
        cells: [
          DataCell(yearCell),
          DataCell(Text('${row.baseAmount.toStringAsFixed(0)} DH')),
          DataCell(Text('${row.annuity.toStringAsFixed(0)} DH')),
          DataCell(Text('${row.cumulativeAnnuity.toStringAsFixed(0)} DH')),
          DataCell(Text('${row.netBookValue.toStringAsFixed(0)} DH')),
        ],
      );
    }
  }

  Widget _buildDerogatoryCell(double? amount, bool isProvision) {
    if (amount == null || amount == 0) {
      return const Text('-');
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
      decoration: BoxDecoration(
        color: isProvision ? Colors.red[100] : Colors.green[100],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        '${amount.toStringAsFixed(0)} DH',
        style: TextStyle(
          color: isProvision ? Colors.red[800] : Colors.green[800],
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  Widget _buildTaxAdviceCard() {
    final advice = _result!.taxAdvice!;
    
    return Card(
      elevation: 2,
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Conseil Fiscal',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Méthode recommandée: ${advice.recommendedMethod}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Économie fiscale estimée: ${advice.estimatedTaxSavings.toStringAsFixed(0)} DH',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(advice.reasoning),
          ],
        ),
      ),
    );
  }
  
  Widget _buildComparisonCard(DepreciationComparison comparison) {
    final isSuccessful = comparison.calculationSuccessful;
    final cardColor = isSuccessful ? null : Colors.red[50];
    final borderColor = isSuccessful ? null : Colors.red[200];
    
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      color: cardColor,
      shape: borderColor != null 
          ? RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
              side: BorderSide(color: borderColor),
            )
          : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccessful ? Icons.check_circle : Icons.error,
                  color: isSuccessful ? Colors.green : Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    comparison.methodDisplayName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSuccessful ? DesignTokens.colorPrimary : Colors.red[700],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (isSuccessful) ...[
              _buildSummaryItem('Amortissement total', '${comparison.totalDepreciation.toStringAsFixed(0)} DH'),
              _buildSummaryItem('Première année', '${comparison.firstYearDepreciation.toStringAsFixed(0)} DH'),
              _buildSummaryItem('Valeur actuelle nette', '${comparison.netPresentValue.toStringAsFixed(0)} DH'),
              _buildSummaryItem('Avantage fiscal', '${comparison.taxBenefit.toStringAsFixed(0)} DH'),
              const SizedBox(height: 8),
              Text(
                comparison.recommendation,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[600],
                ),
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Calcul échoué',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      comparison.calculationError ?? 'Erreur inconnue',
                      style: TextStyle(color: Colors.red[700]),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildCalculateButton() {
    final isMobile = context.isMobile;

    if (isMobile) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_isCalculating && _calculationProgress.isNotEmpty) ...[
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: DesignTokens.colorSurface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                _calculationProgress,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ),
          ],
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              FloatingActionButton.extended(
                onPressed: _isCalculating ? null : _calculateDepreciation,
                backgroundColor: DesignTokens.colorPrimary,
                icon: _isCalculating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.calculate),
                label: Text(_isCalculating ? 'Calcul...' : 'Calculer'),
              ),
              if (_isCalculating) ...[
                const SizedBox(width: 8),
                FloatingActionButton(
                  mini: true,
                  onPressed: _cancelCalculation,
                  backgroundColor: Colors.red,
                  child: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ],
          ),
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_isCalculating && _calculationProgress.isNotEmpty) ...[
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: DesignTokens.colorSurface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                _calculationProgress,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ),
          ],
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              FloatingActionButton.extended(
                onPressed: _isCalculating ? null : _calculateDepreciation,
                backgroundColor: DesignTokens.colorPrimary,
                icon: _isCalculating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.calculate),
                label: Text(_isCalculating ? 'Calculer l\'amortissement' : 'Calculer l\'amortissement'),
              ),
              if (_isCalculating) ...[
                const SizedBox(width: 8),
                FloatingActionButton(
                  mini: true,
                  onPressed: _cancelCalculation,
                  backgroundColor: Colors.red,
                  child: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ],
          ),
        ],
      );
    }
  }
  
  Widget _buildAnnualUnitsSection() {
    final usefulLife = int.tryParse(_usefulLifeController.text) ?? 0;
    
    if (usefulLife <= 0 || usefulLife > 20) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange[200]!),
        ),
        child: Text(
          usefulLife <= 0 
            ? 'Veuillez d\'abord saisir la durée d\'utilité pour configurer la production annuelle'
            : 'Durée d\'utilité trop longue pour la saisie manuelle (max 20 ans)',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.orange[700],
          ),
        ),
      );
    }
    
    // Ensure we have the right number of controllers
    _updateAnnualUnitsControllers(usefulLife);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Production annuelle (optionnel)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Si non renseigné, la production sera répartie uniformément',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 12),
        ...List.generate(usefulLife, (index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: CustomTextField(
              controller: _annualUnitsControllers[index],
              label: 'Année ${index + 1}',
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final number = double.tryParse(value);
                  if (number == null || number < 0) {
                    return 'Valeur invalide';
                  }
                }
                return null;
              },
            ),
          );
        }),
      ],
    );
  }
  
  void _updateAnnualUnitsFields() {
    setState(() {
      // This will trigger a rebuild and update the annual units section
    });
  }
  
  void _updateAnnualUnitsControllers(int count) {
    // Add controllers if needed
    while (_annualUnitsControllers.length < count) {
      _annualUnitsControllers.add(TextEditingController());
    }
    
    // Remove excess controllers
    while (_annualUnitsControllers.length > count) {
      final controller = _annualUnitsControllers.removeLast();
      controller.dispose();
    }
  }
  
  
  List<JournalEntry> _generateJournalEntries() {
    if (_result == null || _result!.amortizationTable.isEmpty) return [];

    // Create input with asset type information for the service
    final input = EnhancedDepreciationInput(
      assetName: _assetNameController.text.trim(),
      assetCost: double.parse(_assetCostController.text),
      residualValue: double.parse(_residualValueController.text.isEmpty ? '0' : _residualValueController.text),
      usefulLifeYears: int.parse(_usefulLifeController.text),
      method: _selectedMethod,
      acquisitionDate: _acquisitionDate,
      degressiveCoefficient: _selectedMethod.requiresCoefficient 
          ? double.tryParse(_degressiveCoefficientController.text) ?? 2.0
          : null,
      totalUnits: _selectedMethod.requiresUnits 
          ? double.tryParse(_totalUnitsController.text)
          : null,
      midYearConvention: _midYearConvention,
      assetType: _determineAssetType(),
      enableDerogatoire: _enableDerogatoire,
      fiscalMethod: _fiscalMethod,
      fiscalDegressiveCoefficient: _fiscalMethod == DepreciationMethod.degressive
          ? double.tryParse(_fiscalDegressiveCoefficientController.text) ?? 2.0
          : null,
    );

    final service = EnhancedDepreciationService();
    
    // Generate journal entries for all available tables
    final entries = <JournalEntry>[];
    
    // Add accounting entries
    entries.addAll(service.generateJournalEntries(_result!.amortizationTable, input));
    
    // Add derogatory entries if available
    // TODO: Implement generateDerogatoryJournalEntries method in service
    // if (_result!.hasMultipleTables && _result!.derogatoryAmortizationTable != null) {
    //   entries.addAll(service.generateDerogatoryJournalEntries(_result!.derogatoryAmortizationTable!, input));
    // }
    
    return entries;
  }

  String _determineAssetType() {
    // Use selected asset type if available
    if (_selectedAssetType != null) {
      return _selectedAssetType!.accountKey;
    }
    
    // Auto-detect from asset name
    final detectedType = AssetType.detectFromAssetName(_assetNameController.text);
    if (detectedType != null) {
      return detectedType.accountKey;
    }
    
    return 'buildings'; // Default asset type
  }


  void _onAssetNameChanged(String value) {
    // Auto-suggest asset type if none is manually selected
    if (_selectedAssetType == null && value.isNotEmpty) {
      final detectedType = AssetType.detectFromAssetName(value);
      if (detectedType != null) {
        setState(() {
          _selectedAssetType = detectedType;
        });
      }
    }
  }
  
  
  void _applyPreset(AssetPreset preset) {
    _usefulLifeController.text = preset.defaultUsefulLife.toString();
    _selectedMethod = preset.recommendedMethod;
    
    // Apply preset-specific values
    if (preset.defaultDegressiveCoefficient != null) {
      _degressiveCoefficientController.text = preset.defaultDegressiveCoefficient!.toString();
    }
    
    if (preset.estimatedTotalUnits != null) {
      _totalUnitsController.text = preset.estimatedTotalUnits!.toStringAsFixed(0);
    }
    
    // Calculate residual value if asset cost is provided
    final assetCost = double.tryParse(_assetCostController.text);
    if (assetCost != null) {
      final residualValue = assetCost * (preset.defaultResidualValuePercent / 100);
      _residualValueController.text = residualValue.toStringAsFixed(0);
    }
    
    // Update annual units fields if needed
    if (_selectedMethod.requiresUnits) {
      _updateAnnualUnitsFields();
    }
  }
  
  String? _validateNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Ce champ est requis';
    }
    
    final number = double.tryParse(value);
    if (number == null) {
      return 'Veuillez saisir un nombre valide';
    }
    
    if (!number.isFinite || number.isNaN) {
      return 'Valeur numérique invalide';
    }
    
    return null;
  }
  
  String? _validatePositiveNumber(String? value) {
    final error = _validateNumber(value);
    if (error != null) return error;
    
    final number = double.parse(value!);
    if (number <= 0) {
      return 'La valeur doit être positive';
    }
    
    if (number > CalculationLimits.maxAssetCost) {
      return 'Valeur trop élevée (max: ${CalculationLimits.maxAssetCost.toStringAsFixed(0)})';
    }
    
    return null;
  }
  
  String? _validatePositiveInteger(String? value) {
    final error = _validateNumber(value);
    if (error != null) return error;
    
    final number = int.tryParse(value!);
    if (number == null || number <= 0) {
      return 'Veuillez saisir un nombre entier positif';
    }
    
    if (number > CalculationLimits.maxUsefulLife) {
      return 'Durée trop longue (max: ${CalculationLimits.maxUsefulLife} ans)';
    }
    
    return null;
  }
  
  String? _validateAssetCost(String? value) {
    final error = _validatePositiveNumber(value);
    if (error != null) return error;
    
    final assetCost = double.parse(value!);
    final residualValue = double.tryParse(_residualValueController.text) ?? 0;
    
    if (residualValue >= assetCost) {
      return 'Le coût doit être supérieur à la valeur résiduelle';
    }
    
    return null;
  }
  
  String? _validateResidualValue(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Residual value is optional
    }
    
    final number = double.tryParse(value);
    if (number == null) {
      return 'Veuillez saisir un nombre valide';
    }
    
    if (number < 0) {
      return 'La valeur résiduelle ne peut pas être négative';
    }
    
    final assetCost = double.tryParse(_assetCostController.text) ?? 0;
    if (assetCost > 0 && number >= assetCost) {
      return 'La valeur résiduelle doit être inférieure au coût';
    }
    
    return null;
  }
  
  String? _validateDegressiveCoefficient(String? value) {
    if (_selectedMethod != DepreciationMethod.degressive) {
      return null;
    }
    
    final error = _validatePositiveNumber(value);
    if (error != null) return error;
    
    final coefficient = double.parse(value!);
    if (coefficient <= 1.0) {
      return 'Le coefficient doit être supérieur à 1.0';
    }
    
    if (coefficient > CalculationLimits.maxDegressiveCoefficient) {
      return 'Coefficient trop élevé (max: ${CalculationLimits.maxDegressiveCoefficient})';
    }
    
    return null;
  }
  
  String? _validateTotalUnits(String? value) {
    if (_selectedMethod != DepreciationMethod.unitsOfProduction) {
      return null;
    }
    
    final error = _validatePositiveNumber(value);
    if (error != null) return error;
    
    final totalUnits = double.parse(value!);
    if (totalUnits > CalculationLimits.maxTotalUnits) {
      return 'Nombre d\'unités trop élevé (max: ${CalculationLimits.maxTotalUnits.toStringAsFixed(0)})';
    }
    
    return null;
  }

  String? _validateFiscalDegressiveCoefficient(String? value) {
    if (!_enableDerogatoire || _fiscalMethod != DepreciationMethod.degressive) {
      return null;
    }
    
    final error = _validatePositiveNumber(value);
    if (error != null) return error;
    
    final coefficient = double.parse(value!);
    if (coefficient <= 1.0) {
      return 'Le coefficient fiscal doit être supérieur à 1.0';
    }
    
    if (coefficient > CalculationLimits.maxDegressiveCoefficient) {
      return 'Coefficient fiscal trop élevé (max: ${CalculationLimits.maxDegressiveCoefficient})';
    }
    
    return null;
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  Future<void> _selectAcquisitionDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _acquisitionDate,
      firstDate: DateTime(1900),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: 'Sélectionner la date d\'acquisition',
      fieldLabelText: 'Date d\'acquisition',
    );
    
    if (date != null) {
      setState(() {
        _acquisitionDate = date;
      });
    }
  }
  
  void _validateFormRealTime() {
    // Trigger form validation without showing errors
    _formKey.currentState?.validate();
  }
  
  void _cancelCalculation() {
    developer.log('User requested calculation cancellation');
    
    _calculationTimeout?.cancel();
    _calculationCompleter?.complete();
    
    setState(() {
      _isCalculating = false;
      _calculationProgress = '';
      _lastError = 'Calcul annulé par l\'utilisateur';
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Calcul annulé'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
  
  void _calculateDepreciation() async {
    // Clear previous error
    _lastError = null;
    
    if (!_formKey.currentState!.validate()) {
      _showErrorDialog(
        'Données invalides',
        'Veuillez corriger les erreurs dans le formulaire avant de continuer.',
      );
      return;
    }

    // Additional validation for derogatory calculations
    if (_enableDerogatoire) {
      if (_fiscalMethod == null) {
        _showErrorDialog(
          'Méthode fiscale requise',
          'Veuillez sélectionner une méthode fiscale pour le calcul dérogatoire.',
        );
        return;
      }
      
      if (_fiscalMethod == _selectedMethod) {
        final shouldContinue = await _showWarningDialog(
          'Méthodes identiques',
          'La méthode fiscale est identique à la méthode comptable. Aucun amortissement dérogatoire ne sera généré.\n\nVoulez-vous continuer ?',
        );
        if (!shouldContinue) return;
      }
    }
    
    setState(() {
      _isCalculating = true;
      _calculationProgress = 'Préparation du calcul...';
    });
    
    // Create a completer to handle cancellation
    _calculationCompleter = Completer<void>();
    
    // Set up timeout
    _calculationTimeout = Timer(_maxCalculationDuration, () {
      if (!_calculationCompleter!.isCompleted) {
        _calculationCompleter!.complete();
        _handleCalculationTimeout();
      }
    });
    
    try {
      // Validate input data with enhanced validation
      setState(() {
        _calculationProgress = 'Validation des données...';
      });
      
      final input = await _createDepreciationInput();
      
      if (_calculationCompleter!.isCompleted) return; // Cancelled or timed out
      
      // Use enhanced validation from CalculationUtils
      final calculationValidation = calc_utils.CalculationUtils.validateCalculationInputs(
        assetCost: input.assetCost,
        usefulLife: input.usefulLifeYears,
        degressiveCoefficient: input.degressiveCoefficient,
        totalUnits: input.totalUnits,
        annualUnits: input.annualUnitsProduced,
      );
      
      if (!calculationValidation.isValid) {
        throw ArgumentError('Validation échouée: ${calculationValidation.primaryError}');
      }
      
      // Validate the input using the model's enhanced validation
      final validation = input.validate();
      if (!validation.isValid) {
        throw ArgumentError('Données invalides: ${validation.primaryError}');
      }
      
      // Combine warnings from both validations
      final allWarnings = <String>[];
      if (calculationValidation.hasWarnings) {
        allWarnings.addAll(calculationValidation.warnings);
      }
      if (validation.hasWarnings) {
        allWarnings.addAll(validation.warnings);
      }
      
      if (allWarnings.isNotEmpty) {
        final shouldContinue = await _showWarningDialog(
          'Avertissements détectés',
          allWarnings.join('\n'),
        );
        if (!shouldContinue) {
          _cancelCalculation();
          return;
        }
      }
      
      setState(() {
        _calculationProgress = 'Calcul de l\'amortissement...';
      });
      
      if (_calculationCompleter!.isCompleted) return; // Cancelled or timed out
      
      final service = EnhancedDepreciationService();
      final result = await _performCalculationWithTimeout(service, input);
      
      if (_calculationCompleter!.isCompleted) return; // Cancelled or timed out
      
      List<DepreciationComparison>? comparisons;
      if (_comparisonMode) {
        setState(() {
          _calculationProgress = 'Comparaison des méthodes...';
        });
        
        if (_calculationCompleter!.isCompleted) return; // Cancelled or timed out
        
        comparisons = await _performComparisonWithTimeout(service, input);
      }
      
      // Generate journal entries from the calculation result
      setState(() {
        _calculationProgress = 'Génération des écritures comptables...';
      });
      
      if (_calculationCompleter!.isCompleted) return; // Cancelled or timed out
      
      // Success
      _calculationTimeout?.cancel();
      
      setState(() {
        _result = result;
        _comparisons = comparisons;
        _journalEntries = _generateJournalEntries();
        _isCalculating = false;
        _calculationProgress = '';
      });
      
      // Show warnings if any
      if (result.hasWarnings) {
        _showWarningSnackBar('Calcul terminé avec des avertissements', result.formattedWarnings);
      } else {
        _showSuccessSnackBar('Calcul terminé avec succès');
      }
      
      // Switch to results tab
      _tabController.animateTo(1);
      
    } catch (e, stackTrace) {
      _calculationTimeout?.cancel();
      
      developer.log('Calculation failed', error: e, stackTrace: stackTrace);
      
      setState(() {
        _isCalculating = false;
        _calculationProgress = '';
        _lastError = e.toString();
      });
      
      if (mounted) {
        _handleCalculationError(e);
      }
    }
  }
  
  Future<EnhancedDepreciationInput> _createDepreciationInput() async {
    // Collect annual units if provided
    List<double>? annualUnits;
    if (_selectedMethod.requiresUnits && _annualUnitsControllers.isNotEmpty) {
      annualUnits = [];
      for (final controller in _annualUnitsControllers) {
        if (controller.text.isNotEmpty) {
          final value = double.tryParse(controller.text);
          if (value != null && value >= 0) {
            annualUnits.add(value);
          }
        }
      }
      if (annualUnits.isEmpty) {
        annualUnits = null; // Let the service generate default values
      }
    }
    
    return EnhancedDepreciationInput(
      assetName: _assetNameController.text.trim(),
      assetCost: double.parse(_assetCostController.text),
      residualValue: double.parse(_residualValueController.text.isEmpty ? '0' : _residualValueController.text),
      usefulLifeYears: int.parse(_usefulLifeController.text),
      method: _selectedMethod,
      acquisitionDate: _acquisitionDate,
      degressiveCoefficient: _selectedMethod.requiresCoefficient 
          ? double.tryParse(_degressiveCoefficientController.text) ?? 2.0
          : null,
      totalUnits: _selectedMethod.requiresUnits 
          ? double.tryParse(_totalUnitsController.text)
          : null,
      annualUnitsProduced: annualUnits,
      midYearConvention: _midYearConvention,
      assetType: _determineAssetType(),
      enableDerogatoire: _enableDerogatoire,
      fiscalMethod: _fiscalMethod,
      fiscalDegressiveCoefficient: _fiscalMethod == DepreciationMethod.degressive
          ? double.tryParse(_fiscalDegressiveCoefficientController.text) ?? 2.0
          : null,
    );
  }
  
  Future<EnhancedDepreciationResult> _performCalculationWithTimeout(
    EnhancedDepreciationService service,
    EnhancedDepreciationInput input,
  ) async {
    // Use the CalculationTimeout utility for better timeout handling
    return await calc_utils.CalculationUtils.calculateWithTimeout(
      () async {
        // Provide progress updates during calculation
        Timer.periodic(const Duration(milliseconds: 500), (timer) {
          if (_calculationCompleter!.isCompleted) {
            timer.cancel();
            return;
          }
          
          setState(() {
            final elapsed = timer.tick * 500;
            if (elapsed < 2000) {
              _calculationProgress = 'Initialisation du calcul...';
            } else if (elapsed < 5000) {
              _calculationProgress = 'Traitement des données...';
            } else if (elapsed < 10000) {
              _calculationProgress = 'Calcul en cours...';
            } else if (elapsed < 20000) {
              _calculationProgress = 'Finalisation...';
            } else {
              _calculationProgress = 'Calcul complexe en cours...';
            }
          });
        });
        
        return service.calculateDepreciation(input, includeComparisons: false);
      },
      _maxCalculationDuration,
    );
  }
  
  Future<List<DepreciationComparison>> _performComparisonWithTimeout(
    EnhancedDepreciationService service,
    EnhancedDepreciationInput input,
  ) async {
    // Use the CalculationTimeout utility for comparison timeout handling
    return await calc_utils.CalculationUtils.calculateWithTimeout(
      () async {
        // Update progress for comparison calculations
        setState(() {
          _calculationProgress = 'Comparaison des méthodes d\'amortissement...';
        });
        
        return service.compareDepreciationMethods(input);
      },
      _maxCalculationDuration,
    );
  }
  
  void _handleCalculationTimeout() {
    developer.log('Calculation timed out after ${_maxCalculationDuration.inSeconds} seconds');
    
    setState(() {
      _isCalculating = false;
      _calculationProgress = '';
      _lastError = 'Le calcul a pris trop de temps et a été interrompu';
    });
    
    if (mounted) {
      _showErrorDialog(
        'Calcul interrompu',
        'Le calcul a pris trop de temps (plus de ${_maxCalculationDuration.inSeconds} secondes) et a été automatiquement interrompu.\n\n'
        'Suggestions:\n'
        '• Réduisez la durée d\'utilité\n'
        '• Simplifiez les paramètres\n'
        '• Désactivez le mode comparaison\n'
        '• Vérifiez que les valeurs ne sont pas extrêmes',
      );
    }
  }
  
  void _handleCalculationError(dynamic error) {
    String title = 'Erreur de calcul';
    String message = 'Une erreur inattendue s\'est produite.';
    
    if (error is ArgumentError) {
      title = 'Données invalides';
      message = error.message;
    } else if (error is StateError) {
      title = 'Erreur de calcul';
      message = error.message;
    } else if (error is TimeoutException) {
      title = 'Calcul interrompu';
      message = 'Le calcul a pris trop de temps et a été interrompu.';
    } else if (error.toString().contains('overflow') || error.toString().contains('infinity')) {
      title = 'Valeurs trop importantes';
      message = 'Les valeurs saisies sont trop importantes et causent un débordement de calcul. '
               'Veuillez réduire les montants ou la durée d\'utilité.';
    } else if (error.toString().contains('division by zero') || error.toString().contains('NaN')) {
      title = 'Erreur mathématique';
      message = 'Une division par zéro ou une valeur invalide a été détectée. '
               'Vérifiez que tous les champs obligatoires sont remplis avec des valeurs valides.';
    } else {
      message = 'Erreur: ${error.toString()}';
    }
    
    _showErrorDialog(title, message);
  }
  
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  
  Future<bool> _showWarningDialog(String title, String message) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 16),
            const Text(
              'Voulez-vous continuer malgré ces avertissements ?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Continuer'),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }
  
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  void _showWarningSnackBar(String title, String details) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(title, style: const TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
            if (details.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(details, style: const TextStyle(fontSize: 12)),
            ],
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Détails',
          textColor: Colors.white,
          onPressed: () => _showWarningDialog('Avertissements', details),
        ),
      ),
    );
  }
}
