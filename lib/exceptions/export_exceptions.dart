// Custom exception classes for export operations in the enhanced depreciation system.
// These exceptions provide granular error handling for better user experience.

/// Base class for all export-related exceptions
abstract class ExportException implements Exception {
  final String message;
  final String? details;
  final dynamic cause;

  const ExportException(this.message, {this.details, this.cause});

  @override
  String toString() {
    if (details != null) {
      return 'ExportException: $message\nDetails: $details';
    }
    return 'ExportException: $message';
  }
}

/// Exception thrown when an export functionality is not yet implemented
class ExportNotImplementedException extends ExportException {
  final String exportType;
  final String format;

  const ExportNotImplementedException(
    this.exportType,
    this.format, {
    String? details,
  }) : super(
    'Export functionality not implemented for $exportType in $format format',
    details: details,
  );

  @override
  String toString() => 'ExportNotImplementedException: Export functionality not implemented for $exportType in $format format';
}

/// Exception thrown when a required table is missing for export
class MissingTableException extends ExportException {
  final String tableType;
  final String requestedExport;

  const MissingTableException(
    this.tableType,
    this.requestedExport, {
    String? details,
  }) : super(
    'Required table "$tableType" is missing for $requestedExport export',
    details: details,
  );

  @override
  String toString() => 'MissingTableException: Required table "$tableType" is missing for $requestedExport export';
}

/// Exception thrown when export data is invalid or incomplete
class InvalidExportDataException extends ExportException {
  final String dataType;
  final List<String> validationErrors;

  const InvalidExportDataException(
    this.dataType,
    this.validationErrors, {
    String? details,
  }) : super(
    'Invalid export data for $dataType',
    details: details,
  );

  String get formattedErrors => validationErrors.join('\n• ');

  @override
  String toString() => 'InvalidExportDataException: Invalid export data for $dataType\nErrors:\n• $formattedErrors';
}

/// Exception thrown when the requested export format is not supported
class UnsupportedFormatException extends ExportException {
  final String requestedFormat;
  final List<String> supportedFormats;

  const UnsupportedFormatException(
    this.requestedFormat,
    this.supportedFormats, {
    String? details,
  }) : super(
    'Unsupported export format: $requestedFormat',
    details: details,
  );

  String get supportedFormatsString => supportedFormats.join(', ');

  @override
  String toString() => 'UnsupportedFormatException: Unsupported export format: $requestedFormat. Supported formats: $supportedFormatsString';
}

/// Exception thrown when file system operations fail during export
class ExportFileSystemException extends ExportException {
  final String operation;
  final String? filePath;

  const ExportFileSystemException(
    this.operation,
    String message, {
    this.filePath,
    String? details,
    dynamic cause,
  }) : super(message, details: details, cause: cause);

  @override
  String toString() {
    final pathInfo = filePath != null ? ' (Path: $filePath)' : '';
    return 'ExportFileSystemException: $operation failed - $message$pathInfo';
  }
}

/// Exception thrown when permissions are insufficient for export operations
class ExportPermissionException extends ExportException {
  final String requiredPermission;
  final String operation;

  const ExportPermissionException(
    this.requiredPermission,
    this.operation, {
    String? details,
  }) : super(
    'Insufficient permissions for $operation',
    details: details,
  );

  @override
  String toString() => 'ExportPermissionException: Insufficient permissions for $operation. Required: $requiredPermission';
}

/// Exception thrown when export process times out
class ExportTimeoutException extends ExportException {
  final Duration timeout;
  final String exportType;

  const ExportTimeoutException(
    this.exportType,
    this.timeout, {
    String? details,
  }) : super(
    'Export timed out',
    details: details,
  );

  @override
  String toString() => 'ExportTimeoutException: Export of $exportType timed out after ${timeout.inSeconds} seconds';
}

/// Exception thrown when export data exceeds size limits
class ExportSizeLimitException extends ExportException {
  final int currentSize;
  final int maxSize;
  final String unit;

  const ExportSizeLimitException(
    this.currentSize,
    this.maxSize,
    this.unit, {
    String? details,
  }) : super(
    'Export data size ($currentSize $unit) exceeds limit ($maxSize $unit)',
    details: details,
  );

  @override
  String toString() => 'ExportSizeLimitException: Export data size ($currentSize $unit) exceeds limit ($maxSize $unit)';
}

/// Exception thrown when derogatory table operations fail
class DerogatoryTableException extends ExportException {
  final String operation;
  final String reason;

  const DerogatoryTableException(
    this.operation,
    this.reason, {
    String? details,
  }) : super(
    'Derogatory table $operation failed: $reason',
    details: details,
  );

  @override
  String toString() => 'DerogatoryTableException: Derogatory table $operation failed: $reason';
}

/// Exception thrown when export template is missing or invalid
class ExportTemplateException extends ExportException {
  final String templateType;
  final String templatePath;

  const ExportTemplateException(
    this.templateType,
    this.templatePath, {
    String? details,
  }) : super(
    'Export template error for $templateType',
    details: details,
  );

  @override
  String toString() => 'ExportTemplateException: Export template error for $templateType (Path: $templatePath)';
}

/// Utility class for creating export exceptions with common patterns
class ExportExceptionFactory {
  /// Create an exception for not implemented export functionality
  static ExportNotImplementedException notImplemented(String exportType, String format, {String? details}) {
    return ExportNotImplementedException(exportType, format, details: details);
  }

  /// Create an exception for missing table with context
  static MissingTableException missingTable(String tableType, String requestedExport, {String? suggestion}) {
    String details = suggestion != null 
        ? 'Suggestion: $suggestion'
        : 'Ensure the calculation includes the required table type.';
    return MissingTableException(tableType, requestedExport, details: details);
  }

  /// Create an exception for unsupported format with available alternatives
  static UnsupportedFormatException unsupportedFormat(String requested, List<String> supported) {
    return UnsupportedFormatException(requested, supported);
  }

  /// Create an exception for file system operations
  static ExportFileSystemException fileSystemError(String operation, String message, {String? filePath, dynamic cause}) {
    return ExportFileSystemException(operation, message, filePath: filePath, cause: cause);
  }

  /// Create an exception for invalid export data with validation details
  static InvalidExportDataException invalidData(String dataType, List<String> errors) {
    return InvalidExportDataException(dataType, errors);
  }

  /// Create an exception for derogatory table issues
  static DerogatoryTableException derogatoryTableError(String operation, String reason) {
    return DerogatoryTableException(operation, reason);
  }

  /// Create timeout exception with standard message
  static ExportTimeoutException timeout(String exportType, Duration timeout) {
    return ExportTimeoutException(exportType, timeout);
  }

  /// Create size limit exception with formatted message
  static ExportSizeLimitException sizeLimit(int currentSize, int maxSize, String unit) {
    return ExportSizeLimitException(currentSize, maxSize, unit);
  }
  
  /// Create validation error with list of specific issues
  static InvalidExportDataException validationError(String code, String message, List<String> issues) {
    return InvalidExportDataException(code, issues, details: message);
  }
  
  /// Create encoding error for data conversion issues
  static ExportFileSystemException encodingError(String operation, String message) {
    return ExportFileSystemException('encoding_$operation', message);
  }
  
  /// Create platform-specific error
  static ExportException platformError(String code, String message) {
    return _GenericExportException('platform_$code', message);
  }
  
  /// Create permission error with actionable suggestions
  static ExportPermissionException permissionError(String code, String message, [List<String>? suggestions]) {
    String? details = suggestions != null && suggestions.isNotEmpty
        ? 'Suggestions: ${suggestions.join(", ")}'
        : null;
    return ExportPermissionException(code, message, details: details);
  }
  
  /// Create disk space error with recovery options
  static ExportException diskSpaceError(String code, String message, List<String> suggestions) {
    return _GenericExportException('disk_space_$code', message, suggestions.join(", "));
  }
  
  /// Create generic export error for uncategorized issues
  static ExportException generic(String operation, String message) {
    return _GenericExportException(operation, message);
  }
}

/// Generic export exception for uncategorized issues
class _GenericExportException extends ExportException {
  final String operation;
  
  const _GenericExportException(this.operation, String message, [String? details])
      : super(message, details: details);
  
  @override
  String toString() => 'ExportException($operation): $message${details != null ? ' - $details' : ''}';
}

/// Helper extension for common exception checks
extension ExportExceptionChecks on Exception {
  /// Check if this is an export-related exception
  bool get isExportException => this is ExportException;

  /// Check if this is a not-implemented exception
  bool get isNotImplemented => this is ExportNotImplementedException;

  /// Check if this is a missing table exception
  bool get isMissingTable => this is MissingTableException;

  /// Check if this is a file system related exception
  bool get isFileSystemError => this is ExportFileSystemException;

  /// Get user-friendly message for display
  String get userFriendlyMessage {
    if (this is ExportException) {
      final exportEx = this as ExportException;
      
      // Provide user-friendly messages for common exceptions
      if (this is ExportNotImplementedException) {
        return 'Cette fonctionnalité d\'export sera bientôt disponible';
      } else if (this is MissingTableException) {
        final missingEx = this as MissingTableException;
        return 'Tableau ${missingEx.tableType} non disponible pour cet actif';
      } else if (this is UnsupportedFormatException) {
        final formatEx = this as UnsupportedFormatException;
        return 'Format ${formatEx.requestedFormat} non supporté. Formats disponibles: ${formatEx.supportedFormatsString}';
      } else if (this is ExportFileSystemException) {
        return 'Erreur lors de la sauvegarde du fichier';
      } else if (this is ExportPermissionException) {
        return 'Permissions insuffisantes pour exporter le fichier';
      } else if (this is ExportTimeoutException) {
        return 'L\'export a pris trop de temps et a été interrompu';
      } else if (this is ExportSizeLimitException) {
        return 'Les données à exporter sont trop volumineuses';
      } else if (this is InvalidExportDataException) {
        return 'Données invalides pour l\'export';
      } else {
        return exportEx.message;
      }
    }
    return toString();
  }
}