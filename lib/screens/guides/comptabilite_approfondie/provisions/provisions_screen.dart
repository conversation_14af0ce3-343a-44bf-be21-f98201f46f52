import 'package:flutter/material.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_approfondie/provisions/sections/overview_section.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_approfondie/provisions/sections/calculator_section.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_approfondie/provisions/sections/exercices_section.dart';

class ProvisionsScreen extends StatefulWidget {
  const ProvisionsScreen({super.key});

  @override
  State<ProvisionsScreen> createState() => _ProvisionsScreenState();
}

class _ProvisionsScreenState extends State<ProvisionsScreen> {
  int _selectedIndex = 0;

  final List<Widget> _sections = [
    const OverviewSection(),
    const CalculatorSection(),
    const ExercicesSection(),
  ];

  final List<NavigationRailDestination> _destinations = const [
    NavigationRailDestination(
      icon: Icon(Icons.article_outlined),
      selectedIcon: Icon(Icons.article),
      label: Text('Aperçu'),
    ),
    NavigationRailDestination(
      icon: Icon(Icons.calculate_outlined),
      selectedIcon: Icon(Icons.calculate),
      label: Text('Calculateur'),
    ),
    NavigationRailDestination(
      icon: Icon(Icons.assignment_outlined),
      selectedIcon: Icon(Icons.assignment),
      label: Text('Exercices'),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isWideScreen = MediaQuery.of(context).size.width > 600;

    return PopScope(
      canPop: _selectedIndex == 0,
      onPopInvokedWithResult: (didPop, result) {
        // If not on the first tab, go back to the first tab
        if (!didPop && _selectedIndex > 0) {
          setState(() {
            _selectedIndex = 0;
          });
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text('Provisions', style: Theme.of(context).textTheme.titleLarge),
          leading: Navigator.canPop(context)
              ? IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => Navigator.of(context).pop(),
                )
              : null,
          bottom: isWideScreen ? null : PreferredSize(
            preferredSize: const Size.fromHeight(kToolbarHeight),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _destinations.map<Widget>((destination) {
                  final index = _destinations.indexOf(destination);
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: ChoiceChip(
                      label: destination.label,
                      avatar: _selectedIndex == index 
                        ? destination.selectedIcon 
                        : destination.icon,
                      selected: _selectedIndex == index,
                      onSelected: (bool selected) {
                        if (selected) {
                          setState(() {
                            _selectedIndex = index;
                          });
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ),
        body: isWideScreen
          ? Row(
              children: [
                NavigationRail(
                  selectedIndex: _selectedIndex,
                  onDestinationSelected: (int index) {
                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                  labelType: NavigationRailLabelType.all,
                  destinations: _destinations,
                ),
                const VerticalDivider(thickness: 1, width: 1),
                Expanded(
                  child: _sections[_selectedIndex],
                ),
              ],
            )
          : _sections[_selectedIndex],
        bottomNavigationBar: isWideScreen 
          ? null 
          : BottomNavigationBar(
              currentIndex: _selectedIndex,
              onTap: (int index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              items: [
                BottomNavigationBarItem(
                  icon: const Icon(Icons.article_outlined),
                  activeIcon: const Icon(Icons.article),
                  label: 'Aperçu',
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.calculate_outlined),
                  activeIcon: const Icon(Icons.calculate),
                  label: 'Calculateur',
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.assignment_outlined),
                  activeIcon: const Icon(Icons.assignment),
                  label: 'Exercices',
                ),
              ],
            ),
      ),
    );
  }
}
