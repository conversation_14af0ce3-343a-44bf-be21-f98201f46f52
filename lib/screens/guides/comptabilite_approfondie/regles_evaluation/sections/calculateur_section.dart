import 'dart:io';
import 'package:flutter/material.dart';
import '../../../../../utils/calculation_utils.dart';
import '../../../../../models/immobilisations/amortization_row.dart';
import '../../../../../widgets/journal_comptable_widget.dart';
import '../../../../../utils/excel_export_utils.dart';
import '../../../../../utils/pdf_export_utils.dart';

class CalculateurSection extends StatefulWidget {
  const CalculateurSection({super.key});

  @override
  State<CalculateurSection> createState() => _CalculateurSectionState();
}

class _CalculateurSectionState extends State<CalculateurSection> {
  final _formKey = GlobalKey<FormState>();
  final _valueController = TextEditingController();
  final _durationController = TextEditingController();
  final _rateController = TextEditingController();

  String _selectedMethod = 'lineaire';
  String _selectedAssetType = 'buildings';
  DateTime? _acquisitionDate;
  List<AmortizationRow> _amortizationTable = [];
  List<JournalEntry> _journalEntries = [];
  bool _isCalculated = false;

  @override
  void dispose() {
    _valueController.dispose();
    _durationController.dispose();
    _rateController.dispose();
    super.dispose();
  }

  void _calculate() {
    if (_formKey.currentState!.validate() && _acquisitionDate != null) {
      final value = double.parse(_valueController.text.replaceAll(' ', ''));
      final duration = int.parse(_durationController.text);
      final rate = _rateController.text.isNotEmpty
          ? double.parse(_rateController.text)
          : (100 / duration);

      setState(() {
        _amortizationTable = _generateAmortizationTable(
          value: value,
          duration: duration,
          rate: rate,
          method: _selectedMethod,
          acquisitionDate: _acquisitionDate!,
        );
        _journalEntries = _generateJournalEntries();
        _isCalculated = true;
      });
    }
  }

  List<AmortizationRow> _generateAmortizationTable({
    required double value,
    required int duration,
    required double rate,
    required String method,
    required DateTime acquisitionDate,
  }) {
    final List<AmortizationRow> table = [];
    double remainingValue = value;
    double cumulativeAnnuity = 0;

    for (int year = 1; year <= duration; year++) {
      final currentYear = acquisitionDate.year + year - 1;
      double annuity;

      if (method == 'lineaire') {
        annuity = value * (rate / 100);
        if (year == 1) {
          // Prorata temporis for first year
          final monthsRemaining = 12 - acquisitionDate.month + 1;
          annuity = annuity * (monthsRemaining / 12);
        } else if (year == duration) {
          // Prorata temporis for last year
          final lastYearMonths = acquisitionDate.month - 1;
          if (lastYearMonths > 0) {
            annuity = annuity * (lastYearMonths / 12);
          }
        }
      } else {
        // Degressive method
        annuity = remainingValue * (rate / 100);
      }

      if (year == duration) {
        annuity = remainingValue; // Last year takes remaining value
      }

      cumulativeAnnuity += annuity;
      remainingValue -= annuity;

      // Generate year label with month count for partial years
      String yearLabel;
      if (year == 1 && acquisitionDate.month > 1) {
        final firstYearMonths = 12 - acquisitionDate.month + 1;
        yearLabel = '$currentYear ($firstYearMonths mois)';
      } else if (year == duration && acquisitionDate.month > 1) {
        final lastYearMonths = acquisitionDate.month - 1;
        if (lastYearMonths > 0) {
          yearLabel = '$currentYear ($lastYearMonths mois)';
        } else {
          yearLabel = currentYear.toString();
        }
      } else {
        yearLabel = currentYear.toString();
      }

      table.add(AmortizationRow(
        year: currentYear,
        yearLabel: yearLabel,
        baseAmount: value,
        rate: rate,
        annuity: annuity,
        cumulativeAnnuity: cumulativeAnnuity,
        netBookValue: value - cumulativeAnnuity,
      ));
    }

    return table;
  }

  (String, String) _getAccountsForAsset() {
    // Returns (chargeAccount, amortizationAccount)
    // Map asset types to appropriate Moroccan chart accounts
    switch (_selectedAssetType) {
      case 'buildings':
        return ('6193', '28111'); // Constructions
      case 'technical_installations':
        return ('6192', '28121'); // Installations techniques, matériel et outillage
      case 'transport_equipment':
        return ('6195', '28131'); // Matériel de transport
      case 'office_equipment':
        return ('6194', '28141'); // Mobilier, matériel de bureau et aménagements divers
      case 'computer_equipment':
        return ('6196', '28151'); // Matériel informatique
      case 'other_equipment':
        return ('6192', '28121'); // Other technical equipment
      case 'land_improvements':
        return ('6193', '28101'); // Terrains aménagés
      case 'intangible_assets':
        return ('6197', '2811'); // Immobilisations incorporelles
      default:
        return ('6191', '28111'); // Generic amortization
    }
  }

  List<JournalEntry> _generateJournalEntries() {
    if (_amortizationTable.isEmpty) return [];

    final accounts = _getAccountsForAsset();
    final chargeAccount = accounts.$1;
    final amortizationAccount = accounts.$2;

    return _amortizationTable.map((row) {
      return JournalEntry(
        date: row.yearLabel,
        lines: [
          JournalLine(
            account: chargeAccount,
            label: 'Dotation aux amortissements ${row.yearLabel}',
            debit: row.annuity.toStringAsFixed(2),
          ),
          JournalLine(
            account: amortizationAccount,
            label: 'Cumul amortissements ${row.yearLabel}',
            credit: row.annuity.toStringAsFixed(2),
          ),
        ],
      );
    }).toList();
  }

  TableData _journalEntriesToTableData() {
    final rows = <List<String>>[];
    
    for (final entry in _journalEntries) {
      for (final line in entry.lines) {
        rows.add([
          entry.date ?? '',
          line.account,
          line.label,
          line.debit ?? '',
          line.credit ?? '',
        ]);
      }
    }

    return TableData(
      name: 'journal',
      title: 'Écritures comptables',
      headers: ['Date', 'Compte', 'Libellé', 'Débit', 'Crédit'],
      rows: rows,
      numericColumns: [3, 4],
      currencyColumns: [3, 4],
    );
  }

  Future<void> _handleExport(String format) async {
    String errorMessage = 'Erreur inconnue lors de l\'export';
    
    try {
      if (_amortizationTable.isEmpty) {
        throw ArgumentError('Aucune donnée à exporter. Veuillez d\'abord effectuer un calcul.');
      }

      if (format != 'excel' && format != 'pdf') {
        throw ArgumentError('Format d\'export non supporté: $format');
      }

      final amortTableData = TableData.fromAmortizationRows(
        _amortizationTable,
        name: 'amortization',
        title: 'Tableau d\'amortissement',
        isDegressive: _selectedMethod == 'degressif',
      );

      final journalTableData = _journalEntriesToTableData();
      
      if (journalTableData.rows.isEmpty) {
        throw StateError('Impossible de générer les écritures comptables');
      }

      final tables = [amortTableData, journalTableData];

      final now = DateTime.now();
      final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
      final fileName = 'amortissement_$timestamp';

      if (format == 'excel') {
        await ExcelExportUtils.exportMultipleTablesExcel(tables, fileName: fileName);
      } else if (format == 'pdf') {
        await PdfExportUtils.exportCalculationToPdf(
          tables,
          fileName,
          title: 'Calcul d\'amortissement',
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export $format réussi: $fileName'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } on ArgumentError catch (e) {
      errorMessage = 'Données invalides: ${e.message}';
    } on StateError catch (e) {
      errorMessage = 'Erreur d\'état: ${e.message}';
    } on FileSystemException {
      errorMessage = 'Erreur d\'accès au fichier: Vérifiez les permissions d\'écriture';
    } on FormatException catch (e) {
      errorMessage = 'Erreur de format de données: ${e.message}';
    } on UnsupportedError catch (e) {
      errorMessage = 'Opération non supportée: ${e.message}';
    } catch (e) {
      if (e.toString().contains('permission')) {
        errorMessage = 'Accès refusé: Vérifiez les permissions d\'écriture dans le dossier de destination';
      } else if (e.toString().contains('space') || e.toString().contains('disk')) {
        errorMessage = 'Espace disque insuffisant pour créer le fichier';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'Erreur réseau: Impossible d\'accéder au service d\'export';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'Délai d\'attente dépassé lors de l\'export';
      } else {
        errorMessage = 'Erreur inattendue: ${e.toString()}';
      }
    }
    
    if (mounted && errorMessage.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Réessayer',
            textColor: Colors.white,
            onPressed: () => _handleExport(format),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Calculateur d\'Amortissement'),
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        actions: [
          if (_isCalculated)
            PopupMenuButton<String>(
              icon: const Icon(Icons.file_download),
              onSelected: _handleExport,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'pdf',
                  child: ListTile(
                    leading: Icon(Icons.picture_as_pdf),
                    title: Text('Exporter en PDF'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'excel',
                  child: ListTile(
                    leading: Icon(Icons.table_chart),
                    title: Text('Exporter en Excel'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calculateur d\'Amortissement',
              style: textTheme.headlineMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            _buildCalculatorForm(),
            if (_isCalculated) ...[
              const SizedBox(height: 32),
              _buildAmortizationTable(),
              const SizedBox(height: 32),
              JournalComptableWidget(
                title: 'Écritures comptables',
                entries: _journalEntries,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorForm() {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _valueController,
                decoration: const InputDecoration(
                  labelText: 'Valeur d\'acquisition (DH)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez saisir la valeur';
                  }
                  if (double.tryParse(value.replaceAll(' ', '')) == null) {
                    return 'Valeur invalide';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _durationController,
                decoration: const InputDecoration(
                  labelText: 'Durée d\'amortissement (années)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez saisir la durée';
                  }
                  final duration = int.tryParse(value);
                  if (duration == null || duration <= 0) {
                    return 'Durée invalide';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedAssetType,
                decoration: const InputDecoration(
                  labelText: 'Type d\'immobilisation',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'buildings', child: Text('Constructions')),
                  DropdownMenuItem(value: 'technical_installations', child: Text('Installations techniques')),
                  DropdownMenuItem(value: 'transport_equipment', child: Text('Matériel de transport')),
                  DropdownMenuItem(value: 'office_equipment', child: Text('Mobilier et matériel de bureau')),
                  DropdownMenuItem(value: 'computer_equipment', child: Text('Matériel informatique')),
                  DropdownMenuItem(value: 'other_equipment', child: Text('Autres équipements')),
                  DropdownMenuItem(value: 'land_improvements', child: Text('Terrains aménagés')),
                  DropdownMenuItem(value: 'intangible_assets', child: Text('Immobilisations incorporelles')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedAssetType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedMethod,
                decoration: const InputDecoration(
                  labelText: 'Méthode d\'amortissement',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'lineaire', child: Text('Linéaire')),
                  DropdownMenuItem(value: 'degressif', child: Text('Dégressif')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedMethod = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                title: Text(_acquisitionDate == null
                    ? 'Date d\'acquisition'
                    : 'Date: ${_acquisitionDate!.day}/${_acquisitionDate!.month}/${_acquisitionDate!.year}'),
                trailing: const Icon(Icons.calendar_today),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2030),
                  );
                  if (date != null) {
                    setState(() {
                      _acquisitionDate = date;
                    });
                  }
                },
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _calculate,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Calculer'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmortizationTable() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tableau d\'Amortissement',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Année')),
                  DataColumn(label: Text('Base')),
                  DataColumn(label: Text('Taux')),
                  DataColumn(label: Text('Annuité')),
                  DataColumn(label: Text('Cumul')),
                  DataColumn(label: Text('VNC')),
                ],
                rows: _amortizationTable.map((row) {
                  return DataRow(
                    cells: [
                      DataCell(Text(row.yearLabel)),
                      DataCell(Text(CalculationUtils.formatMonetary(row.baseAmount))),
                      DataCell(Text('${row.rate.toStringAsFixed(2)}%')),
                      DataCell(Text(CalculationUtils.formatMonetary(row.annuity))),
                      DataCell(Text(CalculationUtils.formatMonetary(row.cumulativeAnnuity))),
                      DataCell(Text(CalculationUtils.formatMonetary(row.netBookValue))),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
