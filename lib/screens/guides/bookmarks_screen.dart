import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import '../../models/guide/bookmark_data.dart';
import '../../providers/bookmark_provider.dart';
import '../../theme/app_theme.dart';
import '../../theme/app_icons.dart';
import '../../theme/design_tokens.dart';
import '../../widgets/guide/bookmark_button.dart';

/// Enum for bookmark sorting options
enum BookmarkSortOption {
  dateCreated,
  dateModified,
  title,
  type,
}

/// Enum for bookmark view modes
enum BookmarkViewMode {
  list,
  grid,
}

/// Comprehensive bookmarks management screen with search, filtering, organization, and bulk operations
class BookmarksScreen extends ConsumerStatefulWidget {
  const BookmarksScreen({super.key});

  @override
  ConsumerState<BookmarksScreen> createState() => _BookmarksScreenState();
}

class _BookmarksScreenState extends ConsumerState<BookmarksScreen>
    with TickerProviderStateMixin {
  // Controllers and state
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  // Filter and sort state
  String _searchQuery = '';
  BookmarkType? _selectedType;
  String? _selectedTag;
  BookmarkSortOption _sortOption = BookmarkSortOption.dateCreated;
  bool _sortAscending = false;
  BookmarkViewMode _viewMode = BookmarkViewMode.list;

  // Selection state for bulk operations
  bool _isSelectionMode = false;
  final Set<String> _selectedBookmarks = {};

  // Statistics
  bool _showStatistics = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
    _fabAnimationController.forward();

    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _tabController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          if (_showStatistics) _buildStatisticsCard(),
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(child: _buildTabBarView()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
      bottomNavigationBar: _isSelectionMode ? _buildSelectionBottomBar() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: _isSelectionMode
          ? Text('${_selectedBookmarks.length} sélectionné(s)')
          : const Text('Mes Signets'),
      leading: _isSelectionMode
          ? IconButton(
              icon: const Icon(Icons.close),
              onPressed: _exitSelectionMode,
            )
          : null,
      actions: [
        if (!_isSelectionMode) ...[
          IconButton(
            icon: Icon(_showStatistics ? Icons.show_chart : Icons.analytics),
            onPressed: () {
              setState(() {
                _showStatistics = !_showStatistics;
              });
            },
            tooltip: _showStatistics ? 'Masquer les statistiques' : 'Afficher les statistiques',
          ),
          IconButton(
            icon: Icon(_viewMode == BookmarkViewMode.list ? Icons.grid_view : Icons.list),
            onPressed: _toggleViewMode,
            tooltip: _viewMode == BookmarkViewMode.list ? 'Vue grille' : 'Vue liste',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'select_all',
                child: ListTile(
                  leading: Icon(Icons.select_all),
                  title: Text('Mode sélection'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Exporter'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('Importer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: ListTile(
                  leading: Icon(Icons.clear_all, color: Colors.red),
                  title: Text('Tout supprimer', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ] else ...[
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _selectAllVisible,
            tooltip: 'Tout sélectionner',
          ),
        ],
      ],
    );
  }

  Widget _buildStatisticsCard() {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsync = ref.watch(bookmarkStatisticsProvider);
        
        return statisticsAsync.when(
          data: (stats) => Container(
            margin: const EdgeInsets.all(DesignTokens.space4),
            padding: const EdgeInsets.all(DesignTokens.space4),
            decoration: AppTheme.getCardDecoration(Theme.of(context).colorScheme),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.analytics, color: Theme.of(context).colorScheme.primary),
                    const SizedBox(width: DesignTokens.space2),
                    Text(
                      'Statistiques',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: DesignTokens.space3),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        'Total',
                        stats.totalBookmarks.toString(),
                        Icons.bookmark,
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Guides',
                        stats.bookmarksByGuide.length.toString(),
                        Icons.book,
                        Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Tags',
                        stats.uniqueTags.length.toString(),
                        Icons.tag,
                        Theme.of(context).colorScheme.tertiary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          loading: () => const SizedBox.shrink(),
          error: (_, __) => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.space2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppIcons.sizeLg),
          const SizedBox(height: DesignTokens.space1),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.space4),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Rechercher dans les signets...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
            ),
          ),
          const SizedBox(height: DesignTokens.space3),
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'Type',
                  _selectedType?.name ?? 'Tous',
                  () => _showTypeFilter(),
                ),
                const SizedBox(width: DesignTokens.space2),
                _buildFilterChip(
                  'Tag',
                  _selectedTag ?? 'Tous',
                  () => _showTagFilter(),
                ),
                const SizedBox(width: DesignTokens.space2),
                _buildSortChip(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, VoidCallback onTap) {
    final isActive = (label == 'Type' && _selectedType != null) ||
                    (label == 'Tag' && _selectedTag != null);
    
    return FilterChip(
      label: Text('$label: $value'),
      selected: isActive,
      onSelected: (_) => onTap(),
      avatar: isActive
          ? const Icon(Icons.check, size: 16)
          : const Icon(Icons.filter_list, size: 16),
    );
  }

  Widget _buildSortChip() {
    return ActionChip(
      label: Text('${_getSortLabel(_sortOption)} ${_sortAscending ? '↑' : '↓'}'),
      avatar: const Icon(Icons.sort, size: 16),
      onPressed: _showSortOptions,
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      tabs: const [
        Tab(text: 'Tous', icon: Icon(Icons.bookmark)),
        Tab(text: 'Récents', icon: Icon(Icons.access_time)),
        Tab(text: 'Par Guide', icon: Icon(Icons.book)),
        Tab(text: 'Par Tag', icon: Icon(Icons.tag)),
      ],
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildAllBookmarksTab(),
        _buildRecentBookmarksTab(),
        _buildByGuideTab(),
        _buildByTagTab(),
      ],
    );
  }

  Widget _buildAllBookmarksTab() {
    return Consumer(
      builder: (context, ref, child) {
        final bookmarksAsync = ref.watch(bookmarksProvider);
        
        return bookmarksAsync.when(
          data: (bookmarks) {
            final filteredBookmarks = _filterAndSortBookmarks(bookmarks);
            
            if (filteredBookmarks.isEmpty) {
              return _buildEmptyState(
                'Aucun signet trouvé',
                'Vos signets apparaîtront ici',
                Icons.bookmark_border,
              );
            }
            
            return _buildBookmarksList(filteredBookmarks);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildRecentBookmarksTab() {
    return Consumer(
      builder: (context, ref, child) {
        final recentBookmarksAsync = ref.watch(recentBookmarksProvider(20));
        
        return recentBookmarksAsync.when(
          data: (bookmarks) {
            final filteredBookmarks = _filterAndSortBookmarks(bookmarks);
            
            if (filteredBookmarks.isEmpty) {
              return _buildEmptyState(
                'Aucun signet récent',
                'Vos signets récents apparaîtront ici',
                Icons.access_time,
              );
            }
            
            return _buildBookmarksList(filteredBookmarks);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildByGuideTab() {
    return Consumer(
      builder: (context, ref, child) {
        final bookmarksAsync = ref.watch(bookmarksProvider);
        
        return bookmarksAsync.when(
          data: (bookmarks) {
            final groupedBookmarks = _groupBookmarksByGuide(bookmarks);
            
            if (groupedBookmarks.isEmpty) {
              return _buildEmptyState(
                'Aucun signet par guide',
                'Vos signets groupés par guide apparaîtront ici',
                Icons.book,
              );
            }
            
            return _buildGroupedBookmarksList(groupedBookmarks);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildByTagTab() {
    return Consumer(
      builder: (context, ref, child) {
        final bookmarksAsync = ref.watch(bookmarksProvider);
        
        return bookmarksAsync.when(
          data: (bookmarks) {
            final groupedBookmarks = _groupBookmarksByTag(bookmarks);
            
            if (groupedBookmarks.isEmpty) {
              return _buildEmptyState(
                'Aucun signet avec tags',
                'Vos signets avec des tags apparaîtront ici',
                Icons.tag,
              );
            }
            
            return _buildTaggedBookmarksList(groupedBookmarks);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildBookmarksList(List<BookmarkData> bookmarks) {
    if (_viewMode == BookmarkViewMode.grid) {
      return _buildBookmarksGrid(bookmarks);
    }
    
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(DesignTokens.space4),
      itemCount: bookmarks.length,
      itemBuilder: (context, index) {
        final bookmark = bookmarks[index];
        return _buildBookmarkCard(bookmark);
      },
    );
  }

  Widget _buildBookmarksGrid(List<BookmarkData> bookmarks) {
    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(DesignTokens.space4),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: DesignTokens.space3,
        mainAxisSpacing: DesignTokens.space3,
      ),
      itemCount: bookmarks.length,
      itemBuilder: (context, index) {
        final bookmark = bookmarks[index];
        return _buildBookmarkGridCard(bookmark);
      },
    );
  }

  Widget _buildBookmarkCard(BookmarkData bookmark) {
    final isSelected = _selectedBookmarks.contains(bookmark.id);
    
    return Card(
      margin: const EdgeInsets.only(bottom: DesignTokens.space3),
      child: InkWell(
        onTap: () => _isSelectionMode 
            ? _toggleBookmarkSelection(bookmark.id)
            : _openBookmark(bookmark),
        onLongPress: () => _enterSelectionMode(bookmark.id),
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.space4),
          decoration: isSelected
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                )
              : null,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 4,
                    height: 40,
                    decoration: BoxDecoration(
                      color: bookmark.color,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: DesignTokens.space3),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          bookmark.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (bookmark.description.isNotEmpty) ...[
                          const SizedBox(height: DesignTokens.space1),
                          Text(
                            bookmark.description,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (_isSelectionMode)
                    Checkbox(
                      value: isSelected,
                      onChanged: (_) => _toggleBookmarkSelection(bookmark.id),
                    )
                  else
                    PopupMenuButton<String>(
                      onSelected: (action) => _handleBookmarkAction(action, bookmark),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('Modifier'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'share',
                          child: ListTile(
                            leading: Icon(Icons.share),
                            title: Text('Partager'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('Supprimer', style: TextStyle(color: Colors.red)),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              const SizedBox(height: DesignTokens.space3),
              Row(
                children: [
                  Chip(
                    label: Text(bookmark.getBookmarkTypeLabel()),
                    backgroundColor: bookmark.color.withValues(alpha: 0.1),
                    labelStyle: TextStyle(
                      color: bookmark.color,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(width: DesignTokens.space2),
                  Text(
                    _formatDate(bookmark.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  if (bookmark.tags.isNotEmpty)
                    Icon(
                      Icons.tag,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                ],
              ),
              if (bookmark.tags.isNotEmpty) ...[
                const SizedBox(height: DesignTokens.space2),
                Wrap(
                  spacing: DesignTokens.space1,
                  runSpacing: DesignTokens.space1,
                  children: bookmark.tags.take(3).map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.space2,
                        vertical: DesignTokens.space1,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
                      ),
                      child: Text(
                        tag,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookmarkGridCard(BookmarkData bookmark) {
    final isSelected = _selectedBookmarks.contains(bookmark.id);
    
    return Card(
      child: InkWell(
        onTap: () => _isSelectionMode 
            ? _toggleBookmarkSelection(bookmark.id)
            : _openBookmark(bookmark),
        onLongPress: () => _enterSelectionMode(bookmark.id),
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.space3),
          decoration: isSelected
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                )
              : null,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: bookmark.color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
                    ),
                    child: Icon(
                      Icons.bookmark,
                      color: bookmark.color,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  if (_isSelectionMode)
                    Checkbox(
                      value: isSelected,
                      onChanged: (_) => _toggleBookmarkSelection(bookmark.id),
                    ),
                ],
              ),
              const SizedBox(height: DesignTokens.space2),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bookmark.title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: DesignTokens.space1),
                    Text(
                      bookmark.getBookmarkTypeLabel(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _formatDate(bookmark.createdAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGroupedBookmarksList(Map<String, List<BookmarkData>> groupedBookmarks) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(DesignTokens.space4),
      itemCount: groupedBookmarks.length,
      itemBuilder: (context, index) {
        final guideId = groupedBookmarks.keys.elementAt(index);
        final bookmarks = groupedBookmarks[guideId]!;
        
        return Card(
          margin: const EdgeInsets.only(bottom: DesignTokens.space3),
          child: ExpansionTile(
            title: Text(
              _getGuideTitle(guideId),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text('${bookmarks.length} signet(s)'),
            leading: Icon(
              Icons.book,
              color: Theme.of(context).colorScheme.primary,
            ),
            children: bookmarks.map((bookmark) => ListTile(
              title: Text(bookmark.title),
              subtitle: Text(bookmark.description),
              leading: Container(
                width: 4,
                height: 40,
                decoration: BoxDecoration(
                  color: bookmark.color,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              trailing: PopupMenuButton<String>(
                onSelected: (action) => _handleBookmarkAction(action, bookmark),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text('Modifier'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('Supprimer', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
              onTap: () => _openBookmark(bookmark),
            )).toList(),
          ),
        );
      },
    );
  }

  Widget _buildTaggedBookmarksList(Map<String, List<BookmarkData>> groupedBookmarks) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(DesignTokens.space4),
      itemCount: groupedBookmarks.length,
      itemBuilder: (context, index) {
        final tag = groupedBookmarks.keys.elementAt(index);
        final bookmarks = groupedBookmarks[tag]!;
        
        return Card(
          margin: const EdgeInsets.only(bottom: DesignTokens.space3),
          child: ExpansionTile(
            title: Text(
              tag,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text('${bookmarks.length} signet(s)'),
            leading: Icon(
              Icons.tag,
              color: Theme.of(context).colorScheme.secondary,
            ),
            children: bookmarks.map((bookmark) => ListTile(
              title: Text(bookmark.title),
              subtitle: Text(bookmark.description),
              leading: Container(
                width: 4,
                height: 40,
                decoration: BoxDecoration(
                  color: bookmark.color,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              trailing: PopupMenuButton<String>(
                onSelected: (action) => _handleBookmarkAction(action, bookmark),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text('Modifier'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('Supprimer', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
              onTap: () => _openBookmark(bookmark),
            )).toList(),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: DesignTokens.space4),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: DesignTokens.space2),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: DesignTokens.space4),
          Text(
            'Erreur de chargement',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: DesignTokens.space2),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: DesignTokens.space4),
          ElevatedButton.icon(
            onPressed: () {
              // Refresh the current tab
              setState(() {});
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return ScaleTransition(
      scale: _fabAnimation,
      child: FloatingActionButton.extended(
        onPressed: () {
          // Navigate to create bookmark screen or show dialog
          _showCreateBookmarkDialog();
        },
        icon: const Icon(Icons.bookmark_add),
        label: const Text('Nouveau signet'),
      ),
    );
  }

  Widget? _buildSelectionBottomBar() {
    if (!_isSelectionMode || _selectedBookmarks.isEmpty) return null;

    return BottomAppBar(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.space4),
        child: Row(
          children: [
            Text(
              '${_selectedBookmarks.length} sélectionné(s)',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Spacer(),
            IconButton(
              onPressed: _shareSelectedBookmarks,
              icon: const Icon(Icons.share),
              tooltip: 'Partager',
            ),
            IconButton(
              onPressed: _exportSelectedBookmarks,
              icon: const Icon(Icons.download),
              tooltip: 'Exporter',
            ),
            IconButton(
              onPressed: _deleteSelectedBookmarks,
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Supprimer',
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  List<BookmarkData> _filterAndSortBookmarks(List<BookmarkData> bookmarks) {
    var filtered = bookmarks.where((bookmark) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!bookmark.title.toLowerCase().contains(query) &&
            !bookmark.description.toLowerCase().contains(query) &&
            !bookmark.tags.any((tag) => tag.toLowerCase().contains(query))) {
          return false;
        }
      }

      // Type filter
      if (_selectedType != null && bookmark.bookmarkType != _selectedType) {
        return false;
      }

      // Tag filter
      if (_selectedTag != null && !bookmark.tags.contains(_selectedTag)) {
        return false;
      }

      return true;
    }).toList();

    // Sort
    filtered.sort((a, b) {
      int comparison;
      switch (_sortOption) {
        case BookmarkSortOption.title:
          comparison = a.title.compareTo(b.title);
          break;
        case BookmarkSortOption.type:
          comparison = a.bookmarkType.index.compareTo(b.bookmarkType.index);
          break;
        case BookmarkSortOption.dateModified:
          comparison = a.lastAccessed.compareTo(b.lastAccessed);
          break;
        case BookmarkSortOption.dateCreated:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  Map<String, List<BookmarkData>> _groupBookmarksByGuide(List<BookmarkData> bookmarks) {
    final filtered = _filterAndSortBookmarks(bookmarks);
    final Map<String, List<BookmarkData>> grouped = {};
    
    for (final bookmark in filtered) {
      grouped.putIfAbsent(bookmark.guideId, () => []).add(bookmark);
    }
    
    return grouped;
  }

  Map<String, List<BookmarkData>> _groupBookmarksByTag(List<BookmarkData> bookmarks) {
    final filtered = _filterAndSortBookmarks(bookmarks);
    final Map<String, List<BookmarkData>> grouped = {};
    
    for (final bookmark in filtered) {
      if (bookmark.tags.isEmpty) {
        grouped.putIfAbsent('Sans tag', () => []).add(bookmark);
      } else {
        for (final tag in bookmark.tags) {
          grouped.putIfAbsent(tag, () => []).add(bookmark);
        }
      }
    }
    
    return grouped;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Aujourd\'hui';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} jours';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  String _getSortLabel(BookmarkSortOption option) {
    switch (option) {
      case BookmarkSortOption.dateCreated:
        return 'Date création';
      case BookmarkSortOption.dateModified:
        return 'Date modif.';
      case BookmarkSortOption.title:
        return 'Titre';
      case BookmarkSortOption.type:
        return 'Type';
    }
  }

  String _getGuideTitle(String guideId) {
    // This should be replaced with actual guide title lookup
    return 'Guide ${guideId.substring(0, 8)}...';
  }

  // Action methods
  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedBookmarks.clear();
    });
  }

  void _enterSelectionMode(String bookmarkId) {
    setState(() {
      _isSelectionMode = true;
      _selectedBookmarks.add(bookmarkId);
    });
  }

  void _toggleBookmarkSelection(String bookmarkId) {
    setState(() {
      if (_selectedBookmarks.contains(bookmarkId)) {
        _selectedBookmarks.remove(bookmarkId);
        if (_selectedBookmarks.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedBookmarks.add(bookmarkId);
      }
    });
  }

  void _selectAllVisible() {
    // This would need to get the currently visible bookmarks
    // For now, we'll just select all bookmarks
    final bookmarksAsync = ref.read(bookmarksProvider);
    bookmarksAsync.whenData((bookmarks) {
      final filtered = _filterAndSortBookmarks(bookmarks);
      setState(() {
        _selectedBookmarks.addAll(filtered.map((b) => b.id));
      });
    });
  }

  void _toggleViewMode() {
    setState(() {
      _viewMode = _viewMode == BookmarkViewMode.list
          ? BookmarkViewMode.grid
          : BookmarkViewMode.list;
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'select_all':
        setState(() {
          _isSelectionMode = true;
        });
        break;
      case 'export':
        _exportAllBookmarks();
        break;
      case 'import':
        _importBookmarks();
        break;
      case 'clear_all':
        _showClearAllDialog();
        break;
    }
  }

  void _handleBookmarkAction(String action, BookmarkData bookmark) {
    switch (action) {
      case 'edit':
        _editBookmark(bookmark);
        break;
      case 'share':
        _shareBookmark(bookmark);
        break;
      case 'delete':
        _deleteBookmark(bookmark);
        break;
    }
  }

  void _openBookmark(BookmarkData bookmark) {
    // Update last accessed time
    bookmark.updateLastAccessed();
    
    // Navigate to the bookmarked section
    // This would typically navigate to the guide section
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ouverture du signet: ${bookmark.title}'),
        action: SnackBarAction(
          label: 'Fermer',
          onPressed: () {},
        ),
      ),
    );
  }

  void _showTypeFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(DesignTokens.space4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filtrer par type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: DesignTokens.space3),
            ListTile(
              title: const Text('Tous les types'),
              leading: Radio<BookmarkType?>(
                value: null,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
            ...BookmarkType.values.map((type) => ListTile(
              title: Text(_getTypeLabel(type)),
              leading: Radio<BookmarkType?>(
                value: type,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value;
                  });
                  Navigator.pop(context);
                },
              ),
            )),
          ],
        ),
      ),
    );
  }

  void _showTagFilter() {
    final bookmarksAsync = ref.read(bookmarksProvider);
    bookmarksAsync.whenData((bookmarks) {
      final allTags = <String>{};
      for (final bookmark in bookmarks) {
        allTags.addAll(bookmark.tags);
      }
      
      showModalBottomSheet(
        context: context,
        builder: (context) => Container(
          padding: const EdgeInsets.all(DesignTokens.space4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Filtrer par tag',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: DesignTokens.space3),
              ListTile(
                title: const Text('Tous les tags'),
                leading: Radio<String?>(
                  value: null,
                  groupValue: _selectedTag,
                  onChanged: (value) {
                    setState(() {
                      _selectedTag = value;
                    });
                    Navigator.pop(context);
                  },
                ),
              ),
              ...allTags.map((tag) => ListTile(
                title: Text(tag),
                leading: Radio<String?>(
                  value: tag,
                  groupValue: _selectedTag,
                  onChanged: (value) {
                    setState(() {
                      _selectedTag = value;
                    });
                    Navigator.pop(context);
                  },
                ),
              )),
            ],
          ),
        ),
      );
    });
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(DesignTokens.space4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trier par',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: DesignTokens.space3),
            ...BookmarkSortOption.values.map((option) => ListTile(
              title: Text(_getSortLabel(option)),
              leading: Radio<BookmarkSortOption>(
                value: option,
                groupValue: _sortOption,
                onChanged: (value) {
                  setState(() {
                    _sortOption = value!;
                  });
                  Navigator.pop(context);
                },
              ),
              trailing: option == _sortOption
                  ? IconButton(
                      icon: Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward),
                      onPressed: () {
                        setState(() {
                          _sortAscending = !_sortAscending;
                        });
                        Navigator.pop(context);
                      },
                    )
                  : null,
            )),
          ],
        ),
      ),
    );
  }

  void _showCreateBookmarkDialog() {
    showDialog(
      context: context,
      builder: (context) => BookmarkCreationDialog(
        guideId: 'manual',
        sectionId: 'manual',
        sectionTitle: 'Nouveau signet',
        onBookmarkCreated: (bookmark) {
          ref.read(bookmarkNotifierProvider.notifier).addBookmark(bookmark);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Signet créé avec succès')),
          );
        },
      ),
    );
  }

  void _editBookmark(BookmarkData bookmark) {
    showDialog(
      context: context,
      builder: (context) => BookmarkCreationDialog(
        guideId: bookmark.guideId,
        sectionId: bookmark.sectionId,
        sectionTitle: bookmark.title,
        onBookmarkCreated: (updatedBookmark) {
          final updated = bookmark.copyWith(
            title: updatedBookmark.title,
            description: updatedBookmark.description,
            tags: updatedBookmark.tags,
            color: updatedBookmark.color,
            bookmarkType: updatedBookmark.bookmarkType,
          );
          ref.read(bookmarkNotifierProvider.notifier).updateBookmark(updated);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Signet modifié avec succès')),
          );
        },
      ),
    );
  }

  void _shareBookmark(BookmarkData bookmark) {
    final content = '''
Signet: ${bookmark.title}
${bookmark.description.isNotEmpty ? 'Description: ${bookmark.description}\n' : ''}
Type: ${bookmark.getBookmarkTypeLabel()}
${bookmark.tags.isNotEmpty ? 'Tags: ${bookmark.tags.join(', ')}\n' : ''}
Créé le: ${_formatDate(bookmark.createdAt)}
''';
    
    // Share.share(content, subject: 'Signet partagé');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité de partage à implémenter')),
    );
  }

  void _deleteBookmark(BookmarkData bookmark) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le signet'),
        content: Text('Êtes-vous sûr de vouloir supprimer "${bookmark.title}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(bookmarkNotifierProvider.notifier).deleteBookmark(bookmark.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Signet supprimé')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Supprimer', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _shareSelectedBookmarks() {
    // Implementation for sharing selected bookmarks
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Partage de ${_selectedBookmarks.length} signets')),
    );
  }

  void _exportSelectedBookmarks() {
    // Implementation for exporting selected bookmarks
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Export de ${_selectedBookmarks.length} signets')),
    );
  }

  void _deleteSelectedBookmarks() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer les signets'),
        content: Text('Êtes-vous sûr de vouloir supprimer ${_selectedBookmarks.length} signet(s) ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              for (final id in _selectedBookmarks) {
                await ref.read(bookmarkNotifierProvider.notifier).deleteBookmark(id);
              }
              Navigator.pop(context);
              _exitSelectionMode();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${_selectedBookmarks.length} signet(s) supprimé(s)')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Supprimer', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _exportAllBookmarks() async {
    try {
      final data = await ref.read(bookmarkNotifierProvider.notifier).exportBookmarks();
      final jsonString = const JsonEncoder.withIndent('  ').convert(data);
      
      // For now, just show a dialog with the JSON
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Export des signets'),
          content: SingleChildScrollView(
            child: SelectableText(jsonString),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Fermer'),
            ),
          ],
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur lors de l\'export: $e')),
      );
    }
  }

  void _importBookmarks() {
    // Implementation for importing bookmarks
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité d\'import à implémenter')),
    );
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer tous les signets'),
        content: const Text('Êtes-vous sûr de vouloir supprimer tous vos signets ? Cette action est irréversible.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(bookmarkNotifierProvider.notifier).clearAllBookmarks();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Tous les signets ont été supprimés')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Tout supprimer', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  String _getTypeLabel(BookmarkType type) {
    switch (type) {
      case BookmarkType.section:
        return 'Section';
      case BookmarkType.specificContent:
        return 'Contenu spécifique';
      case BookmarkType.personalNote:
        return 'Note personnelle';
    }
  }
}