import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/calculators/enhanced_depreciation_calculator.dart';
import '../../providers/calculator_providers.dart';
import '../../models/calculators/calculation_history_item.dart';
import '../../theme/design_tokens.dart';
import '../../theme/responsive_breakpoints.dart';
import '../../exceptions/export_exceptions.dart';

class EnhancedDepreciationScreen extends ConsumerStatefulWidget {
  const EnhancedDepreciationScreen({super.key});

  @override
  ConsumerState<EnhancedDepreciationScreen> createState() => _EnhancedDepreciationScreenState();
}

class _EnhancedDepreciationScreenState extends ConsumerState<EnhancedDepreciationScreen> {
  bool _showHelp = false;

  @override
  void initState() {
    super.initState();
    // Set active calculator
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(activeCalculatorProvider.notifier).state = CalculatorType.depreciation;
    });
  }

  @override
  Widget build(BuildContext context) {
    final calculatorState = ref.watch(calculatorStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Amortissement Avancé'),
        backgroundColor: DesignTokens.colorPrimary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showHelp ? Icons.help : Icons.help_outline),
            onPressed: () {
              setState(() {
                _showHelp = !_showHelp;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: Icon(
              Icons.file_download,
              color: calculatorState.canExport ? null : Colors.grey,
            ),
            enabled: calculatorState.canExport,
            tooltip: calculatorState.canExport ? 'Exporter les résultats' : 'Aucun résultat à exporter',
            onSelected: _handleExport,
            itemBuilder: (context) => _buildExportMenuItems(calculatorState),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'presets',
                child: ListTile(
                  leading: Icon(Icons.bookmark),
                  title: Text('Modèles d\'actifs'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: ListTile(
                  leading: Icon(Icons.clear),
                  title: Text('Effacer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Historique'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('À propos'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (_showHelp) _buildHelpSection(),
          Expanded(
            child: const EnhancedDepreciationCalculator(),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection() {
    final isMobile = context.isMobile;

    if (isMobile) {
      return ExpansionTile(
        leading: Icon(Icons.lightbulb, color: Colors.orange[700]),
        title: Text(
          'Guide des méthodes d\'amortissement',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.orange[700],
            fontWeight: FontWeight.bold,
          ),
        ),
        children: [
          Container(
            margin: context.responsivePadding(
              mobile: const EdgeInsets.symmetric(horizontal: 12),
              tablet: const EdgeInsets.all(16),
              desktop: const EdgeInsets.all(20),
            ),
            padding: context.responsivePadding(
              mobile: const EdgeInsets.all(12),
              tablet: const EdgeInsets.all(16),
              desktop: const EdgeInsets.all(20),
            ),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: _buildHelpContent(),
          ),
        ],
      );
    }

    return Container(
      margin: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      padding: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.orange[700]),
              const SizedBox(width: 8),
              Text(
                'Guide des méthodes d\'amortissement',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildHelpContent(),
        ],
      ),
    );
  }

  Widget _buildHelpContent() {
    return Column(
      children: [
        _buildMethodHelp(
          'Linéaire',
          'Amortissement constant chaque année. Simple et conforme.',
          Icons.trending_flat,
        ),
        _buildMethodHelp(
          'Dégressive',
          'Amortissement plus important les premières années. Optimise la trésorerie. Support des tableaux multiples pour l\'amortissement dérogatoire.',
          Icons.trending_down,
        ),
        _buildMethodHelp(
          'Somme des chiffres',
          'Amortissement dégressif basé sur la somme des années.',
          Icons.functions,
        ),
        _buildMethodHelp(
          'Unités de production',
          'Amortissement basé sur l\'utilisation réelle de l\'actif.',
          Icons.precision_manufacturing,
        ),
        const SizedBox(height: 8),
        _buildFeatureHelp(
          'Prorata temporis',
          'L\'annuité de la première année est calculée au prorata du nombre de mois d\'utilisation basé sur la date d\'acquisition.',
          Icons.calendar_today,
        ),
        _buildFeatureHelp(
          'Amortissement dérogatoire',
          'Différence entre amortissement fiscal et comptable, permettant l\'optimisation fiscale avec tableaux séparés.',
          Icons.account_balance,
        ),
        _buildFeatureHelp(
          'Années calendaires',
          'Affichage en années calendaires réelles (ex: 2025) au lieu des labels génériques "Année N".',
          Icons.date_range,
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.tips_and_updates, color: Colors.orange[700], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Utilisez le mode comparaison pour voir toutes les méthodes et choisir la plus avantageuse. L\'export Excel propose des feuilles séparées pour chaque tableau.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureHelp(String feature, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.blue[700], size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMethodHelp(String method, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.orange[700], size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  method,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildExportMenuItems(CalculatorState calculatorState) {
    if (!calculatorState.canExport) {
      return [
        const PopupMenuItem(
          enabled: false,
          child: ListTile(
            leading: Icon(Icons.info, color: Colors.grey),
            title: Text('Aucun résultat à exporter', style: TextStyle(color: Colors.grey)),
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ];
    }

    final result = ref.read(depreciationResultProvider);
    final hasMultipleTables = result?.hasMultipleTables ?? false;
    final isMobile = context.isMobile;

    // Simplified menu structure for better UX
    List<PopupMenuEntry<String>> menuItems = [
      // Primary export options (always visible)
      const PopupMenuItem(
        value: 'excel_all',
        child: ListTile(
          leading: Icon(Icons.table_chart, color: Colors.green),
          title: Text('Export Excel'),
          subtitle: Text('Inclut tous les tableaux'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
      const PopupMenuItem(
        value: 'pdf_all',
        child: ListTile(
          leading: Icon(Icons.picture_as_pdf, color: Colors.red),
          title: Text('Export PDF'),
          subtitle: Text('Document complet'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
      
      // Preview option
      const PopupMenuItem(
        value: 'preview',
        child: ListTile(
          leading: Icon(Icons.preview, color: Colors.blue),
          title: Text('Aperçu avant export'),
          subtitle: Text('Vérifier les données'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
    ];

    // Advanced export options in submenu for mobile, expanded for desktop
    if (hasMultipleTables) {
      if (isMobile) {
        menuItems.addAll([
          const PopupMenuDivider(),
          const PopupMenuItem(
            value: 'advanced_export',
            child: ListTile(
              leading: Icon(Icons.more_horiz, color: Colors.orange),
              title: Text('Export Avancé'),
              subtitle: Text('Par tableau individuel'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ]);
      } else {
        menuItems.addAll([
          const PopupMenuDivider(),
          PopupMenuItem(
            value: 'excel_accounting',
            child: ListTile(
              leading: Icon(Icons.account_balance_wallet, color: Colors.blue[700]),
              title: const Text('Tableau Comptable'),
              subtitle: const Text('Amortissement comptable seul'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          PopupMenuItem(
            value: 'excel_fiscal',
            child: ListTile(
              leading: Icon(Icons.account_balance, color: Colors.orange[700]),
              title: const Text('Tableau Fiscal'),
              subtitle: const Text('Amortissement fiscal seul'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          PopupMenuItem(
            value: 'excel_derogatory',
            child: ListTile(
              leading: Icon(Icons.compare_arrows, color: Colors.purple[700]),
              title: const Text('Tableau Dérogatoire'),
              subtitle: const Text('Écarts fiscal/comptable'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ]);
      }
    }

    return menuItems;
  }

  void _handleExport(String format) async {
    // Handle special cases first
    switch (format) {
      case 'preview':
        _showExportPreview();
        return;
      case 'advanced_export':
        _showAdvancedExportBottomSheet();
        return;
    }

    try {
      final exportActions = ref.read(calculatorExportProvider);
      final parts = format.split('_');
      final exportFormat = parts[0];
      final tableType = parts.length > 1 ? parts[1] : 'all';
      
      String successMessage;
      
      // Validate format first
      final supportedFormats = ['excel', 'pdf'];
      if (!supportedFormats.contains(exportFormat)) {
        throw ExportExceptionFactory.unsupportedFormat(exportFormat, supportedFormats);
      }
      
      // Show export progress for large operations
      if (mounted) {
        _showExportProgress();
      }
      
      switch (tableType) {
        case 'accounting':
          await exportActions.exportAccountingTable(format: exportFormat);
          successMessage = 'Tableau comptable exporté avec succès';
          break;
        case 'fiscal':
          await exportActions.exportFiscalTable(format: exportFormat);
          successMessage = 'Tableau fiscal exporté avec succès';
          break;
        case 'derogatory':
          await exportActions.exportDerogatoryTable(format: exportFormat);
          successMessage = 'Tableau dérogatoire exporté avec succès';
          break;
        default:
          await exportActions.exportDepreciation(format: exportFormat);
          successMessage = 'Export complet terminé avec succès';
      }
      
      if (mounted) {
        _hideExportProgress();
        _showSuccessSnackBar(successMessage);
      }
    } on ExportException catch (e) {
      if (mounted) {
        _hideExportProgress();
        _handleExportException(e);
      }
    } on UnimplementedError {
      // Handle legacy UnimplementedError by converting to our custom exception
      if (mounted) {
        _hideExportProgress();
        final exportException = ExportExceptionFactory.notImplemented(
          format.split('_').length > 1 ? format.split('_')[1] : 'general',
          format.split('_')[0],
        );
        _handleExportException(exportException);
      }
    } catch (e) {
      if (mounted) {
        _hideExportProgress();
        _handleGenericExportError(e);
      }
    }
  }

  void _handleExportException(ExportException exception) {
    Color backgroundColor;
    IconData icon;
    String title;
    String message;
    List<String> actionableSteps = [];
    
    // Transform technical errors to user-friendly language with actionable steps
    if (exception is ExportNotImplementedException) {
      backgroundColor = Colors.orange;
      icon = Icons.construction;
      title = 'Cette fonctionnalité arrive bientôt';
      message = 'L\'export PDF est en cours de développement. Utilisez l\'export Excel en attendant.';
      actionableSteps = [
        'Utilisez l\'export Excel pour le moment',
        'Cette fonctionnalité sera disponible dans une prochaine mise à jour'
      ];
    } else if (exception is MissingTableException) {
      backgroundColor = Colors.amber;
      icon = Icons.warning;
      title = 'Calcul incomplet';
      message = 'Il manque des données pour créer l\'export. Vérifiez votre calcul.';
      actionableSteps = [
        'Effectuez d\'abord un calcul d\'amortissement',
        'Vérifiez que tous les champs obligatoires sont remplis'
      ];
    } else if (exception is UnsupportedFormatException) {
      backgroundColor = Colors.red;
      icon = Icons.error;
      title = 'Format non compatible';
      message = 'Ce format d\'export n\'est pas supporté actuellement.';
      actionableSteps = [
        'Utilisez l\'export Excel ou PDF',
        'Contactez le support si vous avez besoin d\'autres formats'
      ];
    } else if (exception is ExportFileSystemException) {
      backgroundColor = Colors.red;
      icon = Icons.folder_off;
      title = 'Problème d\'enregistrement';
      message = 'Impossible d\'enregistrer le fichier sur votre appareil.';
      actionableSteps = [
        'Vérifiez l\'espace de stockage disponible',
        'Redémarrez l\'application et réessayez',
        'Contactez le support si le problème persiste'
      ];
    } else if (exception is ExportPermissionException) {
      backgroundColor = Colors.orange;
      icon = Icons.security;
      title = 'Autorisation requise';
      message = 'L\'application a besoin d\'autorisation pour enregistrer des fichiers.';
      actionableSteps = [
        'Allez dans Paramètres > Applications > HielCompta',
        'Activez l\'autorisation "Stockage" ou "Fichiers"',
        'Revenez dans l\'application et réessayez l\'export'
      ];
    } else if (exception is ExportTimeoutException) {
      backgroundColor = Colors.orange;
      icon = Icons.timer_off;
      title = 'Export trop long';
      message = 'L\'export prend plus de temps que prévu. Réessayez avec moins de données.';
      actionableSteps = [
        'Réduisez le nombre d\'années dans le calcul',
        'Exportez les tableaux individuellement',
        'Vérifiez votre connexion internet'
      ];
    } else if (exception is ExportSizeLimitException) {
      backgroundColor = Colors.orange;
      icon = Icons.data_usage;
      title = 'Export trop volumineux';
      message = 'Le fichier généré serait trop gros. Essayez d\'exporter par parties.';
      actionableSteps = [
        'Exportez les tableaux individuellement',
        'Réduisez la période d\'amortissement',
        'Contactez le support pour augmenter les limites'
      ];
    } else if (exception is InvalidExportDataException) {
      backgroundColor = Colors.red;
      icon = Icons.data_object;
      title = 'Données incohérentes';
      message = 'Certaines données de calcul semblent incorrectes.';
      actionableSteps = [
        'Vérifiez les montants saisis',
        'Refaites le calcul depuis le début',
        'Contactez le support avec une capture d\'écran'
      ];
    } else if (exception is DerogatoryTableException) {
      backgroundColor = Colors.red;
      icon = Icons.account_balance;
      title = 'Problème avec l\'amortissement dérogatoire';
      message = 'Le calcul dérogatoire contient des erreurs.';
      actionableSteps = [
        'Vérifiez les paramètres fiscal et comptable',
        'Assurez-vous que les méthodes sont différentes',
        'Désactivez temporairement le calcul dérogatoire'
      ];
    } else if (exception is ExportTemplateException) {
      backgroundColor = Colors.red;
      icon = Icons.description;
      title = 'Erreur de mise en forme';
      message = 'Impossible de créer le fichier avec la mise en forme demandée.';
      actionableSteps = [
        'Essayez un format d\'export différent',
        'Mettez à jour l\'application',
        'Contactez le support'
      ];
    } else {
      backgroundColor = Colors.red;
      icon = Icons.error;
      title = 'Erreur inattendue';
      message = 'Une erreur s\'est produite lors de l\'export.';
      actionableSteps = [
        'Réessayez dans quelques instants',
        'Redémarrez l\'application si nécessaire',
        'Contactez le support si le problème persiste'
      ];
    }

    _showUserFriendlyErrorDialog(title, message, backgroundColor, icon, actionableSteps, exception);
  }

  void _handleGenericExportError(dynamic error) {
    String errorMessage = 'Erreur inattendue lors de l\'export';
    
    // Handle common error patterns that might not be wrapped in our custom exceptions yet
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('permission') || errorString.contains('access denied')) {
      errorMessage = 'Permissions insuffisantes pour sauvegarder le fichier';
    } else if (errorString.contains('timeout') || errorString.contains('time out')) {
      errorMessage = 'L\'export a pris trop de temps et a été interrompu';
    } else if (errorString.contains('space') || errorString.contains('disk full')) {
      errorMessage = 'Espace disque insuffisant pour l\'export';
    } else if (errorString.contains('network') || errorString.contains('connection')) {
      errorMessage = 'Erreur de réseau durant l\'export';
    } else {
      errorMessage = 'Erreur lors de l\'export: $error';
    }

    _showExportErrorSnackBar(
      'Erreur d\'export',
      errorMessage,
      Colors.red,
      Icons.error,
      null,
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showExportErrorSnackBar(
    String title,
    String message,
    Color backgroundColor,
    IconData icon,
    ExportException? exception,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              message,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 5),
        action: exception != null && exception.details != null
            ? SnackBarAction(
                label: 'Détails',
                textColor: Colors.white,
                onPressed: () => _showExportErrorDialog(title, message, exception),
              )
            : null,
      ),
    );
  }

  void _showExportErrorDialog(String title, String message, ExportException exception) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            if (exception.details != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Détails techniques:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  exception.details!,
                  style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                ),
              ),
            ],
            if (exception is InvalidExportDataException) ...[
              const SizedBox(height: 16),
              const Text(
                'Erreurs de validation:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...exception.validationErrors.map((error) => Padding(
                padding: const EdgeInsets.only(left: 8),
                child: Text('• $error'),
              )),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          if (exception is ExportNotImplementedException)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Could navigate to a feature request form or roadmap
              },
              child: const Text('En savoir plus'),
            ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'presets':
        _showPresetsDialog();
        break;
      case 'clear':
        _showClearConfirmation();
        break;
      case 'history':
        _navigateToHistory();
        break;
      case 'about':
        _showAboutDialog();
        break;
    }
  }

  void _showPresetsDialog() {
    final presets = ref.read(calculatorPresetsProvider)['depreciation'] ?? [];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modèles d\'Actifs'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: presets.length,
            itemBuilder: (context, index) {
              final preset = presets[index];
              return ListTile(
                title: Text(preset.name),
                subtitle: Text(preset.description),
                onTap: () {
                  Navigator.pop(context);
                  _applyPreset(preset);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _applyPreset(CalculatorPreset preset) {
    // TODO: Apply preset to calculator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Modèle "${preset.name}" appliqué')),
    );
  }

  void _showClearConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Effacer les données'),
        content: const Text('Voulez-vous vraiment effacer toutes les données saisies ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(calculatorActionsProvider).clearDepreciationResults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Données effacées')),
              );
            },
            child: const Text('Effacer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _navigateToHistory() {
    Navigator.pushNamed(context, '/calculation-history');
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Calculateur d\'Amortissement Avancé'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Ce calculateur propose quatre méthodes d\'amortissement conformes à la réglementation marocaine :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('• Linéaire : Amortissement constant sur la durée de vie'),
              Text('• Dégressive : Amortissement accéléré les premières années'),
              Text('• Somme des chiffres : Méthode dégressive alternative'),
              Text('• Unités de production : Basé sur l\'utilisation réelle'),
              SizedBox(height: 16),
              Text(
                'Fonctionnalités avancées :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Prorata temporis basé sur la date d\'acquisition'),
              Text('• Affichage en années calendaires'),
              Text('• Support des tableaux multiples pour l\'amortissement dérogatoire'),
              Text('• Comparaison automatique des méthodes'),
              Text('• Conseils d\'optimisation fiscale'),
              Text('• Modèles d\'actifs prédéfinis'),
              Text('• Export Excel amélioré avec feuilles séparées'),
              Text('• Export PDF et Excel sélectif par tableau'),
              SizedBox(height: 16),
              Text(
                'Amortissement dérogatoire :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Calcul automatique des différences fiscal/comptable'),
              Text('• Gestion des provisions et reprises'),
              Text('• Tableaux séparés pour optimisation fiscale'),
              Text('• Export sélectif par type de tableau'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  // Enhanced UX methods for export functionality
  void _showExportPreview() {
    final result = ref.read(depreciationResultProvider);
    if (result == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.preview, color: Colors.blue),
            const SizedBox(width: 8),
            const Text('Aperçu de l\'export'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPreviewSummary(result),
                const SizedBox(height: 16),
                _buildPreviewTablesList(result),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _handleExport('excel_all');
            },
            icon: const Icon(Icons.table_chart),
            label: const Text('Exporter Excel'),
          ),
        ],
      ),
    );
  }

  void _showAdvancedExportBottomSheet() {
    final result = ref.read(depreciationResultProvider);
    if (result == null) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.7,
          maxChildSize: 0.9,
          minChildSize: 0.5,
          builder: (context, scrollController) => Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Title
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.more_horiz, color: Colors.orange[700]),
                    const SizedBox(width: 8),
                    Text(
                      'Export Avancé',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: ListView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  children: [
                    _buildAdvancedExportOption(
                      'Tableau Comptable',
                      'Amortissement selon les règles comptables',
                      Icons.account_balance_wallet,
                      Colors.blue[700]!,
                      () => _handleExport('excel_accounting'),
                    ),
                    _buildAdvancedExportOption(
                      'Tableau Fiscal',
                      'Amortissement selon les règles fiscales',
                      Icons.account_balance,
                      Colors.orange[700]!,
                      () => _handleExport('excel_fiscal'),
                    ),
                    if (result.hasMultipleTables)
                      _buildAdvancedExportOption(
                        'Tableau Dérogatoire',
                        'Écarts entre fiscal et comptable',
                        Icons.compare_arrows,
                        Colors.purple[700]!,
                        () => _handleExport('excel_derogatory'),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedExportOption(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.file_download),
        onTap: () {
          Navigator.pop(context);
          onTap();
        },
      ),
    );
  }

  Widget _buildPreviewSummary(dynamic result) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résumé du calcul',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text('Méthode: ${result.summary?.method?.displayName ?? "N/A"}'),
            Text('Durée: ${result.summary?.totalYears ?? 0} années'),
            Text('Total amortissement: ${result.summary?.totalDepreciation?.toStringAsFixed(2) ?? "0"} DH'),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewTablesList(dynamic result) {
    final tables = <String>[];
    if (result.amortizationTable?.isNotEmpty == true) tables.add('Tableau principal');
    if (result.accountingAmortizationTable?.isNotEmpty == true) tables.add('Tableau comptable');
    if (result.fiscalAmortizationTable?.isNotEmpty == true) tables.add('Tableau fiscal');
    if (result.derogatoryAmortizationTable?.isNotEmpty == true) tables.add('Tableau dérogatoire');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tableaux inclus',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...tables.map((table) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  const Icon(Icons.check, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  Text(table),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  void _showExportProgress() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text('Export en cours...'),
          ],
        ),
      ),
    );
  }

  void _hideExportProgress() {
    Navigator.of(context, rootNavigator: true).pop();
  }

  void _showUserFriendlyErrorDialog(String title, String message, Color color, IconData icon, List<String> actionableSteps, ExportException? exception) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(message),
              const SizedBox(height: 16),
              const Text(
                'Que faire maintenant :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...actionableSteps.map((step) => Padding(
                padding: const EdgeInsets.only(left: 8, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(child: Text(step)),
                  ],
                ),
              )),
              if (exception?.details != null) ...[
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () => _showTechnicalDetails(exception!),
                  child: const Text('Voir les détails techniques'),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Compris'),
          ),
          if (actionableSteps.isNotEmpty)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Could implement specific actions based on exception type
              },
              child: const Text('Réessayer'),
            ),
        ],
      ),
    );
  }

  void _showTechnicalDetails(ExportException exception) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Détails techniques'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Type: ${exception.runtimeType}'),
              const SizedBox(height: 8),
              if (exception.details != null)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    exception.details!,
                    style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                  ),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
