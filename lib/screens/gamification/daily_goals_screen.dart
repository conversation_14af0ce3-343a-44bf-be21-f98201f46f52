// DailyGoalsScreen for Moroccan Accounting App
// Dedicated screen for managing daily goals with goal setting, history, and statistics.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/daily_goal_provider.dart';
import '../../widgets/gamification/daily_goal_widget.dart';
import '../../models/gamification/daily_goal_data.dart';
import '../../theme/design_tokens.dart';

class DailyGoalsScreen extends ConsumerStatefulWidget {
  const DailyGoalsScreen({super.key});

  @override
  ConsumerState<DailyGoalsScreen> createState() => _DailyGoalsScreenState();
}

class _DailyGoalsScreenState extends ConsumerState<DailyGoalsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showGoalSettingDialog() {
    DailyGoalType selectedType = DailyGoalType.timeBased;
    int targetValue = 30;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Set Daily Goal'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Choose your daily learning goal:'),
              SizedBox(height: 16),
              
              // Goal type selection
              DropdownButtonFormField<DailyGoalType>(
                value: selectedType,
                decoration: InputDecoration(
                  labelText: 'Goal Type',
                  border: OutlineInputBorder(),
                ),
                items: DailyGoalType.values.map((type) {
                  String label;
                  String unit;
                  switch (type) {
                    case DailyGoalType.timeBased:
                      label = 'Study Time';
                      unit = 'minutes';
                      break;
                    case DailyGoalType.sectionBased:
                      label = 'Sections to Read';
                      unit = 'sections';
                      break;
                    case DailyGoalType.quizBased:
                      label = 'Quizzes to Complete';
                      unit = 'quizzes';
                      break;
                  }
                  return DropdownMenuItem(
                    value: type,
                    child: Text('$label ($unit)'),
                  );
                }).toList(),
                onChanged: (type) {
                  if (type != null) {
                    setDialogState(() {
                      selectedType = type;
                      // Set default values based on type
                      switch (type) {
                        case DailyGoalType.timeBased:
                          targetValue = 30;
                          break;
                        case DailyGoalType.sectionBased:
                          targetValue = 3;
                          break;
                        case DailyGoalType.quizBased:
                          targetValue = 2;
                          break;
                      }
                    });
                  }
                },
              ),
              SizedBox(height: 16),
              
              // Target value input
              TextFormField(
                initialValue: targetValue.toString(),
                decoration: InputDecoration(
                  labelText: 'Target Value',
                  border: OutlineInputBorder(),
                  suffixText: _getUnitText(selectedType),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  targetValue = int.tryParse(value) ?? targetValue;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              child: Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              child: Text('Set Goal'),
              onPressed: () {
                // Create new goal
                final newGoal = DailyGoalData(
                  goalType: selectedType,
                  targetValue: targetValue,
                  currentProgress: 0,
                  goalDate: DateTime.now(),
                  lastResetDate: DateTime.now(),
                  streakCount: 0,
                  bestStreak: 0,
                );
                
                // Update provider (you would implement this)
                ref.read(currentDailyGoalProvider.notifier).state = newGoal;
                
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Daily goal set successfully!')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getUnitText(DailyGoalType type) {
    switch (type) {
      case DailyGoalType.timeBased:
        return 'min';
      case DailyGoalType.sectionBased:
        return 'sections';
      case DailyGoalType.quizBased:
        return 'quizzes';
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentGoal = ref.watch(currentDailyGoalProvider);
    final goalProgress = ref.watch(dailyGoalProgressProvider);
    final goalStreak = ref.watch(goalStreakProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Daily Goals'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: _showGoalSettingDialog,
            tooltip: 'Set New Goal',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(icon: Icon(Icons.today), text: 'Today'),
            Tab(icon: Icon(Icons.history), text: 'History'),
            Tab(icon: Icon(Icons.analytics), text: 'Statistics'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Today's goal
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                if (currentGoal != null)
                  DailyGoalWidget(
                    goal: currentGoal,
                    onSetGoal: _showGoalSettingDialog,
                    onCelebrate: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: Text('🎉 Congratulations!'),
                          content: Text('You\'ve completed your daily goal! Keep up the great work!'),
                          actions: [
                            TextButton(
                              child: Text('Awesome!'),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                else
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        children: [
                          Icon(Icons.flag, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'No Daily Goal Set',
                            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Set a daily goal to stay motivated and track your progress!',
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          SizedBox(height: 16),
                          ElevatedButton.icon(
                            icon: Icon(Icons.add),
                            label: Text('Set Your First Goal'),
                            onPressed: _showGoalSettingDialog,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: DesignTokens.primaryColor,
                              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                
                SizedBox(height: 20),
                
                // Quick stats cards
                Row(
                  children: [
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              Icon(Icons.local_fire_department, color: DesignTokens.streakColor, size: 32),
                              SizedBox(height: 8),
                              Text('$goalStreak', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                              Text('Day Streak'),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              Icon(Icons.trending_up, color: DesignTokens.successColor, size: 32),
                              SizedBox(height: 8),
                              Text('${(goalProgress * 100).toInt()}%', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                              Text('Progress'),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // History
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.history, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'Goal History',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('Your past goal completions will appear here'),
                SizedBox(height: 16),
                Text('Feature coming soon!', style: TextStyle(color: Colors.grey)),
              ],
            ),
          ),
          
          // Statistics
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Goal Statistics',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 16),
                        _buildStatRow('Total Goals Set', '0'),
                        _buildStatRow('Goals Completed', '0'),
                        _buildStatRow('Current Streak', '$goalStreak days'),
                        _buildStatRow('Best Streak', '0 days'),
                        _buildStatRow('Success Rate', '0%'),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Weekly Progress',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 16),
                        SizedBox(
                          height: 200,
                          child: Center(
                            child: Text(
                              'Chart coming soon!',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(fontWeight: FontWeight.bold, color: DesignTokens.primaryColor),
          ),
        ],
      ),
    );
  }
}

// End of daily_goals_screen.dart
