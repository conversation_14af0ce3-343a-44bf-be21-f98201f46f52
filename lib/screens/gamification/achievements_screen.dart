// AchievementsScreen for Moroccan Accounting App
// Dedicated screen for viewing and managing achievements with filtering and sharing.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/achievement_provider.dart';
import '../../widgets/gamification/achievement_gallery.dart';
import '../../widgets/gamification/social_share_widget.dart';
import '../../models/gamification/achievement_definitions.dart';
import '../../models/gamification/social_share_data.dart';
import '../../services/social_sharing_service.dart';

class AchievementsScreen extends ConsumerStatefulWidget {
  const AchievementsScreen({super.key});

  @override
  ConsumerState<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends ConsumerState<AchievementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final SocialSharingService _sharingService = SocialSharingService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAchievementShareDialog(AchievementDefinition achievement) {
    final shareData = SocialShareData(
      achievementId: achievement.id,
      platform: SocialPlatform.other,
      shareText: 'I just unlocked "${achievement.title}" in Moroccan Accounting! 🏆',
      shareImage: null,
      shareCount: 0,
      shareDate: DateTime.now(),
      isPrivate: false,
      userPrefersSharing: true,
    );

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Share Achievement',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              SocialShareWidget(
                shareData: shareData,
                onPlatformSelected: (platform) {
                  shareData.platform = platform;
                  setState(() {});
                },
                onShare: () async {
                  await _sharingService.shareAchievement(shareData);
                  if (mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Achievement shared!')),
                    );
                  }
                },
                onPrivacyToggle: () {
                  shareData.isPrivate = !shareData.isPrivate;
                  setState(() {});
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final achievements = ref.watch(achievementsProvider);
    final unlockedAchievements = ref.watch(unlockedAchievementsProvider);
    final achievementPoints = ref.watch(achievementPointsProvider);
    final unlockedIds = unlockedAchievements.map((a) => a.id).toSet();

    return Scaffold(
      appBar: AppBar(
        title: Text('Achievements'),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Chip(
              label: Text('$achievementPoints pts'),
              backgroundColor: Colors.amber.withValues(alpha: 0.2),
              avatar: Icon(Icons.star, color: Colors.amber),
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: Icon(Icons.emoji_events),
              text: 'All (${achievements.length})',
            ),
            Tab(
              icon: Icon(Icons.check_circle),
              text: 'Unlocked (${unlockedAchievements.length})',
            ),
            Tab(
              icon: Icon(Icons.lock),
              text: 'Locked (${achievements.length - unlockedAchievements.length})',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // All achievements
          AchievementGallery(
            achievements: achievements,
            unlockedIds: unlockedIds,
            onShare: _showAchievementShareDialog,
          ),
          
          // Unlocked achievements
          AchievementGallery(
            achievements: unlockedAchievements,
            unlockedIds: unlockedIds,
            onShare: _showAchievementShareDialog,
          ),
          
          // Locked achievements
          AchievementGallery(
            achievements: achievements.where((a) => !unlockedIds.contains(a.id)).toList(),
            unlockedIds: unlockedIds,
            onShare: null, // Can't share locked achievements
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Achievement Statistics'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatRow('Total Achievements', '${achievements.length}'),
                  _buildStatRow('Unlocked', '${unlockedAchievements.length}'),
                  _buildStatRow('Locked', '${achievements.length - unlockedAchievements.length}'),
                  _buildStatRow('Total Points', '$achievementPoints'),
                  SizedBox(height: 16),
                  Text('Progress: ${((unlockedAchievements.length / achievements.length) * 100).toStringAsFixed(1)}%'),
                  SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: unlockedAchievements.length / achievements.length,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  child: Text('Close'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          );
        },
        icon: Icon(Icons.analytics),
        label: Text('Stats'),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}

// End of achievements_screen.dart
