import 'dart:math' as math;
import 'dart:async';
import 'dart:developer' as developer;

/// Utility class for safe floating-point operations and precision handling
class FloatingPointUtils {
  // Standard epsilon for floating-point comparisons
  static const double defaultEpsilon = 1e-10;
  
  // Financial precision epsilon (for monetary calculations)
  static const double financialEpsilon = 0.01;
  
  // Maximum reasonable financial value to prevent overflow
  static const double maxFinancialValue = 1e12;
  
  /// Safe division that handles division by zero
  static double safeDivide(double numerator, double denominator, {double fallback = 0.0}) {
    if (isZero(denominator)) {
      return fallback;
    }
    
    final result = numerator / denominator;
    
    // Check for overflow/underflow
    if (!result.isFinite) {
      return fallback;
    }
    
    return result;
  }
  
  /// Check if a value is effectively zero using epsilon comparison
  static bool isZero(double value, {double epsilon = defaultEpsilon}) {
    return value.abs() < epsilon;
  }
  
  /// Compare two floating-point numbers for equality using epsilon
  static bool areEqual(double a, double b, {double epsilon = defaultEpsilon}) {
    return (a - b).abs() < epsilon;
  }
  
  /// Check if a value is within a reasonable range for financial calculations
  static bool isReasonableFinancialValue(double value) {
    return value.isFinite && 
           value.abs() <= maxFinancialValue && 
           value >= -maxFinancialValue;
  }
  
  /// Round to specified precision with proper handling of edge cases
  static double roundToPrecision(double value, int precision) {
    if (!value.isFinite) {
      return 0.0;
    }
    
    if (precision < 0) {
      precision = 0;
    }
    
    final factor = math.pow(10, precision).toDouble();
    return (value * factor).round() / factor;
  }
  
  /// Clamp a value between min and max bounds
  static double clamp(double value, double min, double max) {
    if (!value.isFinite) {
      return min;
    }
    return math.max(min, math.min(max, value));
  }
  
  /// Check if a calculation has converged based on relative change
  static bool hasConverged(double current, double previous, {double threshold = 1e-6}) {
    if (isZero(previous)) {
      return isZero(current, epsilon: threshold);
    }
    
    final relativeChange = ((current - previous) / previous).abs();
    return relativeChange < threshold;
  }
  
  /// Detect if values are oscillating (stuck in a loop)
  static bool isOscillating(List<double> recentValues, {double threshold = 1e-6}) {
    if (recentValues.length < 4) {
      return false;
    }
    
    // Check if the last 4 values show a pattern (A, B, A, B)
    final len = recentValues.length;
    final val1 = recentValues[len - 4];
    final val2 = recentValues[len - 3];
    final val3 = recentValues[len - 2];
    final val4 = recentValues[len - 1];
    
    return areEqual(val1, val3, epsilon: threshold) && 
           areEqual(val2, val4, epsilon: threshold) &&
           !areEqual(val1, val2, epsilon: threshold);
  }
  
  /// Safe square root that handles negative values
  static double safeSqrt(double value, {double fallback = 0.0}) {
    if (value < 0 || !value.isFinite) {
      return fallback;
    }
    return math.sqrt(value);
  }
  
  /// Safe power operation that handles edge cases
  static double safePow(double base, double exponent, {double fallback = 0.0}) {
    try {
      if (!base.isFinite || !exponent.isFinite) {
        return fallback;
      }
      
      final result = math.pow(base, exponent).toDouble();
      
      if (!result.isFinite) {
        return fallback;
      }
      
      return result;
    } catch (e) {
      return fallback;
    }
  }
}

/// Calculation limits and constraints to prevent extreme values
class CalculationLimits {
  // Maximum asset cost to prevent overflow in calculations
  static const double maxAssetCost = 1e9; // 1 billion
  
  // Maximum useful life in years
  static const int maxUsefulLifeYears = 100;
  
  // Maximum degressive coefficient
  static const double maxDegressiveCoefficient = 10.0;
  
  // Maximum total units for units of production method
  static const double maxTotalUnits = 1e9;
  
  // Maximum annual units produced
  static const double maxAnnualUnits = 1e8;
  
  // Maximum number of calculation iterations
  static const int maxIterations = 1000;
  
  // Maximum calculation time in seconds
  static const int maxCalculationTimeSeconds = 30;
  
  /// Validate asset cost is within reasonable limits
  static bool isValidAssetCost(double cost) {
    return cost > 0 && 
           cost <= maxAssetCost && 
           FloatingPointUtils.isReasonableFinancialValue(cost);
  }
  
  /// Validate useful life is within reasonable limits
  static bool isValidUsefulLife(int years) {
    return years > 0 && years <= maxUsefulLifeYears;
  }
  
  /// Validate degressive coefficient is within reasonable limits
  static bool isValidDegressiveCoefficient(double coefficient) {
    return coefficient > 1.0 && 
           coefficient <= maxDegressiveCoefficient &&
           coefficient.isFinite;
  }
  
  /// Validate total units is within reasonable limits
  static bool isValidTotalUnits(double units) {
    return units > 0 && 
           units <= maxTotalUnits && 
           units.isFinite;
  }
  
  /// Validate annual units is within reasonable limits
  static bool isValidAnnualUnits(double units) {
    return units >= 0 && 
           units <= maxAnnualUnits && 
           units.isFinite;
  }
}

/// Timeout utilities for long-running calculations
class CalculationTimeout {
  /// Execute a calculation with a timeout
  static Future<T> withTimeout<T>(
    Future<T> Function() calculation,
    Duration timeout, {
    T? fallback,
  }) async {
    try {
      return await calculation().timeout(timeout);
    } on TimeoutException {
      if (fallback != null) {
        return fallback;
      }
      throw TimeoutException('Calculation timed out', timeout);
    }
  }
  
  /// Execute a synchronous calculation with timeout using isolates
  static Future<T> withTimeoutSync<T>(
    T Function() calculation,
    Duration timeout, {
    T? fallback,
  }) async {
    final completer = Completer<T>();
    Timer? timer;
    
    // Start the calculation
    try {
      final result = calculation();
      if (!completer.isCompleted) {
        completer.complete(result);
      }
    } catch (e) {
      if (!completer.isCompleted) {
        completer.completeError(e);
      }
    }
    
    // Set up timeout
    timer = Timer(timeout, () {
      if (!completer.isCompleted) {
        if (fallback != null) {
          completer.complete(fallback);
        } else {
          completer.completeError(TimeoutException('Calculation timed out', timeout));
        }
      }
    });
    
    try {
      final result = await completer.future;
      timer.cancel();
      return result;
    } catch (e) {
      timer.cancel();
      rethrow;
    }
  }
}

/// Convergence detection utilities for iterative calculations
class ConvergenceDetector {
  final List<double> _history = [];
  final int _maxHistorySize;
  final double _convergenceThreshold;
  final double _oscillationThreshold;
  
  ConvergenceDetector({
    int maxHistorySize = 10,
    double convergenceThreshold = 1e-6,
    double oscillationThreshold = 1e-6,
  }) : _maxHistorySize = maxHistorySize,
       _convergenceThreshold = convergenceThreshold,
       _oscillationThreshold = oscillationThreshold;
  
  /// Add a new value to the convergence history
  void addValue(double value) {
    _history.add(value);
    
    // Keep history size manageable
    if (_history.length > _maxHistorySize) {
      _history.removeAt(0);
    }
  }
  
  /// Check if the calculation has converged
  bool hasConverged() {
    if (_history.length < 2) {
      return false;
    }
    
    final current = _history.last;
    final previous = _history[_history.length - 2];
    
    return FloatingPointUtils.hasConverged(current, previous, threshold: _convergenceThreshold);
  }
  
  /// Check if the calculation is oscillating
  bool isOscillating() {
    return FloatingPointUtils.isOscillating(_history, threshold: _oscillationThreshold);
  }
  
  /// Check if the calculation is stuck (no significant change)
  bool isStuck({int minSamples = 5}) {
    if (_history.length < minSamples) {
      return false;
    }
    
    final recent = _history.sublist(_history.length - minSamples);
    final first = recent.first;
    
    return recent.every((value) => 
        FloatingPointUtils.areEqual(value, first, epsilon: _convergenceThreshold));
  }
  
  /// Get the current convergence rate
  double getConvergenceRate() {
    if (_history.length < 2) {
      return double.infinity;
    }
    
    final current = _history.last;
    final previous = _history[_history.length - 2];
    
    if (FloatingPointUtils.isZero(previous)) {
      return FloatingPointUtils.isZero(current) ? 0.0 : double.infinity;
    }
    
    return ((current - previous) / previous).abs();
  }
  
  /// Reset the convergence detector
  void reset() {
    _history.clear();
  }
  
  /// Get the history of values
  List<double> get history => List.unmodifiable(_history);
}

/// Error recovery utilities for calculation failures
class CalculationErrorRecovery {
  /// Attempt to recover from a calculation error with fallback strategies
  static T recoverFromError<T>(
    T Function() primaryCalculation,
    List<T Function()> fallbackStrategies, {
    T? ultimateFallback,
  }) {
    // Try primary calculation
    try {
      return primaryCalculation();
    } catch (e) {
      // Try fallback strategies in order
      for (final fallback in fallbackStrategies) {
        try {
          return fallback();
        } catch (fallbackError) {
          // Continue to next fallback
          continue;
        }
      }
      
      // If all fallbacks fail, return ultimate fallback or rethrow
      if (ultimateFallback != null) {
        return ultimateFallback;
      }
      
      rethrow;
    }
  }
  
  /// Sanitize input values to prevent calculation errors
  static double sanitizeFinancialInput(double value, {
    double min = 0.0,
    double max = CalculationLimits.maxAssetCost,
    double fallback = 0.0,
  }) {
    if (!value.isFinite) {
      return fallback;
    }
    
    return FloatingPointUtils.clamp(value, min, max);
  }
  
  /// Validate and sanitize a list of financial values
  static List<double> sanitizeFinancialList(List<double> values, {
    double min = 0.0,
    double max = CalculationLimits.maxAssetCost,
    double fallback = 0.0,
  }) {
    return values.map((value) => 
        sanitizeFinancialInput(value, min: min, max: max, fallback: fallback)
    ).toList();
  }
  
  /// Create a safe wrapper for mathematical operations
  static double safeOperation(
    double Function() operation, {
    double fallback = 0.0,
    bool logErrors = true,
  }) {
    try {
      final result = operation();
      
      if (!FloatingPointUtils.isReasonableFinancialValue(result)) {
        return fallback;
      }
      
      return result;
    } catch (e) {
      if (logErrors) {
        // In a real app, you might want to use a proper logging framework
        // Using dart:developer for better logging
        developer.log('Mathematical operation failed', error: e);
      }
      return fallback;
    }
  }
}

/// Validation result for mathematical operations
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  
  const ValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });
  
  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  
  String? get primaryError => errors.isNotEmpty ? errors.first : null;
  String? get primaryWarning => warnings.isNotEmpty ? warnings.first : null;
  
  /// Combine multiple validation results
  static ValidationResult combine(List<ValidationResult> results) {
    final allErrors = <String>[];
    final allWarnings = <String>[];
    bool allValid = true;
    
    for (final result in results) {
      if (!result.isValid) {
        allValid = false;
      }
      allErrors.addAll(result.errors);
      allWarnings.addAll(result.warnings);
    }
    
    return ValidationResult(
      isValid: allValid,
      errors: allErrors,
      warnings: allWarnings,
    );
  }
}

/// Utility class for standardized calculation precision and rounding
class CalculationUtils {
  // Standard precision for monetary calculations (2 decimal places)
  static const int monetaryPrecision = 2;
  
  // Standard precision for percentage calculations (4 decimal places)
  static const int percentagePrecision = 4;
  
  // Standard precision for tax rate calculations (2 decimal places)
  static const int taxRatePrecision = 2;

  /// Round a monetary amount to standard precision (2 decimal places)
  static double roundMonetary(double value) {
    return _roundToPrecision(value, monetaryPrecision);
  }

  /// Round a percentage to standard precision (4 decimal places)
  static double roundPercentage(double value) {
    return _roundToPrecision(value, percentagePrecision);
  }

  /// Round a tax rate to standard precision (2 decimal places)
  static double roundTaxRate(double value) {
    return _roundToPrecision(value, taxRatePrecision);
  }

  /// Round to specified number of decimal places
  static double _roundToPrecision(double value, int precision) {
    final factor = math.pow(10, precision);
    return (value * factor).round() / factor;
  }

  /// Format monetary amount for display
  static String formatMonetary(double value, {String currency = 'DH'}) {
    final rounded = roundMonetary(value);
    return '${rounded.toStringAsFixed(monetaryPrecision)} $currency';
  }

  /// Format percentage for display
  static String formatPercentage(double value, {bool includeSymbol = true}) {
    final rounded = roundPercentage(value);
    final formatted = rounded.toStringAsFixed(percentagePrecision);
    return includeSymbol ? '$formatted%' : formatted;
  }

  /// Calculate percentage of a value with proper rounding
  static double calculatePercentage(double value, double percentage) {
    final result = value * (percentage / 100);
    return roundMonetary(result);
  }

  /// Calculate tax amount with proper rounding
  static double calculateTax(double taxableAmount, double taxRate) {
    final result = taxableAmount * (taxRate / 100);
    return roundMonetary(result);
  }

  /// Calculate net amount after deducting tax
  static double calculateNetAmount(double grossAmount, double taxAmount) {
    final result = grossAmount - taxAmount;
    return roundMonetary(result);
  }

  /// Calculate HT amount from TTC amount and VAT rate
  static double calculateHTFromTTC(double ttcAmount, double vatRate) {
    final result = ttcAmount / (1 + (vatRate / 100));
    return roundMonetary(result);
  }

  /// Calculate TTC amount from HT amount and VAT rate
  static double calculateTTCFromHT(double htAmount, double vatRate) {
    final result = htAmount * (1 + (vatRate / 100));
    return roundMonetary(result);
  }

  /// Calculate VAT amount from HT amount and VAT rate
  static double calculateVATFromHT(double htAmount, double vatRate) {
    final result = htAmount * (vatRate / 100);
    return roundMonetary(result);
  }

  /// Calculate VAT amount from TTC amount and VAT rate
  static double calculateVATFromTTC(double ttcAmount, double vatRate) {
    final htAmount = calculateHTFromTTC(ttcAmount, vatRate);
    return roundMonetary(ttcAmount - htAmount);
  }

  /// Validate that a monetary amount is positive
  static bool isValidMonetaryAmount(double amount) {
    return amount >= 0 && amount.isFinite;
  }

  /// Validate that a percentage is within valid range (0-100)
  static bool isValidPercentage(double percentage) {
    return percentage >= 0 && percentage <= 100 && percentage.isFinite;
  }

  /// Validate that a tax rate is within reasonable range (0-50)
  static bool isValidTaxRate(double taxRate) {
    return taxRate >= 0 && taxRate <= 50 && taxRate.isFinite;
  }

  /// Clamp a value to ensure it's not negative (useful for tax calculations)
  static double ensureNonNegative(double value) {
    return math.max(0.0, value);
  }

  /// Calculate progressive tax using brackets
  static double calculateProgressiveTax(
    double taxableIncome,
    List<Map<String, dynamic>> brackets,
  ) {
    double totalTax = 0.0;
    double remainingIncome = taxableIncome;

    for (final bracket in brackets) {
      if (remainingIncome <= 0) break;

      final min = (bracket['min'] as num).toDouble();
      final max = bracket['max'] != null ? (bracket['max'] as num).toDouble() : null;
      final rate = (bracket['rate'] as num).toDouble();

      double taxableAmount;
      if (max == null) {
        // Last bracket - tax all remaining income
        taxableAmount = remainingIncome;
      } else {
        // Calculate taxable amount for this bracket
        final bracketSize = max - min;
        taxableAmount = math.min(remainingIncome, bracketSize);
      }

      if (taxableAmount > 0) {
        final bracketTax = calculateTax(taxableAmount, rate);
        totalTax += bracketTax;
        remainingIncome -= taxableAmount;
      }
    }

    return roundMonetary(totalTax);
  }

  /// Calculate social security contributions with ceiling
  static double calculateSocialContribution(
    double salary,
    double rate,
    double? ceiling,
  ) {
    final baseAmount = ceiling != null ? math.min(salary, ceiling) : salary;
    return calculatePercentage(baseAmount, rate);
  }

  /// Calculate professional expenses with rate and ceiling
  static double calculateProfessionalExpenses(
    double baseAmount,
    double rate,
    double ceiling,
  ) {
    final calculated = calculatePercentage(baseAmount, rate);
    return math.min(calculated, ceiling);
  }

  // TVA Line Item and Invoice Calculation Methods

  /// Calculate totals for a single line item
  /// Returns a record with (totalHT, totalTVA, totalTTC)
  static ({double totalHT, double totalTVA, double totalTTC}) calculateLineItemTotals(
    double quantity,
    double unitPrice,
    double vatRate,
  ) {
    final totalHT = roundMonetary(quantity * unitPrice);
    final totalTVA = calculateVATFromHT(totalHT, vatRate);
    final totalTTC = roundMonetary(totalHT + totalTVA);
    
    return (
      totalHT: totalHT,
      totalTVA: totalTVA,
      totalTTC: totalTTC,
    );
  }

  /// Calculate comprehensive invoice totals from a list of line items
  /// Returns a record with totals and VAT breakdown
  static ({
    double totalHT,
    double totalTVA,
    double totalTTC,
    Map<double, double> vatBreakdown,
    int exemptItemsCount
  }) calculateInvoiceTotals(List<dynamic> items) {
    double totalHT = 0.0;
    double totalTVA = 0.0;
    Map<double, double> vatBreakdown = {};
    int exemptItemsCount = 0;

    for (final item in items) {
      // Access item properties dynamically since TvaLineItem is defined elsewhere
      final itemTotalHT = (item as dynamic).totalHT as double;
      final itemTotalTVA = (item as dynamic).totalTVA as double;
      final vatRate = (item as dynamic).vatRate as double;
      final isExempt = (item as dynamic).isExempt as bool;

      totalHT += itemTotalHT;
      totalTVA += itemTotalTVA;

      // Build VAT breakdown by rate
      if (vatBreakdown.containsKey(vatRate)) {
        vatBreakdown[vatRate] = vatBreakdown[vatRate]! + itemTotalTVA;
      } else {
        vatBreakdown[vatRate] = itemTotalTVA;
      }

      if (isExempt) {
        exemptItemsCount++;
      }
    }

    // Round final totals
    totalHT = roundMonetary(totalHT);
    totalTVA = roundMonetary(totalTVA);
    final totalTTC = roundMonetary(totalHT + totalTVA);

    // Round VAT breakdown values
    vatBreakdown = vatBreakdown.map(
      (rate, amount) => MapEntry(rate, roundMonetary(amount)),
    );

    return (
      totalHT: totalHT,
      totalTVA: totalTVA,
      totalTTC: totalTTC,
      vatBreakdown: vatBreakdown,
      exemptItemsCount: exemptItemsCount,
    );
  }

  /// Validate that a quantity is positive and finite
  static bool isValidQuantity(double quantity) {
    return quantity > 0 && quantity.isFinite;
  }

  /// Validate that a unit price is non-negative and finite
  /// Allows zero unit price for free items
  static bool isValidUnitPrice(double unitPrice) {
    return unitPrice >= 0 && unitPrice.isFinite;
  }

  /// Enhanced safe division using FloatingPointUtils
  static double safeDivide(double numerator, double denominator, {double fallback = 0.0}) {
    return FloatingPointUtils.safeDivide(numerator, denominator, fallback: fallback);
  }

  /// Enhanced floating-point comparison
  static bool areEqual(double a, double b, {double epsilon = FloatingPointUtils.financialEpsilon}) {
    return FloatingPointUtils.areEqual(a, b, epsilon: epsilon);
  }

  /// Enhanced validation for financial values
  static bool isValidFinancialValue(double value) {
    return FloatingPointUtils.isReasonableFinancialValue(value) && value >= 0;
  }

  /// Safe mathematical operation wrapper
  static double safeCalculation(double Function() calculation, {double fallback = 0.0}) {
    return CalculationErrorRecovery.safeOperation(calculation, fallback: fallback);
  }

  /// Validate calculation inputs before processing
  static ValidationResult validateCalculationInputs({
    double? assetCost,
    int? usefulLife,
    double? degressiveCoefficient,
    double? totalUnits,
    List<double>? annualUnits,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (assetCost != null && !CalculationLimits.isValidAssetCost(assetCost)) {
      errors.add('Coût de l\'actif invalide: doit être positif et inférieur à ${CalculationLimits.maxAssetCost}');
    }

    if (usefulLife != null && !CalculationLimits.isValidUsefulLife(usefulLife)) {
      errors.add('Durée de vie invalide: doit être entre 1 et ${CalculationLimits.maxUsefulLifeYears} années');
    }

    if (degressiveCoefficient != null && !CalculationLimits.isValidDegressiveCoefficient(degressiveCoefficient)) {
      errors.add('Coefficient dégressif invalide: doit être supérieur à 1.0 et inférieur à ${CalculationLimits.maxDegressiveCoefficient}');
    }

    if (totalUnits != null && !CalculationLimits.isValidTotalUnits(totalUnits)) {
      errors.add('Nombre total d\'unités invalide: doit être positif et inférieur à ${CalculationLimits.maxTotalUnits}');
    }

    if (annualUnits != null) {
      for (int i = 0; i < annualUnits.length; i++) {
        if (!CalculationLimits.isValidAnnualUnits(annualUnits[i])) {
          errors.add('Unités annuelles invalides pour l\'année ${i + 1}: doit être non-négatif et inférieur à ${CalculationLimits.maxAnnualUnits}');
        }
      }
    }

    // Add warnings for edge cases
    if (assetCost != null && assetCost > CalculationLimits.maxAssetCost * 0.8) {
      warnings.add('Coût d\'actif très élevé - les calculs pourraient être lents');
    }

    if (usefulLife != null && usefulLife > 50) {
      warnings.add('Durée de vie très longue - vérifiez la cohérence avec la réglementation');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Execute calculation with timeout protection
  static Future<T> calculateWithTimeout<T>(
    Future<T> Function() calculation,
    Duration timeout, {
    T? fallback,
  }) {
    return CalculationTimeout.withTimeout(calculation, timeout, fallback: fallback);
  }

  /// Execute synchronous calculation with timeout protection
  static Future<T> calculateSyncWithTimeout<T>(
    T Function() calculation,
    Duration timeout, {
    T? fallback,
  }) {
    return CalculationTimeout.withTimeoutSync(calculation, timeout, fallback: fallback);
  }
}
