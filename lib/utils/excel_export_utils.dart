import 'dart:io';
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import '../models/immobilisations/amortization_row.dart';
import '../exceptions/export_exceptions.dart';

/// Export settings for customizing Excel output
class ExportSettings {
  final bool includeCharts;
  final bool includeFormulas;
  final bool useAlternatingRowColors;
  final bool freezePanes;
  final bool includeSummary;
  final String? companyName;
  final String? companyLogo;
  final String language;
  final Map<String, String>? customHeaders;
  final Map<String, String>? customFooters;

  const ExportSettings({
    this.includeCharts = true,
    this.includeFormulas = true,
    this.useAlternatingRowColors = true,
    this.freezePanes = true,
    this.includeSummary = true,
    this.companyName,
    this.companyLogo,
    this.language = 'fr',
    this.customHeaders,
    this.customFooters,
  });

  ExportSettings copyWith({
    bool? includeCharts,
    bool? includeFormulas,
    bool? useAlternatingRowColors,
    bool? freezePanes,
    bool? includeSummary,
    String? companyName,
    String? companyLogo,
    String? language,
    Map<String, String>? customHeaders,
    Map<String, String>? customFooters,
  }) {
    return ExportSettings(
      includeCharts: includeCharts ?? this.includeCharts,
      includeFormulas: includeFormulas ?? this.includeFormulas,
      useAlternatingRowColors: useAlternatingRowColors ?? this.useAlternatingRowColors,
      freezePanes: freezePanes ?? this.freezePanes,
      includeSummary: includeSummary ?? this.includeSummary,
      companyName: companyName ?? this.companyName,
      companyLogo: companyLogo ?? this.companyLogo,
      language: language ?? this.language,
      customHeaders: customHeaders ?? this.customHeaders,
      customFooters: customFooters ?? this.customFooters,
    );
  }
}

/// Data structure for table export
class TableData {
  final String name;
  final String title;
  final List<String> headers;
  final List<List<String>> rows;
  final Map<String, String>? metadata;
  final List<int>? numericColumns;
  final List<int>? percentageColumns;
  final List<int>? currencyColumns;

  const TableData({
    required this.name,
    required this.title,
    required this.headers,
    required this.rows,
    this.metadata,
    this.numericColumns,
    this.percentageColumns,
    this.currencyColumns,
  });

  /// Create TableData from AmortizationRow list
  factory TableData.fromAmortizationRows(
    List<AmortizationRow> rows, {
    required String name,
    required String title,
    bool isDegressive = false,
  }) {
    if (rows.isEmpty) {
      return TableData(
        name: name,
        title: title,
        headers: [],
        rows: [],
      );
    }

    // Determine headers based on mode
    List<String> headers;
    List<int> numericColumns;
    List<int> percentageColumns;
    List<int> currencyColumns;

    if (isDegressive) {
      headers = [
        'Année',
        'Base d\'amortissement',
        'Taux dégressif',
        'Taux linéaire',
        'Annuités d\'amortissement',
        'Annuités cumulées',
        'Valeur nette comptable',
      ];
      numericColumns = [1, 4, 5, 6];
      percentageColumns = [2, 3];
      currencyColumns = [1, 4, 5, 6];
    } else {
      headers = [
        'Année',
        'Base d\'amortissement',
        'Taux',
        'Annuités',
        'Annuités cumulées',
        'VNC',
      ];
      numericColumns = [1, 3, 4, 5];
      percentageColumns = [2];
      currencyColumns = [1, 3, 4, 5];
    }

    // Convert rows to string data
    final tableRows = rows.map((row) {
      if (isDegressive) {
        return [
          row.yearLabel,
          row.baseAmount.toStringAsFixed(2),
          row.degressiveRate?.toStringAsFixed(2) ?? row.rate.toStringAsFixed(2),
          row.linearRate?.toStringAsFixed(2) ?? '',
          row.annuity.toStringAsFixed(2),
          row.cumulativeAnnuity.toStringAsFixed(2),
          row.netBookValue.toStringAsFixed(2),
        ];
      } else {
        return [
          row.yearLabel,
          row.baseAmount.toStringAsFixed(2),
          row.rate.toStringAsFixed(2),
          row.annuity.toStringAsFixed(2),
          row.cumulativeAnnuity.toStringAsFixed(2),
          row.netBookValue.toStringAsFixed(2),
        ];
      }
    }).toList();

    return TableData(
      name: name,
      title: title,
      headers: headers,
      rows: tableRows,
      numericColumns: numericColumns,
      percentageColumns: percentageColumns,
      currencyColumns: currencyColumns,
    );
  }

  /// Create TableData for derogatory amortization
  factory TableData.fromDerogatory(
    List<AmortizationRow> rows, {
    required String name,
    required String title,
  }) {
    if (rows.isEmpty) {
      return TableData(
        name: name,
        title: title,
        headers: [],
        rows: [],
      );
    }

    const headers = [
      'Année',
      'Amortissement comptable',
      'Amortissement fiscal',
      'Dotations',
      'Reprises',
    ];

    final tableRows = rows.map((row) {
      return [
        row.yearLabel,
        row.accountingAmortization?.toStringAsFixed(2) ?? '0.00',
        row.fiscalAmortization?.toStringAsFixed(2) ?? '0.00',
        row.derogationProvision != null && row.derogationProvision! > 0
            ? row.derogationProvision!.toStringAsFixed(2)
            : '-',
        row.derogationReprise != null && row.derogationReprise! > 0
            ? row.derogationReprise!.toStringAsFixed(2)
            : '-',
      ];
    }).toList();

    return TableData(
      name: name,
      title: title,
      headers: headers,
      rows: tableRows,
      numericColumns: [1, 2, 3, 4],
      currencyColumns: [1, 2, 3, 4],
    );
  }
}

/// Main export data structure for amortization
class AmortizationExportData {
  final String assetName;
  final double originalValue;
  final double residualValue;
  final int duration;
  final String mode;
  final double rate;
  final DateTime acquisitionDate;
  final List<AmortizationRow> amortizationRows;
  final List<AmortizationRow>? derogatoryRows;
  final ExportSettings settings;
  final Map<String, dynamic>? additionalMetadata;

  const AmortizationExportData({
    required this.assetName,
    required this.originalValue,
    required this.residualValue,
    required this.duration,
    required this.mode,
    required this.rate,
    required this.acquisitionDate,
    required this.amortizationRows,
    this.derogatoryRows,
    this.settings = const ExportSettings(),
    this.additionalMetadata,
  });

  /// Get main table data
  TableData get mainTableData => TableData.fromAmortizationRows(
        amortizationRows,
        name: 'amortization',
        title: 'Tableau d\'amortissement',
        isDegressive: mode.toLowerCase() == 'degressif',
      );

  /// Get derogatory table data if available
  TableData? get derogatoryTableData => derogatoryRows != null
      ? TableData.fromDerogatory(
          derogatoryRows!,
          name: 'derogatory',
          title: 'Amortissement dérogatoire',
        )
      : null;

  /// Get summary data
  Map<String, dynamic> get summaryData => {
        'Nom de l\'immobilisation': assetName,
        'Valeur d\'origine': originalValue,
        'Valeur résiduelle': residualValue,
        'Durée d\'amortissement': '$duration ans',
        'Mode d\'amortissement': mode,
        'Taux d\'amortissement': '$rate%',
        'Date d\'acquisition': '${acquisitionDate.day.toString().padLeft(2, '0')}/${acquisitionDate.month.toString().padLeft(2, '0')}/${acquisitionDate.year}',
        'Total amortissements': amortizationRows.isNotEmpty 
            ? amortizationRows.last.cumulativeAnnuity 
            : 0.0,
        'Valeur nette finale': amortizationRows.isNotEmpty 
            ? amortizationRows.last.netBookValue 
            : originalValue,
      };
}

/// Export data structure for comparison
class ComparisonExportData {
  final String title;
  final TableData primaryTable;
  final TableData comparisonTable;
  final String primaryTitle;
  final String comparisonTitle;
  final List<List<String>>? differenceData;
  final ExportSettings settings;

  const ComparisonExportData({
    required this.title,
    required this.primaryTable,
    required this.comparisonTable,
    required this.primaryTitle,
    required this.comparisonTitle,
    this.differenceData,
    this.settings = const ExportSettings(),
  });

  /// Calculate differences between tables
  List<List<String>> calculateDifferences() {
    if (primaryTable.rows.length != comparisonTable.rows.length ||
        primaryTable.headers.length != comparisonTable.headers.length) {
      return [];
    }

    final differences = <List<String>>[];
    
    for (int i = 0; i < primaryTable.rows.length; i++) {
      final diffRow = <String>[];
      final primaryRow = primaryTable.rows[i];
      final comparisonRow = comparisonTable.rows[i];
      
      for (int j = 0; j < primaryRow.length; j++) {
        if (j == 0) {
          // Year column - copy as is
          diffRow.add(primaryRow[j]);
        } else {
          // Numeric columns - calculate difference
          final primary = double.tryParse(primaryRow[j]) ?? 0.0;
          final comparison = double.tryParse(comparisonRow[j]) ?? 0.0;
          final diff = comparison - primary;
          diffRow.add(diff.toStringAsFixed(2));
        }
      }
      differences.add(diffRow);
    }
    
    return differences;
  }
}

/// Enhanced Excel export utilities
class ExcelExportUtils {
  static const String _defaultFontFamily = 'Calibri';
  static const int _defaultFontSize = 11;
  static const int _headerFontSize = 12;
  static const int _titleFontSize = 14;

  /// Main export function for amortization data
  static Future<File> exportAmortizationToExcel(AmortizationExportData data) async {
    try {
      // Comprehensive data validation
      _validateAmortizationExportData(data);
      
      final excel = Excel.createExcel();
      
      // Validate Excel creation
      if (excel.sheets.isEmpty) {
        throw ExportExceptionFactory.encodingError('excel_creation', 'Impossible de créer le classeur Excel');
      }
      
      // Remove default sheet safely
      try {
        excel.delete('Sheet1');
      } catch (e) {
        // Non-critical if default sheet doesn't exist
        debugPrint('Default sheet removal failed: $e');
      }
      
      // Create main amortization sheet with error handling
      final mainSheet = excel['Amortissement'];
      await _setupAmortizationSheet(mainSheet, data);
      
      // Create derogatory sheet if data exists
      if (data.derogatoryTableData != null) {
        final derogatorySheet = excel['Dérogatoire'];
        await _setupDerogatorySheet(derogatorySheet, data);
      }
      
      // Create summary sheet if enabled
      if (data.settings.includeSummary) {
        final summarySheet = excel['Résumé'];
        await _setupSummarySheet(summarySheet, data);
      }
      
      // Validate final workbook
      _validateExcelWorkbook(excel);
      
      // Generate filename and save
      final fileName = _generateFileName('amortissement', data.assetName);
      return await _saveExcelFile(excel, fileName);
      
    } on ExportException {
      // Re-throw export exceptions as-is
      rethrow;
    } catch (e) {
      throw ExportExceptionFactory.generic('export_amortization', 'Erreur lors de l\'export d\'amortissement: $e');
    }
  }

  /// Export comparison data to Excel
  static Future<File> exportComparisonToExcel(ComparisonExportData data) async {
    try {
      final excel = Excel.createExcel();
      excel.delete('Sheet1');
      
      // Create comparison sheet
      final comparisonSheet = excel['Comparaison'];
      await _setupComparisonSheet(comparisonSheet, data);
      
      // Create differences sheet
      final differencesSheet = excel['Différences'];
      await _setupDifferencesSheet(differencesSheet, data);
      
      final fileName = _generateFileName('comparaison', data.title);
      return await _saveExcelFile(excel, fileName);
      
    } catch (e) {
      throw Exception('Erreur lors de l\'export de comparaison: $e');
    }
  }

  /// Export multiple tables to separate sheets
  static Future<File> exportMultipleTablesExcel(
    List<TableData> tables, {
    ExportSettings settings = const ExportSettings(),
    String? fileName,
  }) async {
    try {
      final excel = Excel.createExcel();
      excel.delete('Sheet1');
      
      for (final table in tables) {
        final sheet = excel[table.name];
        await _setupTableSheet(sheet, table, settings);
      }
      
      final finalFileName = fileName ?? _generateFileName('export_multiple', 'tables');
      return await _saveExcelFile(excel, finalFileName);
      
    } catch (e) {
      throw Exception('Erreur lors de l\'export multiple: $e');
    }
  }

  /// Setup main amortization sheet
  static Future<void> _setupAmortizationSheet(
    Sheet sheet,
    AmortizationExportData data,
  ) async {
    int currentRow = 0;
    
    // Add title and metadata
    currentRow = await _addSheetHeader(
      sheet,
      data.mainTableData.title,
      data.summaryData,
      data.settings,
      currentRow,
    );
    
    // Add main table
    currentRow = await _addTable(
      sheet,
      data.mainTableData,
      data.settings,
      currentRow,
    );
    
    // Setup print area and freeze panes
    if (data.settings.freezePanes) {
      // Freeze panes at header row
      // Note: Excel package doesn't directly support freeze panes
      // This would need to be implemented with raw Excel XML manipulation
    }
  }

  /// Setup derogatory amortization sheet
  static Future<void> _setupDerogatorySheet(
    Sheet sheet,
    AmortizationExportData data,
  ) async {
    if (data.derogatoryTableData == null) return;
    
    int currentRow = 0;
    
    // Add title
    currentRow = await _addSheetHeader(
      sheet,
      data.derogatoryTableData!.title,
      data.summaryData,
      data.settings,
      currentRow,
    );
    
    // Add derogatory table
    currentRow = await _addTable(
      sheet,
      data.derogatoryTableData!,
      data.settings,
      currentRow,
    );
  }

  /// Setup summary sheet
  static Future<void> _setupSummarySheet(
    Sheet sheet,
    AmortizationExportData data,
  ) async {
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue('Résumé de l\'amortissement');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add summary data
    for (final entry in data.summaryData.entries) {
      // Label column
      final labelCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 0,
        rowIndex: currentRow,
      ));
      labelCell.value = TextCellValue(entry.key);
      labelCell.cellStyle = CellStyle(
        bold: true,
        fontFamily: _defaultFontFamily,
        fontSize: _defaultFontSize,
      );
      
      // Value column
      final valueCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 1,
        rowIndex: currentRow,
      ));
      
      if (entry.value is double) {
        valueCell.value = DoubleCellValue(entry.value as double);
        valueCell.cellStyle = CellStyle(
          fontFamily: _defaultFontFamily,
          fontSize: _defaultFontSize,
          horizontalAlign: HorizontalAlign.Right,
        );
      } else {
        valueCell.value = TextCellValue(entry.value.toString());
        valueCell.cellStyle = CellStyle(
          fontFamily: _defaultFontFamily,
          fontSize: _defaultFontSize,
        );
      }
      
      currentRow++;
    }
    
    // Add calculation formulas if enabled
    if (data.settings.includeFormulas) {
      currentRow += 2;
      await _addCalculationFormulas(sheet, data, currentRow);
    }
  }

  /// Setup comparison sheet
  static Future<void> _setupComparisonSheet(
    Sheet sheet,
    ComparisonExportData data,
  ) async {
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue(data.title);
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Calculate column positions
    final primaryStartCol = 0;
    final primaryEndCol = data.primaryTable.headers.length - 1;
    final comparisonStartCol = primaryEndCol + 2;
    
    // Add primary table title
    final primaryTitleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: primaryStartCol,
      rowIndex: currentRow,
    ));
    primaryTitleCell.value = TextCellValue(data.primaryTitle);
    primaryTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
    );
    
    // Add comparison table title
    final comparisonTitleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: comparisonStartCol,
      rowIndex: currentRow,
    ));
    comparisonTitleCell.value = TextCellValue(data.comparisonTitle);
    comparisonTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
    );
    currentRow += 2;
    
    // Add headers for both tables
    await _addComparisonHeaders(sheet, data, currentRow);
    currentRow++;
    
    // Add data rows
    await _addComparisonData(sheet, data, currentRow);
  }

  /// Setup differences sheet
  static Future<void> _setupDifferencesSheet(
    Sheet sheet,
    ComparisonExportData data,
  ) async {
    final differences = data.differenceData ?? data.calculateDifferences();
    if (differences.isEmpty) return;
    
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue('Différences (${data.comparisonTitle} - ${data.primaryTitle})');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add headers
    for (int col = 0; col < data.primaryTable.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: col,
        rowIndex: currentRow,
      ));
      headerCell.value = TextCellValue(data.primaryTable.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
    currentRow++;
    
    // Add difference data
    for (int row = 0; row < differences.length; row++) {
      for (int col = 0; col < differences[row].length; col++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: col,
          rowIndex: currentRow + row,
        ));
        
        final value = differences[row][col];
        if (col == 0) {
          // Year column
          cell.value = TextCellValue(value);
          cell.cellStyle = CellStyle(
            fontFamily: _defaultFontFamily,
            fontSize: _defaultFontSize,
          );
        } else {
          // Numeric columns
          final numValue = double.tryParse(value) ?? 0.0;
          cell.value = DoubleCellValue(numValue);
          
          // Color code positive/negative differences
          final cellStyle = CellStyle(
            fontFamily: _defaultFontFamily,
            fontSize: _defaultFontSize,
            horizontalAlign: HorizontalAlign.Right,
          );
          
          // Note: Font color styling may need to be handled differently based on Excel package version
          // if (numValue > 0) {
          //   cellStyle.fontColorHex = '#008000'; // Green
          // } else if (numValue < 0) {
          //   cellStyle.fontColorHex = '#FF0000'; // Red
          // }
          
          cell.cellStyle = cellStyle;
        }
      }
    }
  }

  /// Add sheet header with title and metadata
  static Future<int> _addSheetHeader(
    Sheet sheet,
    String title,
    Map<String, dynamic> metadata,
    ExportSettings settings,
    int startRow,
  ) async {
    int currentRow = startRow;
    
    // Add company name if provided
    if (settings.companyName != null) {
      final companyCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 0,
        rowIndex: currentRow,
      ));
      companyCell.value = TextCellValue(settings.companyName!);
      companyCell.cellStyle = CellStyle(
        bold: true,
        fontSize: _headerFontSize,
        fontFamily: _defaultFontFamily,
      );
      currentRow++;
    }
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue(title);
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add generation date
    final dateCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    final now = DateTime.now();
    dateCell.value = TextCellValue(
      'Généré le ${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year} à ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}'
    );
    dateCell.cellStyle = CellStyle(
      fontFamily: _defaultFontFamily,
      fontSize: _defaultFontSize - 1,
      // fontColor: '#666666', // Color styling may need different approach
    );
    currentRow += 2;
    
    return currentRow;
  }

  /// Add table to sheet
  static Future<int> _addTable(
    Sheet sheet,
    TableData tableData,
    ExportSettings settings,
    int startRow,
  ) async {
    int currentRow = startRow;
    
    // Add headers
    for (int col = 0; col < tableData.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: col,
        rowIndex: currentRow,
      ));
      headerCell.value = TextCellValue(tableData.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
    currentRow++;
    
    // Add data rows
    for (int row = 0; row < tableData.rows.length; row++) {
      final isEvenRow = row % 2 == 0;
      
      for (int col = 0; col < tableData.rows[row].length; col++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: col,
          rowIndex: currentRow + row,
        ));
        
        final value = tableData.rows[row][col];
        final cellStyle = _getDataCellStyle(
          isEvenRow: isEvenRow,
          useAlternatingColors: settings.useAlternatingRowColors,
          isNumeric: tableData.numericColumns?.contains(col) ?? false,
          isPercentage: tableData.percentageColumns?.contains(col) ?? false,
          isCurrency: tableData.currencyColumns?.contains(col) ?? false,
        );
        
        // Set cell value and format
        if (tableData.numericColumns?.contains(col) ?? false) {
          final numValue = double.tryParse(value) ?? 0.0;
          cell.value = DoubleCellValue(numValue);
        } else {
          cell.value = TextCellValue(value);
        }
        
        cell.cellStyle = cellStyle;
      }
    }
    
    // Set column widths
    await _setColumnWidths(sheet, tableData);
    
    return currentRow + tableData.rows.length + 1;
  }

  /// Setup table sheet for multiple tables export
  static Future<void> _setupTableSheet(
    Sheet sheet,
    TableData tableData,
    ExportSettings settings,
  ) async {
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue(tableData.title);
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add table
    await _addTable(sheet, tableData, settings, currentRow);
  }

  /// Add comparison headers
  static Future<void> _addComparisonHeaders(
    Sheet sheet,
    ComparisonExportData data,
    int row,
  ) async {
    // Primary table headers
    for (int col = 0; col < data.primaryTable.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: col,
        rowIndex: row,
      ));
      headerCell.value = TextCellValue(data.primaryTable.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
    
    // Comparison table headers
    final comparisonStartCol = data.primaryTable.headers.length + 2;
    for (int col = 0; col < data.comparisonTable.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: comparisonStartCol + col,
        rowIndex: row,
      ));
      headerCell.value = TextCellValue(data.comparisonTable.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
  }

  /// Add comparison data
  static Future<void> _addComparisonData(
    Sheet sheet,
    ComparisonExportData data,
    int startRow,
  ) async {
    final maxRows = [data.primaryTable.rows.length, data.comparisonTable.rows.length].reduce((a, b) => a > b ? a : b);
    
    for (int row = 0; row < maxRows; row++) {
      final isEvenRow = row % 2 == 0;
      
      // Primary table data
      if (row < data.primaryTable.rows.length) {
        for (int col = 0; col < data.primaryTable.rows[row].length; col++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: col,
            rowIndex: startRow + row,
          ));
          
          final value = data.primaryTable.rows[row][col];
          final cellStyle = _getDataCellStyle(
            isEvenRow: isEvenRow,
            useAlternatingColors: data.settings.useAlternatingRowColors,
            isNumeric: data.primaryTable.numericColumns?.contains(col) ?? false,
          );
          
          if (data.primaryTable.numericColumns?.contains(col) ?? false) {
            final numValue = double.tryParse(value) ?? 0.0;
            cell.value = DoubleCellValue(numValue);
          } else {
            cell.value = TextCellValue(value);
          }
          
          cell.cellStyle = cellStyle;
        }
      }
      
      // Comparison table data
      if (row < data.comparisonTable.rows.length) {
        final comparisonStartCol = data.primaryTable.headers.length + 2;
        for (int col = 0; col < data.comparisonTable.rows[row].length; col++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: comparisonStartCol + col,
            rowIndex: startRow + row,
          ));
          
          final value = data.comparisonTable.rows[row][col];
          final cellStyle = _getDataCellStyle(
            isEvenRow: isEvenRow,
            useAlternatingColors: data.settings.useAlternatingRowColors,
            isNumeric: data.comparisonTable.numericColumns?.contains(col) ?? false,
          );
          
          if (data.comparisonTable.numericColumns?.contains(col) ?? false) {
            final numValue = double.tryParse(value) ?? 0.0;
            cell.value = DoubleCellValue(numValue);
          } else {
            cell.value = TextCellValue(value);
          }
          
          cell.cellStyle = cellStyle;
        }
      }
    }
  }

  /// Add calculation formulas
  static Future<void> _addCalculationFormulas(
    Sheet sheet,
    AmortizationExportData data,
    int startRow,
  ) async {
    int currentRow = startRow;
    
    // Add formulas section title
    final formulasTitleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    formulasTitleCell.value = TextCellValue('Formules de vérification');
    formulasTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
    );
    currentRow += 2;
    
    // Add verification formulas
    final formulas = [
      'Total des annuités',
      'Valeur amortissable',
      'Taux moyen effectif',
    ];
    
    for (final formula in formulas) {
      final labelCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 0,
        rowIndex: currentRow,
      ));
      labelCell.value = TextCellValue(formula);
      labelCell.cellStyle = CellStyle(
        fontFamily: _defaultFontFamily,
        fontSize: _defaultFontSize,
      );
      
      // Add corresponding formula (simplified for this example)
      final formulaCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 1,
        rowIndex: currentRow,
      ));
      formulaCell.value = TextCellValue('=SUM(...)'); // Placeholder
      formulaCell.cellStyle = CellStyle(
        fontFamily: _defaultFontFamily,
        fontSize: _defaultFontSize,
        horizontalAlign: HorizontalAlign.Right,
      );
      
      currentRow++;
    }
  }

  /// Get header cell style
  static CellStyle _getHeaderCellStyle() {
    return CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
      verticalAlign: VerticalAlign.Center,
      // backgroundColorHex: '#4472C4', // Color styling may need different approach
      // fontColorHex: '#FFFFFF',
    );
  }

  /// Get data cell style
  static CellStyle _getDataCellStyle({
    required bool isEvenRow,
    required bool useAlternatingColors,
    required bool isNumeric,
    bool isPercentage = false,
    bool isCurrency = false,
  }) {
    // Note: Background color styling commented out due to package compatibility
    // String? backgroundColor;
    // if (useAlternatingColors && !isEvenRow) {
    //   backgroundColor = '#F2F2F2';
    // }

    return CellStyle(
      fontSize: _defaultFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: isNumeric ? HorizontalAlign.Right : HorizontalAlign.Left,
      verticalAlign: VerticalAlign.Center,
      // backgroundColorHex: backgroundColor, // Color styling may need different approach
    );
  }

  /// Set column widths based on content
  static Future<void> _setColumnWidths(Sheet sheet, TableData tableData) async {
    for (int col = 0; col < tableData.headers.length; col++) {
      double width = 15.0; // Default width
      
      // Adjust width based on header length
      final headerLength = tableData.headers[col].length;
      if (headerLength > 15) {
        width = 20.0;
      } else if (headerLength > 10) {
        width = 18.0;
      }
      
      // Adjust for numeric columns
      if (tableData.numericColumns?.contains(col) ?? false) {
        width = 16.0;
      }
      
      sheet.setColumnWidth(col, width);
    }
  }

  /// Generate unique filename with timestamp
  static String _generateFileName(String prefix, String? suffix) {
    final now = DateTime.now();
    final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
    
    String fileName = '${prefix}_$timestamp';
    if (suffix != null && suffix.isNotEmpty) {
      // Clean suffix for filename
      final cleanSuffix = suffix.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
      fileName += '_$cleanSuffix';
    }
    
    return '$fileName.xlsx';
  }

  /// Save Excel file to documents directory
  static Future<File> _saveExcelFile(Excel excel, String fileName) async {
    try {
      // Platform-specific storage handling
      Directory directory;
      
      if (kIsWeb) {
        throw ExportExceptionFactory.platformError('web_storage', 'Export direct non supporté sur web - utilisez le téléchargement browser');
      }
      
      // Validate Excel workbook before encoding
      _validateExcelWorkbook(excel);
      
      // Get platform-appropriate directory
      directory = await _getPlatformDirectory();
      
      // Ensure directory exists and is writable
      await _ensureDirectoryWritable(directory);
      
      // Sanitize filename for file system compatibility
      final sanitizedFileName = _sanitizeFileName(fileName);
      final file = File('${directory.path}/$sanitizedFileName');
      
      // Check available disk space (rough estimate)
      await _checkDiskSpace(directory, estimatedSize: 5 * 1024 * 1024); // 5MB estimate
      
      // Excel generation with validation
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw ExportExceptionFactory.encodingError('excel_encode', 'Échec de l\'encodage Excel - données potentiellement corrompues');
      }
      
      // Validate encoded data
      if (excelBytes.isEmpty) {
        throw ExportExceptionFactory.encodingError('excel_empty', 'Fichier Excel généré vide');
      }
      
      // Write file with retry logic
      await _writeFileWithRetry(file, excelBytes);
      
      // Validate written file
      if (!await file.exists()) {
        throw ExportExceptionFactory.fileSystemError('file_not_created', 'Le fichier n\'a pas été créé correctement', path: file.path);
      }
      
      final fileSize = await file.length();
      if (fileSize == 0) {
        throw ExportExceptionFactory.fileSystemError('empty_file', 'Le fichier créé est vide', path: file.path);
      }
      
      return file;
    } on ExportException {
      // Re-throw export exceptions as-is
      rethrow;
    } on FileSystemException catch (e) {
      throw ExportExceptionFactory.fileSystemError('file_system_error', e.message, path: e.path);
    } on PathAccessException catch (e) {
      throw ExportExceptionFactory.permissionError('path_access', 'Accès refusé au répertoire: ${e.path}', ['Vérifiez les permissions d\'écriture']);
    } catch (e) {
      throw ExportExceptionFactory.generic('save_excel_file', 'Erreur inattendue lors de la sauvegarde: $e');
    }
  }

  /// Utility method to convert Map&lt;String, String&gt; tables to TableData
  static TableData convertLegacyTable(
    List<Map<String, String>> legacyTable,
    String name,
    String title,
  ) {
    if (legacyTable.isEmpty) {
      return TableData(
        name: name,
        title: title,
        headers: [],
        rows: [],
      );
    }
    
    final headers = legacyTable.first.keys.toList();
    final rows = legacyTable.map((row) => headers.map((header) => row[header] ?? '').toList()).toList();
    
    // Detect numeric columns
    final numericColumns = <int>[];
    final percentageColumns = <int>[];
    final currencyColumns = <int>[];
    
    for (int col = 0; col < headers.length; col++) {
      final header = headers[col].toLowerCase();
      if (header.contains('taux') || header.contains('%')) {
        percentageColumns.add(col);
        numericColumns.add(col);
      } else if (header.contains('annuité') || header.contains('base') || header.contains('vnc') || header.contains('valeur')) {
        currencyColumns.add(col);
        numericColumns.add(col);
      }
    }
    
    return TableData(
      name: name,
      title: title,
      headers: headers,
      rows: rows,
      numericColumns: numericColumns,
      percentageColumns: percentageColumns,
      currencyColumns: currencyColumns,
    );
  }

  /// Export legacy table format (for backward compatibility)
  static Future<File> exportLegacyTable(
    List<Map<String, String>> table,
    String type, {
    ExportSettings settings = const ExportSettings(),
  }) async {
    final tableData = convertLegacyTable(table, type, 'Tableau $type');
    return await exportMultipleTablesExcel([tableData], settings: settings);
  }

  // Helper methods for robust file system handling
  
  /// Validate Excel workbook before encoding
  static void _validateExcelWorkbook(Excel excel) {
    if (excel.sheets.isEmpty) {
      throw ExportExceptionFactory.validationError('empty_workbook', 'Le classeur Excel est vide', ['Ajoutez au moins une feuille avec des données']);
    }
    
    // Check if at least one sheet has content
    bool hasContent = false;
    for (final sheetName in excel.sheets.keys) {
      final sheet = excel.sheets[sheetName];
      if (sheet != null && sheet.rows.isNotEmpty) {
        hasContent = true;
        break;
      }
    }
    
    if (!hasContent) {
      throw ExportExceptionFactory.validationError('no_content', 'Aucune donnée trouvée dans le classeur', ['Vérifiez que les données ont été ajoutées correctement']);
    }
  }
  
  /// Get platform-appropriate directory for file storage
  static Future<Directory> _getPlatformDirectory() async {
    try {
      if (Platform.isAndroid) {
        // Try external storage first, fall back to app documents
        try {
          final directory = await getExternalStorageDirectory();
          if (directory != null && await directory.exists()) {
            return Directory('${directory.path}/Documents');
          }
        } catch (e) {
          debugPrint('External storage access failed: $e');
        }
        // Fallback to app documents directory
        return await getApplicationDocumentsDirectory();
      } else if (Platform.isIOS) {
        return await getApplicationDocumentsDirectory();
      } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        // Desktop platforms - use Downloads directory if available
        try {
          final downloadsDirectory = await getDownloadsDirectory();
          if (downloadsDirectory != null && await downloadsDirectory.exists()) {
            return downloadsDirectory;
          }
        } catch (e) {
          debugPrint('Downloads directory access failed: $e');
        }
        // Fallback to documents directory
        return await getApplicationDocumentsDirectory();
      } else {
        throw ExportExceptionFactory.platformError('unsupported_platform', 'Plateforme non supportée: ${Platform.operatingSystem}');
      }
    } on ExportException {
      rethrow;
    } catch (e) {
      throw ExportExceptionFactory.fileSystemError('directory_access', 'Impossible d\'accéder au répertoire de destination: $e');
    }
  }
  
  /// Ensure directory exists and is writable
  static Future<void> _ensureDirectoryWritable(Directory directory) async {
    try {
      // Create directory if it doesn't exist
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      // Test write permissions by creating a temporary file
      final testFileName = '.test_write_${DateTime.now().millisecondsSinceEpoch}.tmp';
      final testFile = File('${directory.path}/$testFileName');
      
      try {
        await testFile.writeAsString('test');
        await testFile.delete();
      } catch (e) {
        throw ExportExceptionFactory.permissionError(
          'write_permission', 
          'Pas de permission d\'écriture dans le répertoire: ${directory.path}',
          ['Vérifiez les permissions du répertoire', 'Essayez un autre emplacement']
        );
      }
      
    } on ExportException {
      rethrow;
    } catch (e) {
      throw ExportExceptionFactory.fileSystemError(
        'directory_setup', 
        'Impossible de configurer le répertoire: $e',
        path: directory.path
      );
    }
  }
  
  /// Sanitize filename for file system compatibility
  static String _sanitizeFileName(String fileName) {
    // Remove or replace invalid characters for file systems
    String sanitized = fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_') // Windows invalid chars
        .replaceAll(RegExp(r'[\x00-\x1F]'), '_') // Control characters
        .replaceAll(RegExp(r'\s+'), '_') // Multiple spaces
        .replaceAll(RegExp(r'_+'), '_'); // Multiple underscores
    
    // Remove leading/trailing dots and spaces (Windows issues)
    sanitized = sanitized.replaceAll(RegExp(r'^[\.\s]+|[\.\s]+$'), '');
    
    // Ensure filename is not empty and not too long
    if (sanitized.isEmpty) {
      sanitized = 'export_${DateTime.now().millisecondsSinceEpoch}';
    }
    
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100);
    }
    
    // Ensure .xlsx extension
    if (!sanitized.toLowerCase().endsWith('.xlsx')) {
      if (sanitized.toLowerCase().endsWith('.xls')) {
        sanitized = sanitized.substring(0, sanitized.length - 4) + '.xlsx';
      } else {
        sanitized += '.xlsx';
      }
    }
    
    return sanitized;
  }
  
  /// Check available disk space (rough estimate)
  static Future<void> _checkDiskSpace(Directory directory, {required int estimatedSize}) async {
    try {
      // This is a simplified check - actual disk space checking varies by platform
      // Create a test file to check if we can write the estimated size
      final testFile = File('${directory.path}/.disk_space_test_${DateTime.now().millisecondsSinceEpoch}.tmp');
      
      try {
        // Try to create a small test file
        final testData = List<int>.filled(1024, 0); // 1KB test
        await testFile.writeAsBytes(testData);
        await testFile.delete();
        
        // If we can't write a small file, we definitely can't write the export
        // This is a basic check - more sophisticated space checking would require platform-specific code
      } catch (e) {
        throw ExportExceptionFactory.diskSpaceError(
          'insufficient_space',
          'Espace disque insuffisant ou inaccessible',
          ['Libérez de l\'espace disque', 'Choisissez un autre emplacement']
        );
      }
    } catch (e) {
      if (e is ExportException) {
        rethrow;
      }
      // Non-critical - continue if disk space check fails
      debugPrint('Disk space check failed: $e');
    }
  }
  
  /// Write file with retry logic
  static Future<void> _writeFileWithRetry(File file, List<int> bytes, {int maxRetries = 3}) async {
    int attempts = 0;
    Exception? lastException;
    
    while (attempts < maxRetries) {
      try {
        await file.writeAsBytes(bytes);
        return; // Success
      } on FileSystemException catch (e) {
        lastException = e;
        attempts++;
        
        if (attempts >= maxRetries) {
          throw ExportExceptionFactory.fileSystemError(
            'write_failed',
            'Échec d\'écriture après $maxRetries tentatives: ${e.message}',
            path: e.path,
          );
        }
        
        // Brief delay before retry
        await Future.delayed(Duration(milliseconds: 100 * attempts));
      } catch (e) {
        throw ExportExceptionFactory.generic(
          'write_error',
          'Erreur inattendue lors de l\'écriture: $e'
        );
      }
    }
    
    // This should never be reached due to the throw above
    throw ExportExceptionFactory.generic(
      'write_retry_exhausted',
      'Toutes les tentatives d\'écriture ont échoué. Dernière erreur: $lastException'
    );
  }
  
  /// Validate amortization export data comprehensively
  static void _validateAmortizationExportData(AmortizationExportData data) {
    final errors = <String>[];
    
    // Basic field validation
    if (data.assetName.trim().isEmpty) {
      errors.add('Le nom de l\'immobilisation ne peut pas être vide');
    }
    
    if (data.originalValue <= 0) {
      errors.add('La valeur d\'origine doit être positive');
    }
    
    if (data.residualValue < 0) {
      errors.add('La valeur résiduelle ne peut pas être négative');
    }
    
    if (data.residualValue >= data.originalValue) {
      errors.add('La valeur résiduelle doit être inférieure à la valeur d\'origine');
    }
    
    if (data.duration <= 0) {
      errors.add('La durée d\'amortissement doit être positive');
    }
    
    if (data.rate <= 0 || data.rate > 100) {
      errors.add('Le taux d\'amortissement doit être entre 0 et 100%');
    }
    
    if (data.acquisitionDate.isAfter(DateTime.now().add(const Duration(days: 365)))) {
      errors.add('La date d\'acquisition ne peut pas être dans le futur lointain');
    }
    
    // Amortization data validation
    if (data.amortizationRows.isEmpty) {
      errors.add('Les données d\'amortissement sont requises');
    } else {
      // Validate amortization consistency
      for (int i = 0; i < data.amortizationRows.length; i++) {
        final row = data.amortizationRows[i];
        
        if (row.annuity < 0) {
          errors.add('L\'annuité de l\'année ${row.year} ne peut pas être négative');
        }
        
        if (row.cumulativeAnnuity < 0) {
          errors.add('Le cumul d\'amortissement de l\'année ${row.year} ne peut pas être négatif');
        }
        
        if (row.netBookValue < 0) {
          errors.add('La VNC de l\'année ${row.year} ne peut pas être négative');
        }
        
        // Check cumulative consistency
        if (i > 0) {
          final previousRow = data.amortizationRows[i - 1];
          if (row.cumulativeAnnuity < previousRow.cumulativeAnnuity) {
            errors.add('Le cumul d\'amortissement ne peut pas diminuer (année ${row.year})');
          }
        }
      }
      
      // Check final values consistency
      final lastRow = data.amortizationRows.last;
      final totalAmortization = data.originalValue - data.residualValue;
      const tolerance = 0.01; // 1 centime tolerance
      
      if ((lastRow.cumulativeAnnuity - totalAmortization).abs() > tolerance) {
        errors.add('Le total d\'amortissement ne correspond pas à la valeur amortissable');
      }
    }
    
    // Derogatory data validation if present
    if (data.derogatoryRows != null && data.derogatoryRows!.isNotEmpty) {
      if (data.derogatoryRows!.length != data.amortizationRows.length) {
        errors.add('Les données dérogatoires doivent avoir le même nombre d\'années');
      }
      
      for (final row in data.derogatoryRows!) {
        if ((row.derogationProvision ?? 0) < 0) {
          errors.add('Les provisions dérogatoires ne peuvent pas être négatives (année ${row.year})');
        }
        
        if ((row.derogationReprise ?? 0) < 0) {
          errors.add('Les reprises dérogatoires ne peuvent pas être négatives (année ${row.year})');
        }
      }
    }
    
    if (errors.isNotEmpty) {
      throw ExportExceptionFactory.validationError(
        'invalid_amortization_data',
        'Données d\'amortissement invalides',
        errors,
      );
    }
  }
}