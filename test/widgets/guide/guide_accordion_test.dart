import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:moroccanaccounting/widgets/guide/guide_accordion_view.dart';
import 'package:moroccanaccounting/widgets/guide/guide_scaffold.dart';
import 'package:moroccanaccounting/widgets/guide/breadcrumb_navigation.dart';
import 'package:moroccanaccounting/models/guide/guide_section_data.dart';

void main() {
  group('GuideAccordionView Tests', () {
    late List<GuideSectionData> testSections;

    setUpAll(() async {
      // Initialize Hive for testing
      await Hive.initFlutter();
      // Skip adapter registration as it's handled elsewhere
    });

    setUp(() {
      testSections = [
        const GuideSectionData(
          id: 'section1',
          title: 'Introduction',
          content: 'This is the introduction section with basic concepts.',
          items: ['Concept 1', 'Concept 2', 'Concept 3'],
          type: GuideSectionType.text,
          estimatedReadTime: 5,
        ),
        const GuideSectionData(
          id: 'section2',
          title: 'Advanced Topics',
          content: 'This section covers advanced topics.',
          items: ['Advanced Topic 1', 'Advanced Topic 2'],
          type: GuideSectionType.list,
          estimatedReadTime: 10,
        ),
        const GuideSectionData(
          id: 'section3',
          title: 'Calculator',
          content: 'Interactive calculator section.',
          type: GuideSectionType.calculator,
          estimatedReadTime: 3,
        ),
      ];
    });

    testWidgets('displays all sections with correct titles', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GuideAccordionView(
                sections: testSections,
                guideId: 'test_guide',
              ),
            ),
          ),
        ),
      );

      // Verify all section titles are displayed
      expect(find.text('Introduction'), findsOneWidget);
      expect(find.text('Advanced Topics'), findsOneWidget);
      expect(find.text('Calculator'), findsOneWidget);
    });

    testWidgets('shows estimated read time for each section', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GuideAccordionView(
                sections: testSections,
                guideId: 'test_guide',
              ),
            ),
          ),
        ),
      );

      // Verify estimated read times are displayed
      expect(find.text('5 min'), findsOneWidget);
      expect(find.text('10 min'), findsOneWidget);
      expect(find.text('3 min'), findsOneWidget);
    });

    testWidgets('expands section when tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GuideAccordionView(
                sections: testSections,
                guideId: 'test_guide',
                allowMultipleExpanded: true,
              ),
            ),
          ),
        ),
      );

      // Initially, section content should not be visible
      expect(find.text('This is the introduction section with basic concepts.'), findsNothing);

      // Tap on the first section to expand it
      await tester.tap(find.text('Introduction'));
      await tester.pumpAndSettle();

      // Now the section content should be visible
      expect(find.text('This is the introduction section with basic concepts.'), findsOneWidget);
      expect(find.text('Concept 1'), findsOneWidget);
      expect(find.text('Concept 2'), findsOneWidget);
      expect(find.text('Concept 3'), findsOneWidget);
    });

    testWidgets('shows progress indicators for each section', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GuideAccordionView(
                sections: testSections,
                guideId: 'test_guide',
              ),
            ),
          ),
        ),
      );

      // Verify progress indicators are present
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(3));
    });

    testWidgets('calls onSectionCompleted when section is marked complete', (WidgetTester tester) async {
      String? completedSectionId;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GuideAccordionView(
                sections: testSections,
                guideId: 'test_guide',
                allowMultipleExpanded: true,
                onSectionCompleted: (sectionId) {
                  completedSectionId = sectionId;
                },
              ),
            ),
          ),
        ),
      );

      // Expand the first section
      await tester.tap(find.text('Introduction'));
      await tester.pumpAndSettle();

      // Find and tap the "Mark as completed" button
      await tester.tap(find.text('Marquer comme terminé'));
      await tester.pumpAndSettle();

      // Verify the callback was called with the correct section ID
      expect(completedSectionId, equals('section1'));
    });

    testWidgets('displays different icons for different section types', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GuideAccordionView(
                sections: testSections,
                guideId: 'test_guide',
              ),
            ),
          ),
        ),
      );

      // Verify different icons are used for different section types
      expect(find.byIcon(Icons.article), findsOneWidget); // text type
      expect(find.byIcon(Icons.list), findsOneWidget); // list type
      expect(find.byIcon(Icons.calculate), findsOneWidget); // calculator type
    });

    testWidgets('handles empty sections list gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GuideAccordionView(
                sections: [],
                guideId: 'test_guide',
              ),
            ),
          ),
        ),
      );

      // Should display empty state
      expect(find.text('Aucune section disponible'), findsOneWidget);
      expect(find.byIcon(Icons.menu_book_outlined), findsOneWidget);
    });
  });

  group('GuideScaffold Tests', () {
    testWidgets('displays title and breadcrumbs correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: GuideScaffold(
              guideId: 'test_guide',
              title: 'Test Guide',
              breadcrumbs: const [
                BreadcrumbItem(title: 'Home'),
                BreadcrumbItem(title: 'Guides'),
                BreadcrumbItem(title: 'Test Guide'),
              ],
            ),
          ),
        ),
      );

      // Verify title is displayed
      expect(find.text('Test Guide'), findsOneWidget);

      // Verify breadcrumbs are displayed
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Guides'), findsOneWidget);
    });

    testWidgets('shows search icon when search is enabled', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: GuideScaffold(
              guideId: 'test_guide',
              title: 'Test Guide',
              breadcrumbs: const [],
              showSearch: true,
            ),
          ),
        ),
      );

      // Verify search icon is present
      expect(find.byIcon(Icons.search), findsOneWidget);
    });

    testWidgets('hides search icon when search is disabled', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: GuideScaffold(
              guideId: 'test_guide',
              title: 'Test Guide',
              breadcrumbs: const [],
              showSearch: false,
            ),
          ),
        ),
      );

      // Verify search icon is not present
      expect(find.byIcon(Icons.search), findsNothing);
    });
  });
}
