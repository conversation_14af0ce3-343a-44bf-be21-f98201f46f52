import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/models/immobilisations/amortization_row.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_approfondie/regles_evaluation/sections/calculateur_section.dart';

void main() {
  group('Simple Calculator Widget Tests', () {
    late CalculateurSection widget;

    setUp(() {
      widget = const CalculateurSection();
    });

    group('Prorata Temporis Validation Tests', () {
      testWidgets('should display prorata temporis calculation correctly', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values for mid-year acquisition
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '100000');
        await tester.enterText(durationField, '5');
        await tester.enterText(rateField, '20');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results table
        expect(find.byType(DataTable), findsOneWidget);

        // Verify that calculation was performed
        expect(find.text('Année'), findsOneWidget);
        expect(find.text('Annuité'), findsOneWidget);
      });

      testWidgets('should handle different acquisition dates', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '60000');
        await tester.enterText(durationField, '3');
        await tester.enterText(rateField, '33.33');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results table
        expect(find.byType(DataTable), findsOneWidget);
      });

      testWidgets('should handle year-end acquisition dates', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values for December acquisition
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '80000');
        await tester.enterText(durationField, '4');
        await tester.enterText(rateField, '25');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results table
        expect(find.byType(DataTable), findsOneWidget);
      });
    });

    group('UI Integration Tests', () {
      testWidgets('should display form fields correctly', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Verify form fields are present
        expect(find.byType(TextFormField), findsAtLeast(3));
        expect(find.text('Valeur d\'acquisition (DH)'), findsOneWidget);
        expect(find.text('Durée d\'amortissement (années)'), findsOneWidget);
        expect(find.text('Taux d\'amortissement (%)'), findsOneWidget);
      });

      testWidgets('should validate required fields', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Try to calculate without entering values
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should show validation errors
        expect(find.text('Ce champ est requis'), findsAtLeast(1));
      });

      testWidgets('should update calculation when values change', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter valid values
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        
        await tester.enterText(valueField, '50000');
        await tester.enterText(durationField, '5');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results
        expect(find.byType(DataTable), findsOneWidget);
      });
    });

    group('Export Functionality Tests', () {
      testWidgets('should handle export with valid data', (tester) async {
        await tester.pumpWidget(MaterialApp(home: Scaffold(body: widget)));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Set up calculation data
        calculatorState._amortizationTable = [
          AmortizationRow(
            year: 2023,
            yearLabel: '2023',
            baseAmount: 50000.0,
            rate: 25.0,
            annuity: 12500.0,
            cumulativeAnnuity: 12500.0,
            netBookValue: 37500.0,
          ),
        ];

        await tester.pumpAndSettle();

        // Verify data is set
        expect(calculatorState._amortizationTable.isNotEmpty, isTrue);
        expect(calculatorState._amortizationTable.first.annuity, equals(12500.0));
      });

      testWidgets('should handle export errors gracefully', (tester) async {
        await tester.pumpWidget(MaterialApp(home: Scaffold(body: widget)));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Try to export with empty data
        calculatorState._amortizationTable = [];

        // This should not crash the widget
        await tester.pumpAndSettle();

        // Verify the table is empty
        expect(calculatorState._amortizationTable.isEmpty, isTrue);
      });
    });

    group('Edge Cases and Validation', () {
      testWidgets('should handle zero rate correctly', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        final acquisitionDate = DateTime(2023, 1, 1);
        final testTable = calculatorState._generateAmortizationTable(
          value: 100000.0,
          duration: 5,
          rate: 0.0, // Zero rate
          method: 'lineaire',
          acquisitionDate: acquisitionDate,
        );

        expect(testTable.length, equals(5));

        // All annuities should be zero
        for (final row in testTable) {
          expect(row.annuity, equals(0.0));
        }

        // Net book value should remain at original value
        for (final row in testTable) {
          expect(row.netBookValue, equals(100000.0));
        }
      });

      testWidgets('should handle single year duration', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        final acquisitionDate = DateTime(2023, 6, 1);
        final testTable = calculatorState._generateAmortizationTable(
          value: 24000.0,
          duration: 1,
          rate: 100.0, // 100% for single year
          method: 'lineaire',
          acquisitionDate: acquisitionDate,
        );

        expect(testTable.length, equals(1));

        final singleRow = testTable.first;
        expect(singleRow.annuity, closeTo(24000.0, 0.01));
        expect(singleRow.netBookValue, closeTo(0.0, 0.01));
        expect(singleRow.cumulativeAnnuity, closeTo(24000.0, 0.01));
      });
    });
  });
}
