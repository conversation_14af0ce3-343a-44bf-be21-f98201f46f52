import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_approfondie/regles_evaluation/sections/calculateur_section.dart';

void main() {
  group('Simple Calculator Widget Tests', () {
    late CalculateurSection widget;

    setUp(() {
      widget = const CalculateurSection();
    });

    group('Prorata Temporis Validation Tests', () {
      testWidgets('should display prorata temporis calculation correctly', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values for mid-year acquisition
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '100000');
        await tester.enterText(durationField, '5');
        await tester.enterText(rateField, '20');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results table
        expect(find.byType(DataTable), findsOneWidget);

        // Verify that calculation was performed
        expect(find.text('Année'), findsOneWidget);
        expect(find.text('Annuité'), findsOneWidget);
      });

      testWidgets('should handle different acquisition dates', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '60000');
        await tester.enterText(durationField, '3');
        await tester.enterText(rateField, '33.33');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results table
        expect(find.byType(DataTable), findsOneWidget);
      });

      testWidgets('should handle year-end acquisition dates', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values for December acquisition
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '80000');
        await tester.enterText(durationField, '4');
        await tester.enterText(rateField, '25');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results table
        expect(find.byType(DataTable), findsOneWidget);
      });
    });

    group('UI Integration Tests', () {
      testWidgets('should display form fields correctly', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Verify form fields are present
        expect(find.byType(TextFormField), findsAtLeast(3));
        expect(find.text('Valeur d\'acquisition (DH)'), findsOneWidget);
        expect(find.text('Durée d\'amortissement (années)'), findsOneWidget);
        expect(find.text('Taux d\'amortissement (%)'), findsOneWidget);
      });

      testWidgets('should validate required fields', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Try to calculate without entering values
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should show validation errors
        expect(find.text('Ce champ est requis'), findsAtLeast(1));
      });

      testWidgets('should update calculation when values change', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter valid values
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        
        await tester.enterText(valueField, '50000');
        await tester.enterText(durationField, '5');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results
        expect(find.byType(DataTable), findsOneWidget);
      });
    });

    group('Export Functionality Tests', () {
      testWidgets('should handle export with valid calculation data', (tester) async {
        await tester.pumpWidget(MaterialApp(home: Scaffold(body: widget)));

        // Enter test values
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '50000');
        await tester.enterText(durationField, '4');
        await tester.enterText(rateField, '25');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should display results table with data
        expect(find.byType(DataTable), findsOneWidget);
        expect(find.text('Année'), findsOneWidget);
      });

      testWidgets('should handle empty calculation state', (tester) async {
        await tester.pumpWidget(MaterialApp(home: Scaffold(body: widget)));

        // Without entering any data or calculating
        await tester.pumpAndSettle();

        // Should not crash and should show form
        expect(find.byType(TextFormField), findsAtLeast(3));
        expect(find.byType(FilledButton), findsOneWidget);
      });
    });

    group('Edge Cases and Validation', () {
      testWidgets('should handle zero rate input', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values with zero rate
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '100000');
        await tester.enterText(durationField, '5');
        await tester.enterText(rateField, '0');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should handle zero rate without crashing
        expect(find.byType(DataTable), findsOneWidget);
      });

      testWidgets('should handle single year duration', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Enter test values for single year
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        await tester.enterText(valueField, '24000');
        await tester.enterText(durationField, '1');
        await tester.enterText(rateField, '100');
        await tester.pumpAndSettle();

        // Trigger calculation
        final calculateButton = find.byType(FilledButton);
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();

        // Should handle single year duration
        expect(find.byType(DataTable), findsOneWidget);
      });
    });
  });
}
