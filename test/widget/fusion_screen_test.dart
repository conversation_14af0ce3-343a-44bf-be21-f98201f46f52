import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_societes/fusion/fusion_screen.dart';
import 'package:moroccanaccounting/services/theme_service.dart';
import 'package:provider/provider.dart';

void main() {
  testWidgets('FusionScreen renders correctly with initial theme', (WidgetTester tester) async {
    final themeService = ThemeService();
    await themeService.init();

    await tester.pumpWidget(
      ChangeNotifierProvider.value(
        value: themeService,
        child: MaterialApp(
          theme: themeService.theme,
          home: const FusionScreen(),
        ),
      ),
    );

    // Verify initial theme colors
    final initialTheme = Theme.of(tester.element(find.byType(FusionScreen)));
    expect(initialTheme.colorScheme.primary, isNotNull);
    expect(initialTheme.colorScheme.surface, isNotNull);

    // Verify tab bar styling
    final tabBar = find.byType(TabBar);
    expect(tabBar, findsOneWidget);
    final TabBar tabBarWidget = tester.widget(tabBar);
    expect(tabBarWidget.isScrollable, isTrue);
    expect(tabBarWidget.indicatorSize, TabBarIndicatorSize.label);
    expect(tabBarWidget.labelColor, initialTheme.colorScheme.primary);
    expect(tabBarWidget.unselectedLabelColor, initialTheme.colorScheme.onSurfaceVariant);

    // Verify app bar styling
    final appBar = find.byType(SliverAppBar);
    expect(appBar, findsOneWidget);
    final SliverAppBar appBarWidget = tester.widget(appBar);
    expect(appBarWidget.surfaceTintColor, Colors.transparent);
    expect(appBarWidget.shadowColor?.a, lessThan(0.2));

    // Verify tab labels are present
    expect(find.text('Introduction'), findsOneWidget);
    expect(find.text('Fusion Création'), findsOneWidget);
    expect(find.text('Fusion Absorption'), findsOneWidget);
    expect(find.text('Comptabilisation'), findsOneWidget);
    expect(find.text('Régularisation TVA'), findsOneWidget);
  });

  testWidgets('FusionScreen handles dark theme correctly', (WidgetTester tester) async {
    final themeService = ThemeService();
    await themeService.init();
    themeService.setTheme(ThemeType.dark);

    await tester.pumpWidget(
      ChangeNotifierProvider.value(
        value: themeService,
        child: MaterialApp(
          theme: themeService.theme,
          home: const FusionScreen(),
        ),
      ),
    );

    final darkTheme = Theme.of(tester.element(find.byType(FusionScreen)));
    expect(darkTheme.colorScheme.primary, isNotNull);
    expect(darkTheme.colorScheme.surface, isNotNull);
    expect(darkTheme.brightness, equals(Brightness.dark));

    // Verify dark theme specific colors
    expect(darkTheme.colorScheme.primary, const Color(0xFF60A5FA));
    expect(darkTheme.colorScheme.surface, const Color(0xFF1A1B26));
    expect(darkTheme.colorScheme.onSurfaceVariant, const Color(0xFFD1D5DB));

    // Verify dark theme specific styling
    final appBar = find.byType(SliverAppBar);
    expect(appBar, findsOneWidget);
    final SliverAppBar appBarWidget = tester.widget(appBar);
    expect(appBarWidget.elevation, 0.0);
    expect(appBarWidget.backgroundColor?.a, lessThan(1.0));

    // Verify card styling in dark mode
    final cards = find.byType(Card);
    for (final card in cards.evaluate()) {
      final cardWidget = card.widget as Card;
      expect(cardWidget.elevation, 0.0);
      expect(cardWidget.surfaceTintColor, Colors.transparent);
      expect(cardWidget.color?.a, lessThan(1.0));
    }
  });
}
