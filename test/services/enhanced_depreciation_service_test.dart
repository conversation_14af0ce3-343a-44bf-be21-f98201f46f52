import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/services/enhanced_depreciation_service.dart';
import 'package:moroccanaccounting/models/calculators/enhanced_depreciation_data.dart';

void main() {
  group('EnhancedDepreciationService', () {
    late EnhancedDepreciationService service;

    setUp(() {
      service = EnhancedDepreciationService();
    });

    group('Prorata Temporis Bug Fix Tests', () {
      test('should calculate correct months in last year for mid-year acquisition', () {
        // Test case: Asset acquired in March (month 3)
        // Expected: March to December = 10 months in first year
        // Last year should have: January to February = 2 months (month - 1)
        final acquisitionDate = DateTime(2023, 3, 15); // March 15th
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 100000.0,
          residualValue: 0.0,
          usefulLifeYears: 5,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        // Verify that we have the correct number of rows
        expect(result.amortizationTable.length, equals(5));

        // Check first year prorata temporis (March to December = 10 months)
        final firstYear = result.amortizationTable.first;
        const expectedFirstYearAnnuity = 100000.0 * (1.0 / 5) * (10 / 12);
        expect(firstYear.annuity, closeTo(expectedFirstYearAnnuity, 0.01));
        expect(firstYear.yearLabel, contains('(10 mois)'));

        // Check last year prorata temporis (January to February = 2 months) 
        final lastYear = result.amortizationTable.last;
        const expectedLastYearAnnuity = 100000.0 * (1.0 / 5) * (2 / 12);
        expect(lastYear.annuity, closeTo(expectedLastYearAnnuity, 0.01));
        expect(lastYear.yearLabel, contains('(2 mois)'));

        // Verify total amortization equals original value
        final totalAmortization = result.amortizationTable
            .fold<double>(0.0, (sum, row) => sum + row.annuity);
        expect(totalAmortization, closeTo(100000.0, 0.01));
      });

      test('should handle January acquisition correctly (no prorata)', () {
        // Asset acquired in January should have no prorata temporis
        final acquisitionDate = DateTime(2023, 1, 10);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 60000.0,
          residualValue: 0.0,
          usefulLifeYears: 3,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        // First year should be full year (12 months)
        final firstYear = result.amortizationTable.first;
        const expectedAnnuity = 60000.0 * (1.0 / 3);
        expect(firstYear.annuity, closeTo(expectedAnnuity, 0.01));
        expect(firstYear.yearLabel, isNot(contains('mois')));

        // Last year should be 0 months (no last year prorata)
        final lastYear = result.amortizationTable.last;
        expect(lastYear.yearLabel, isNot(contains('mois')));
      });

      test('should calculate correct prorata for December acquisition', () {
        // Asset acquired in December should have minimal first year and maximum last year
        final acquisitionDate = DateTime(2023, 12, 20);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 80000.0,
          residualValue: 0.0,
          usefulLifeYears: 4,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        // First year: December only = 1 month
        final firstYear = result.amortizationTable.first;
        const expectedFirstYearAnnuity = 80000.0 * (1.0 / 4) * (1 / 12);
        expect(firstYear.annuity, closeTo(expectedFirstYearAnnuity, 0.01));
        expect(firstYear.yearLabel, contains('(1 mois)'));

        // Last year: January to November = 11 months (12 - 1)
        final lastYear = result.amortizationTable.last;
        const expectedLastYearAnnuity = 80000.0 * (1.0 / 4) * (11 / 12);
        expect(lastYear.annuity, closeTo(expectedLastYearAnnuity, 0.01));
        expect(lastYear.yearLabel, contains('(11 mois)'));
      });
    });

    group('Degressive Depreciation Tests', () {
      test('should calculate degressive depreciation correctly', () {
        final acquisitionDate = DateTime(2023, 1, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 100000.0,
          residualValue: 0.0,
          usefulLifeYears: 5,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.degressive,
          assetType: 'buildings',
          degressiveCoefficient: 2.5,
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        expect(result.amortizationTable.length, equals(5));
        
        // Verify degressive calculation logic
        double remainingValue = 100000.0;
        for (int i = 0; i < result.amortizationTable.length; i++) {
          final row = result.amortizationTable[i];
          
          if (i < result.amortizationTable.length - 1) {
            // Not the last year - verify degressive rate calculation
            expect(row.baseAmount, equals(100000.0)); // Base amount should remain constant
            remainingValue -= row.annuity;
          } else {
            // Last year should consume remaining value
            expect(row.annuity, closeTo(remainingValue, 0.01));
          }
        }
      });

      test('should switch to linear when beneficial in degressive', () {
        final acquisitionDate = DateTime(2023, 6, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 200000.0,
          residualValue: 0.0,
          usefulLifeYears: 8,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.degressive,
          assetType: 'buildings',
          degressiveCoefficient: 2.0,
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        // In later years, the service should switch to linear if more advantageous
        // We can verify this by checking that annuities don't follow pure degressive pattern
        bool foundLinearSwitch = false;
        
        for (int i = 1; i < result.amortizationTable.length - 1; i++) {
          final row = result.amortizationTable[i];
          
          // If isLinearSwitchYear is set, it means service switched to linear
          if (row.isLinearSwitchYear == true) {
            foundLinearSwitch = true;
            break;
          }
        }

        expect(foundLinearSwitch, isTrue, reason: 'Service should switch to linear when beneficial');
      });
    });

    group('Derogatory Depreciation Tests', () {
      test('should calculate derogatory differences correctly', () {
        final acquisitionDate = DateTime(2023, 4, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 150000.0,
          residualValue: 0.0,
          usefulLifeYears: 6,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
          enableDerogatoire: true,
          fiscalMethod: DepreciationMethod.degressive,
          fiscalDegressiveCoefficient: 2.0,
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        expect(result.amortizationTable.length, equals(6));
        expect(result.derogatoryAmortizationTable, isNotNull);

        final derogatoryTable = result.derogatoryAmortizationTable!;
        for (final row in derogatoryTable) {
          expect(row.accountingAmortization, isNotNull);
          expect(row.fiscalAmortization, isNotNull);
          
          // Verify derogatory provision/reprise calculation
          final accounting = row.accountingAmortization!;
          final fiscal = row.fiscalAmortization!;
          
          if (fiscal > accounting) {
            // Provision needed
            expect(row.derogationProvision, closeTo(fiscal - accounting, 0.01));
            expect(row.derogationReprise, isNull);
          } else if (accounting > fiscal) {
            // Reprise needed
            expect(row.derogationReprise, closeTo(accounting - fiscal, 0.01));
            expect(row.derogationProvision, isNull);
          } else {
            // No derogatory needed
            expect(row.derogationProvision, isNull);
            expect(row.derogationReprise, isNull);
          }
        }
      });

      test('should handle prorata temporis in derogatory calculation', () {
        final acquisitionDate = DateTime(2023, 9, 15); // Mid-September
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 100000.0,
          residualValue: 0.0,
          usefulLifeYears: 5,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
          enableDerogatoire: true,
          fiscalMethod: DepreciationMethod.degressive,
          fiscalDegressiveCoefficient: 2.0,
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        // First year should have prorata temporis applied to both rates
        final firstYear = result.amortizationTable.first;
        expect(firstYear.yearLabel, contains('(4 mois)')); // Sept to Dec = 4 months
        
        // Both accounting and fiscal should be prorated
        const expectedAccountingAnnuity = 100000.0 * (1.0 / 5) * (4 / 12);
        expect(firstYear.annuity, closeTo(expectedAccountingAnnuity, 0.01));
      });
    });

    group('Edge Cases and Validation', () {
      test('should handle zero residual value correctly', () {
        final acquisitionDate = DateTime(2023, 6, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 50000.0,
          residualValue: 0.0, // Zero residual
          usefulLifeYears: 4,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        final totalAmortization = result.amortizationTable
            .fold<double>(0.0, (sum, row) => sum + row.annuity);
        expect(totalAmortization, closeTo(50000.0, 0.01));
        
        final finalVNC = result.amortizationTable.last.netBookValue;
        expect(finalVNC, closeTo(0.0, 0.01));
      });

      test('should handle non-zero residual value correctly', () {
        final acquisitionDate = DateTime(2023, 1, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 100000.0,
          residualValue: 10000.0,
          usefulLifeYears: 5,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        final totalAmortization = result.amortizationTable
            .fold<double>(0.0, (sum, row) => sum + row.annuity);
        expect(totalAmortization, closeTo(100000.0 - 10000.0, 0.01));
        
        final finalVNC = result.amortizationTable.last.netBookValue;
        expect(finalVNC, closeTo(10000.0, 0.01));
      });

      test('should maintain VNC consistency across all years', () {
        final acquisitionDate = DateTime(2023, 3, 10);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 175000.0,
          residualValue: 25000.0,
          usefulLifeYears: 7,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        double expectedVNC = 175000.0;
        for (final row in result.amortizationTable) {
          expectedVNC -= row.annuity;
          expect(row.netBookValue, closeTo(expectedVNC, 0.01));
          expect(row.cumulativeAnnuity, closeTo(175000.0 - expectedVNC, 0.01));
        }
      });

      test('should generate consistent year labels', () {
        final acquisitionDate = DateTime(2023, 7, 20);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 60000.0,
          residualValue: 0.0,
          usefulLifeYears: 3,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        // First year: July to December = 6 months
        expect(result.amortizationTable[0].yearLabel, contains('2023'));
        expect(result.amortizationTable[0].yearLabel, contains('(6 mois)'));

        // Middle year: full year
        expect(result.amortizationTable[1].yearLabel, contains('2024'));
        expect(result.amortizationTable[1].yearLabel, isNot(contains('mois')));

        // Last year: January to June = 6 months  
        expect(result.amortizationTable[2].yearLabel, contains('2025'));
        expect(result.amortizationTable[2].yearLabel, contains('(6 mois)'));
      });

      test('should handle single year duration correctly', () {
        final acquisitionDate = DateTime(2023, 5, 15);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 20000.0,
          residualValue: 0.0,
          usefulLifeYears: 1,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);

        expect(result.amortizationTable.length, equals(1));
        
        final singleRow = result.amortizationTable.first;
        // Single year should amortize from May to April (12 months total)
        expect(singleRow.annuity, closeTo(20000.0, 0.01));
        expect(singleRow.netBookValue, closeTo(0.0, 0.01));
      });

      test('should throw ArgumentError for invalid input', () {
        final acquisitionDate = DateTime(2023, 1, 1);
        final input = EnhancedDepreciationInput(
          assetName: '',
          assetCost: -1000.0, // Invalid negative cost
          residualValue: 0.0,
          usefulLifeYears: 5,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        expect(
          () => service.calculateDepreciation(input, includeComparisons: false),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Performance Tests', () {
      test('should handle large duration efficiently', () {
        final acquisitionDate = DateTime(2023, 1, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Test Asset',
          assetCost: 1000000.0,
          residualValue: 0.0,
          usefulLifeYears: 50, // 50 years
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final stopwatch = Stopwatch()..start();
        
        final result = service.calculateDepreciation(input, includeComparisons: false);
        
        stopwatch.stop();

        expect(result.amortizationTable.length, equals(50));
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should complete within 1 second
        
        // Verify accuracy is maintained even with large duration
        final totalAmortization = result.amortizationTable
            .fold<double>(0.0, (sum, row) => sum + row.annuity);
        expect(totalAmortization, closeTo(1000000.0, 1.0)); // Allow 1 DH tolerance for rounding
      });
    });

    group('Journal Entry Generation Tests', () {
      test('should generate correct journal entries for linear depreciation', () {
        final acquisitionDate = DateTime(2023, 1, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Building',
          assetCost: 100000.0,
          residualValue: 0.0,
          usefulLifeYears: 5,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);
        
        expect(result.journalEntries, isNotNull);
        expect(result.journalEntries!.length, equals(5)); // One entry per year

        // Check first journal entry
        final firstEntry = result.journalEntries!.first;
        expect(firstEntry.lines.length, equals(2)); // Debit and credit lines
        
        final debitLine = firstEntry.lines.first;
        final creditLine = firstEntry.lines.last;
        
        expect(debitLine.account, equals('6193')); // Buildings charge account
        expect(creditLine.account, equals('28111')); // Buildings amortization account
        expect(debitLine.debit, equals(creditLine.credit)); // Amounts should match
      });

      test('should generate derogatory journal entries when enabled', () {
        final acquisitionDate = DateTime(2023, 1, 1);
        final input = EnhancedDepreciationInput(
          assetName: 'Equipment',
          assetCost: 100000.0,
          residualValue: 0.0,
          usefulLifeYears: 5,
          acquisitionDate: acquisitionDate,
          method: DepreciationMethod.linear,
          assetType: 'buildings',
          enableDerogatoire: true,
          fiscalMethod: DepreciationMethod.degressive,
          fiscalDegressiveCoefficient: 2.0,
        );

        final result = service.calculateDepreciation(input, includeComparisons: false);
        
        expect(result.journalEntries, isNotNull);
        expect(result.derogatoryAmortizationTable, isNotNull);
        
        // Should have entries for both accounting and derogatory movements
        final entries = result.journalEntries!;
        expect(entries.isNotEmpty, isTrue);
        
        // Check if any entry has derogatory accounts
        bool hasDerogatory = false;
        for (final entry in entries) {
          for (final line in entry.lines) {
            if (line.account == '65941' || line.account == '1351' || line.account == '75941') {
              hasDerogatory = true;
              break;
            }
          }
        }
        
        expect(hasDerogatory, isTrue, reason: 'Should generate derogatory journal entries');
      });
    });
  });
}