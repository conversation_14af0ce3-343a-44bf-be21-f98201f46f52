import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/models/immobilisations/amortization_row.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_approfondie/regles_evaluation/sections/calculateur_section.dart';

void main() {
  group('Simple Calculator (CalculateurSection)', () {
    late CalculateurSection widget;

    setUp(() {
      widget = const CalculateurSection();
    });

    group('Prorata Temporis Bug Fix Tests', () {
      testWidgets('should calculate correct prorata temporis for mid-year acquisition', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        // Find form fields
        final valueField = find.byType(TextFormField).at(0);
        final durationField = find.byType(TextFormField).at(1);
        final rateField = find.byType(TextFormField).at(2);

        // Enter test values
        await tester.enterText(valueField, '100000'); // 100,000 DH
        await tester.enterText(durationField, '5'); // 5 years
        await tester.pumpAndSettle();

        // Set acquisition date to March 15, 2023
        final dateTile = find.byType(ListTile);
        await tester.tap(dateTile);
        await tester.pumpAndSettle();

        // Mock date picker - in a real test environment, we would need to handle this
        // For now, we'll focus on testing the calculation logic separately

        // Get the widget state to test calculation method directly
        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Test March acquisition (month 3)
        final marchDate = DateTime(2023, 3, 15);
        final testTable = calculatorState._generateAmortizationTable(
          value: 100000.0,
          duration: 5,
          rate: 20.0,
          method: 'lineaire',
          acquisitionDate: marchDate,
        );

        expect(testTable.length, equals(5));

        // First year: March to December = 10 months
        final firstYear = testTable.first;
        const expectedFirstYear = 100000 * 0.20 * (10 / 12);
        expect(firstYear.annuity, closeTo(expectedFirstYear, 0.01));
        expect(firstYear.yearLabel, contains('(10 mois)'));

        // Last year: January to February = 2 months
        final lastYear = testTable.last;
        const expectedLastYear = 100000 * 0.20 * (2 / 12);
        expect(lastYear.annuity, closeTo(expectedLastYear, 0.01));
        expect(lastYear.yearLabel, contains('(2 mois)'));

        // Total should equal original value
        final totalAmortization = testTable.fold<double>(0.0, (sum, row) => sum + row.annuity);
        expect(totalAmortization, closeTo(100000.0, 0.01));
      });

      testWidgets('should handle January acquisition correctly (no prorata)', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Test January acquisition (month 1)
        final januaryDate = DateTime(2023, 1, 10);
        final testTable = calculatorState._generateAmortizationTable(
          value: 60000.0,
          duration: 3,
          rate: 33.33,
          method: 'lineaire',
          acquisitionDate: januaryDate,
        );

        expect(testTable.length, equals(3));

        // First year should be full year (12 months remaining)
        final firstYear = testTable.first;
        const expectedFirstYear = 60000 * 0.3333;
        expect(firstYear.annuity, closeTo(expectedFirstYear, 0.01));
        expect(firstYear.yearLabel, isNot(contains('mois'))); // No month indicator

        // Last year should have no prorata (0 months before January)
        final lastYear = testTable.last;
        expect(lastYear.yearLabel, isNot(contains('mois'))); // No month indicator
      });

      testWidgets('should calculate correct prorata for December acquisition', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Test December acquisition (month 12)
        final decemberDate = DateTime(2023, 12, 20);
        final testTable = calculatorState._generateAmortizationTable(
          value: 80000.0,
          duration: 4,
          rate: 25.0,
          method: 'lineaire',
          acquisitionDate: decemberDate,
        );

        expect(testTable.length, equals(4));

        // First year: December only = 1 month remaining
        final firstYear = testTable.first;
        const expectedFirstYear = 80000 * 0.25 * (1 / 12);
        expect(firstYear.annuity, closeTo(expectedFirstYear, 0.01));
        expect(firstYear.yearLabel, contains('(1 mois)'));

        // Last year: January to November = 11 months (12 - 1)
        final lastYear = testTable.last;
        const expectedLastYear = 80000 * 0.25 * (11 / 12);
        expect(lastYear.annuity, closeTo(expectedLastYear, 0.01));
        expect(lastYear.yearLabel, contains('(11 mois)'));
      });
    });

    group('Degressive Method Tests', () {
      testWidgets('should calculate degressive depreciation correctly', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        final acquisitionDate = DateTime(2023, 1, 1);
        final testTable = calculatorState._generateAmortizationTable(
          value: 100000.0,
          duration: 5,
          rate: 40.0, // 40% degressive rate
          method: 'degressif',
          acquisitionDate: acquisitionDate,
        );

        expect(testTable.length, equals(5));

        // In degressive method, each year's base should be the remaining value
        double remainingValue = 100000.0;
        for (int i = 0; i < testTable.length - 1; i++) {
          final row = testTable[i];
          expect(row.baseAmount, equals(100000.0)); // Original value for display
          remainingValue -= row.annuity;
          
          // Each year should depreciate less than the previous (except when switching to linear)
          if (i > 0 && testTable[i-1].annuity > 0) {
            expect(row.annuity, lessThanOrEqualTo(testTable[i-1].annuity * 1.1)); // Allow for rounding and linear switch
          }
        }

        // Last year should consume remaining value
        final lastYear = testTable.last;
        expect(lastYear.annuity, closeTo(remainingValue, 0.01));
      });

      testWidgets('should handle prorata temporis in degressive method', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        final acquisitionDate = DateTime(2023, 6, 15); // Mid-year
        final testTable = calculatorState._generateAmortizationTable(
          value: 120000.0,
          duration: 4,
          rate: 50.0,
          method: 'degressif',
          acquisitionDate: acquisitionDate,
        );

        expect(testTable.length, equals(4));

        // First year should have prorata temporis applied
        final firstYear = testTable.first;
        expect(firstYear.yearLabel, contains('(7 mois)')); // June to December = 7 months

        // Last year should have prorata temporis applied
        final lastYear = testTable.last;
        expect(lastYear.yearLabel, contains('(5 mois)')); // January to May = 5 months
      });
    });

    group('Asset Type Account Mapping Tests', () {
      testWidgets('should map asset types to correct Moroccan chart accounts', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Test different asset type mappings
        const testCases = {
          'buildings': ('6193', '28111'), // Constructions
          'technical_installations': ('6192', '28121'), // Installations techniques
          'transport_equipment': ('6195', '28131'), // Matériel de transport
          'office_equipment': ('6194', '28141'), // Mobilier de bureau
          'computer_equipment': ('6196', '28151'), // Matériel informatique
          'other_equipment': ('6192', '28121'), // Autres équipements
          'land_improvements': ('6193', '28101'), // Terrains aménagés
          'intangible_assets': ('6197', '2811'), // Immobilisations incorporelles
        };

        for (final testCase in testCases.entries) {
          // Set the asset type
          calculatorState._selectedAssetType = testCase.key;
          
          final accounts = calculatorState._getAccountsForAsset();
          expect(accounts.$1, equals(testCase.value.$1), reason: 'Charge account for ${testCase.key}');
          expect(accounts.$2, equals(testCase.value.$2), reason: 'Amortization account for ${testCase.key}');
        }
      });
    });

    group('Journal Entries Generation Tests', () {
      testWidgets('should generate correct journal entries', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Create a test amortization table
        final testDate = DateTime(2023, 1, 1);
        calculatorState._amortizationTable = [
          AmortizationRow(
            year: 2023,
            yearLabel: '2023',
            baseAmount: 100000.0,
            rate: 20.0,
            annuity: 20000.0,
            cumulativeAnnuity: 20000.0,
            netBookValue: 80000.0,
          ),
          AmortizationRow(
            year: 2024,
            yearLabel: '2024',
            baseAmount: 100000.0,
            rate: 20.0,
            annuity: 20000.0,
            cumulativeAnnuity: 40000.0,
            netBookValue: 60000.0,
          ),
        ];

        // Set asset type to equipment
        calculatorState._selectedAssetType = 'technical_installations';

        // Generate journal entries
        final journalEntries = calculatorState._generateJournalEntries();

        expect(journalEntries.length, equals(2));

        // Test first entry
        final firstEntry = journalEntries.first;
        expect(firstEntry.date, equals('2023'));
        expect(firstEntry.lines.length, equals(2));

        final debitLine = firstEntry.lines.firstWhere((line) => line.debit != null);
        final creditLine = firstEntry.lines.firstWhere((line) => line.credit != null);

        expect(debitLine.account, equals('6192')); // Technical installations charge account
        expect(creditLine.account, equals('28121')); // Technical installations amortization account
        expect(debitLine.debit, equals('20000.00'));
        expect(creditLine.credit, equals('20000.00'));
        expect(debitLine.label, contains('Dotation aux amortissements 2023'));
        expect(creditLine.label, contains('Cumul amortissements 2023'));
      });
    });

    group('Export Functionality Tests', () {
      testWidgets('should handle export with valid data', (tester) async {
        await tester.pumpWidget(MaterialApp(home: Scaffold(body: widget)));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Set up calculation data
        calculatorState._amortizationTable = [
          AmortizationRow(
            year: 2023,
            yearLabel: '2023',
            baseAmount: 50000.0,
            rate: 25.0,
            annuity: 12500.0,
            cumulativeAnnuity: 12500.0,
            netBookValue: 37500.0,
          ),
        ];

        // Generate journal entries
        calculatorState._journalEntries = calculatorState._generateJournalEntries();

        // Test TableData conversion
        final amortTableData = TableData.fromAmortizationRows(
          calculatorState._amortizationTable,
          name: 'amortization',
          title: 'Tableau d\'amortissement',
          isDegressive: false,
        );

        expect(amortTableData.headers.length, equals(6));
        expect(amortTableData.rows.length, equals(1));
        expect(amortTableData.rows.first[0], equals('2023')); // Year
        expect(amortTableData.rows.first[1], equals('50000.00')); // Base amount
        expect(amortTableData.rows.first[2], equals('25.00')); // Rate
        expect(amortTableData.rows.first[3], equals('12500.00')); // Annuity

        // Test journal data conversion
        final journalTableData = calculatorState._journalEntriesToTableData();
        expect(journalTableData.headers.length, equals(5));
        expect(journalTableData.title, equals('Écritures comptables'));
      });

      testWidgets('should handle export errors gracefully', (tester) async {
        await tester.pumpWidget(MaterialApp(home: Scaffold(body: widget)));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Try to export with empty data
        calculatorState._amortizationTable = [];

        // This should not crash the widget
        await tester.pumpAndSettle();

        // Verify the table is empty
        expect(calculatorState._amortizationTable.isEmpty, isTrue);
      });
    });

    group('Edge Cases and Validation', () {
      testWidgets('should handle zero rate correctly', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        final acquisitionDate = DateTime(2023, 1, 1);
        final testTable = calculatorState._generateAmortizationTable(
          value: 100000.0,
          duration: 5,
          rate: 0.0, // Zero rate
          method: 'lineaire',
          acquisitionDate: acquisitionDate,
        );

        expect(testTable.length, equals(5));

        // All annuities should be zero
        for (final row in testTable) {
          expect(row.annuity, equals(0.0));
        }

        // Net book value should remain at original value
        for (final row in testTable) {
          expect(row.netBookValue, equals(100000.0));
        }
      });

      testWidgets('should handle single year duration', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        final acquisitionDate = DateTime(2023, 6, 1);
        final testTable = calculatorState._generateAmortizationTable(
          value: 24000.0,
          duration: 1,
          rate: 100.0, // 100% for single year
          method: 'lineaire',
          acquisitionDate: acquisitionDate,
        );

        expect(testTable.length, equals(1));

        final singleRow = testTable.first;
        expect(singleRow.annuity, closeTo(24000.0, 0.01));
        expect(singleRow.netBookValue, closeTo(0.0, 0.01));
        expect(singleRow.cumulativeAnnuity, closeTo(24000.0, 0.01));
      });

      testWidgets('should maintain mathematical consistency', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        final acquisitionDate = DateTime(2023, 4, 15);
        final testTable = calculatorState._generateAmortizationTable(
          value: 150000.0,
          duration: 6,
          rate: 16.67,
          method: 'lineaire',
          acquisitionDate: acquisitionDate,
        );

        // Verify mathematical relationships
        double runningCumulative = 0.0;
        for (int i = 0; i < testTable.length; i++) {
          final row = testTable[i];
          
          runningCumulative += row.annuity;
          expect(row.cumulativeAnnuity, closeTo(runningCumulative, 0.01));
          
          final expectedVNC = 150000.0 - runningCumulative;
          expect(row.netBookValue, closeTo(expectedVNC, 0.01));
          
          // Rate should be consistent
          expect(row.rate, equals(16.67));
          
          // Base amount should always be original value
          expect(row.baseAmount, equals(150000.0));
        }

        // Total amortization should equal original value
        final totalAmortization = testTable.fold<double>(0.0, (sum, row) => sum + row.annuity);
        expect(totalAmortization, closeTo(150000.0, 0.01));

        // Final VNC should be zero or close to it
        expect(testTable.last.netBookValue, closeTo(0.0, 0.01));
      });
    });

    group('Year Label Generation Tests', () {
      testWidgets('should generate correct year labels for different scenarios', (tester) async {
        await tester.pumpWidget(MaterialApp(home: widget));

        final calculatorState = tester.state<_CalculateurSectionState>(find.byType(CalculateurSection));

        // Test different acquisition months
        final testScenarios = [
          (DateTime(2023, 1, 15), ['2023', '2024', '2025'], [null, null, null]), // January - no prorata
          (DateTime(2023, 3, 10), ['2023 (10 mois)', '2024', '2025 (2 mois)']), // March - prorata both ends
          (DateTime(2023, 7, 1), ['2023 (6 mois)', '2024', '2025 (6 mois)']), // July - prorata both ends
          (DateTime(2023, 12, 31), ['2023 (1 mois)', '2024', '2025 (11 mois)']), // December - minimal first year
        ];

        for (final scenario in testScenarios) {
          final table = calculatorState._generateAmortizationTable(
            value: 90000.0,
            duration: 3,
            rate: 33.33,
            method: 'lineaire',
            acquisitionDate: scenario.$1,
          );

          expect(table.length, equals(3));
          
          for (int i = 0; i < table.length; i++) {
            expect(table[i].yearLabel, contains(scenario.$2[i].split(' ')[0])); // Year should match
            
            if (scenario.$2[i].contains('mois')) {
              expect(table[i].yearLabel, contains('mois')); // Should have month indicator
            }
          }
        }
      });
    });
  });
}