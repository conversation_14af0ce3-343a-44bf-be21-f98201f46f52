import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/models/immobilisations/amortization_row.dart';

void main() {
  group('AmortizationRow.calculateMonthsInYear Edge Cases', () {
    
    group('December Acquisitions', () {
      test('December 1st acquisition should return 1 month for first year', () {
        final decemberFirst = DateTime(2024, 12, 1);
        expect(
          AmortizationRow.calculateMonthsInYear(decemberFirst, 1, 5),
          equals(1),
        );
      });

      test('December 31st acquisition should return 1 month for first year', () {
        final decemberLast = DateTime(2024, 12, 31);
        expect(
          AmortizationRow.calculateMonthsInYear(decemberLast, 1, 5),
          equals(1),
        );
      });

      test('December acquisition should distribute remaining months correctly', () {
        final december = DateTime(2024, 12, 15);
        
        // First year: 1 month
        expect(AmortizationRow.calculateMonthsInYear(december, 1, 3), equals(1));
        
        // Second year: 12 months
        expect(AmortizationRow.calculateMonthsInYear(december, 2, 3), equals(12));
        
        // Third year: remaining months (36 - 1 - 12 = 23, but max 12 per year)
        expect(AmortizationRow.calculateMonthsInYear(december, 3, 3), equals(12));
        
        // Fourth year: final months (36 - 1 - 12 - 12 = 11)
        expect(AmortizationRow.calculateMonthsInYear(december, 4, 3), equals(11));
      });
    });

    group('Leap Year Handling', () {
      test('February 29th in leap year should be valid', () {
        final feb29LeapYear = DateTime(2024, 2, 29); // 2024 is a leap year
        expect(
          () => AmortizationRow.calculateMonthsInYear(feb29LeapYear, 1, 5),
          returnsNormally,
        );
        
        // Should return 11 months (March to December)
        expect(
          AmortizationRow.calculateMonthsInYear(feb29LeapYear, 1, 5),
          equals(11),
        );
      });

      test('February 29th in non-leap year should throw ArgumentError', () {
        final feb29NonLeapYear = DateTime(2023, 2, 29); // 2023 is not a leap year
        expect(
          () => AmortizationRow.calculateMonthsInYear(feb29NonLeapYear, 1, 5),
          throwsA(isA<ArgumentError>()
              .having((e) => e.message, 'message', contains('Invalid leap year date'))),
        );
      });

      test('Leap year validation for various years', () {
        // Test leap years
        final leapYears = [2000, 2004, 2020, 2024];
        for (final year in leapYears) {
          expect(
            () => AmortizationRow.calculateMonthsInYear(DateTime(year, 2, 29), 1, 5),
            returnsNormally,
            reason: '$year should be a valid leap year',
          );
        }

        // Test non-leap years (century years not divisible by 400)
        final nonLeapYears = [1900, 2100, 2200];
        for (final year in nonLeapYears) {
          expect(
            () => AmortizationRow.calculateMonthsInYear(DateTime(year, 2, 29), 1, 5),
            throwsArgumentError,
            reason: '$year should not be a valid leap year',
          );
        }
      });
    });

    group('Invalid Date Ranges', () {
      test('Future dates beyond 1 year should throw ArgumentError', () {
        final farFuture = DateTime.now().add(const Duration(days: 400));
        expect(
          () => AmortizationRow.calculateMonthsInYear(farFuture, 1, 5),
          throwsA(isA<ArgumentError>()
              .having((e) => e.message, 'message', contains('cannot be more than 1 year in the future'))),
        );
      });

      test('Future dates within 1 year should be valid', () {
        final nearFuture = DateTime.now().add(const Duration(days: 200));
        expect(
          () => AmortizationRow.calculateMonthsInYear(nearFuture, 1, 5),
          returnsNormally,
        );
      });

      test('Dates before 1900 should throw ArgumentError', () {
        final ancientDate = DateTime(1899, 12, 31);
        expect(
          () => AmortizationRow.calculateMonthsInYear(ancientDate, 1, 5),
          throwsA(isA<ArgumentError>()
              .having((e) => e.message, 'message', contains('cannot be before 1900'))),
        );
      });

      test('Date exactly at 1900-01-01 should be valid', () {
        final minDate = DateTime(1900, 1, 1);
        expect(
          () => AmortizationRow.calculateMonthsInYear(minDate, 1, 5),
          returnsNormally,
        );
      });
    });

    group('Invalid Parameter Validation', () {
      test('yearIndex less than 1 should throw ArgumentError', () {
        final date = DateTime(2024, 6, 15);
        
        expect(
          () => AmortizationRow.calculateMonthsInYear(date, 0, 5),
          throwsA(isA<ArgumentError>()
              .having((e) => e.message, 'message', contains('yearIndex must be at least 1'))),
        );

        expect(
          () => AmortizationRow.calculateMonthsInYear(date, -1, 5),
          throwsA(isA<ArgumentError>()
              .having((e) => e.message, 'message', contains('yearIndex must be at least 1'))),
        );
      });

      test('Negative totalYears should throw ArgumentError', () {
        final date = DateTime(2024, 6, 15);
        expect(
          () => AmortizationRow.calculateMonthsInYear(date, 1, -1),
          throwsA(isA<ArgumentError>()
              .having((e) => e.message, 'message', contains('totalYears cannot be negative'))),
        );
      });

      test('totalYears exceeding 100 should throw ArgumentError', () {
        final date = DateTime(2024, 6, 15);
        expect(
          () => AmortizationRow.calculateMonthsInYear(date, 1, 101),
          throwsA(isA<ArgumentError>()
              .having((e) => e.message, 'message', contains('totalYears cannot exceed 100 years'))),
        );
      });

      test('totalYears exactly 100 should be valid', () {
        final date = DateTime(2024, 6, 15);
        expect(
          () => AmortizationRow.calculateMonthsInYear(date, 1, 100),
          returnsNormally,
        );
      });
    });

    group('Boundary Conditions', () {
      test('yearIndex beyond amortization period should return 0', () {
        final date = DateTime(2024, 6, 15);
        
        // For 2-year amortization, year 5 should return 0
        expect(AmortizationRow.calculateMonthsInYear(date, 5, 2), equals(0));
        
        // For 1-year amortization starting in June, year 2 might have remaining months
        // June acquisition: 7 months in first year (June-December)
        // For 1 year total (12 months), second year gets 12 - 7 = 5 months
        expect(AmortizationRow.calculateMonthsInYear(date, 2, 1), equals(5));
        
        // Year 3 should be 0
        expect(AmortizationRow.calculateMonthsInYear(date, 3, 1), equals(0));
      });

      test('First year months should be clamped between 1 and 12', () {
        // Test various acquisition dates
        final testCases = [
          (DateTime(2024, 1, 1), 12), // January: 12 months
          (DateTime(2024, 6, 15), 7),  // June: 7 months 
          (DateTime(2024, 11, 1), 2),  // November: 2 months
          (DateTime(2024, 12, 31), 1), // December: 1 month
        ];

        for (final (date, expectedMonths) in testCases) {
          final result = AmortizationRow.calculateMonthsInYear(date, 1, 5);
          expect(result, equals(expectedMonths));
          expect(result, greaterThanOrEqualTo(1));
          expect(result, lessThanOrEqualTo(12));
        }
      });
    });

    group('Unknown totalYears Handling', () {
      test('Zero totalYears should use prorata for first year, full years after', () {
        final date = DateTime(2024, 6, 15);
        
        // First year with unknown duration: use prorata (June = 7 months)
        expect(AmortizationRow.calculateMonthsInYear(date, 1, 0), equals(7));
        
        // Subsequent years with unknown duration: full years
        expect(AmortizationRow.calculateMonthsInYear(date, 2, 0), equals(12));
        expect(AmortizationRow.calculateMonthsInYear(date, 5, 0), equals(12));
      });
    });

    group('Complex Scenarios', () {
      test('Mid-year acquisition with exact total years', () {
        // June 15th acquisition for exactly 2.5 years (30 months)
        final june15 = DateTime(2024, 6, 15);
        const totalYears = 2; // Will be distributed as 30 months total when multiplied by 12, but we're testing 2 years = 24 months
        
        // Year 1: June-December = 7 months
        expect(AmortizationRow.calculateMonthsInYear(june15, 1, totalYears), equals(7));
        
        // Year 2: Full year = 12 months  
        expect(AmortizationRow.calculateMonthsInYear(june15, 2, totalYears), equals(12));
        
        // Year 3: Remaining months = 24 - 7 - 12 = 5 months
        expect(AmortizationRow.calculateMonthsInYear(june15, 3, totalYears), equals(5));
        
        // Year 4: Should be 0 (beyond amortization period)
        expect(AmortizationRow.calculateMonthsInYear(june15, 4, totalYears), equals(0));
      });

      test('January 1st acquisition should give full first year', () {
        final jan1 = DateTime(2024, 1, 1);
        
        // January 1st means full first year
        expect(AmortizationRow.calculateMonthsInYear(jan1, 1, 3), equals(12));
        expect(AmortizationRow.calculateMonthsInYear(jan1, 2, 3), equals(12));
        expect(AmortizationRow.calculateMonthsInYear(jan1, 3, 3), equals(12));
        expect(AmortizationRow.calculateMonthsInYear(jan1, 4, 3), equals(0));
      });

      test('Short amortization period edge case', () {
        // November acquisition for 1 year total
        final nov1 = DateTime(2024, 11, 1);
        
        // Year 1: November-December = 2 months
        expect(AmortizationRow.calculateMonthsInYear(nov1, 1, 1), equals(2));
        
        // Year 2: Remaining 10 months
        expect(AmortizationRow.calculateMonthsInYear(nov1, 2, 1), equals(10));
        
        // Year 3: Should be 0
        expect(AmortizationRow.calculateMonthsInYear(nov1, 3, 1), equals(0));
      });
    });

    group('Helper Methods', () {
      test('_isLeapYear should correctly identify leap years', () {
        // Test leap years
        expect(AmortizationRow.calculateMonthsInYear(DateTime(2000, 2, 29), 1, 1), greaterThan(0)); // 2000 is leap
        expect(AmortizationRow.calculateMonthsInYear(DateTime(2004, 2, 29), 1, 1), greaterThan(0)); // 2004 is leap
        expect(AmortizationRow.calculateMonthsInYear(DateTime(2020, 2, 29), 1, 1), greaterThan(0)); // 2020 is leap
        
        // Test non-leap years by trying to use Feb 29 (should throw)
        expect(() => AmortizationRow.calculateMonthsInYear(DateTime(1900, 2, 29), 1, 1), throwsArgumentError); // 1900 is not leap
        expect(() => AmortizationRow.calculateMonthsInYear(DateTime(2001, 2, 29), 1, 1), throwsArgumentError); // 2001 is not leap
        expect(() => AmortizationRow.calculateMonthsInYear(DateTime(2100, 2, 29), 1, 1), throwsArgumentError); // 2100 is not leap
      });
    });
  });

  group('AmortizationRow Additional Properties', () {
    test('isPartialYear should detect partial year labels', () {
      final partialYearRow = AmortizationRow(
        year: 2024,
        yearLabel: '2024 (8 mois)',
        baseAmount: 100000,
        rate: 10,
        annuity: 6666.67,
        cumulativeAnnuity: 6666.67,
        netBookValue: 93333.33,
      );

      final fullYearRow = AmortizationRow(
        year: 2025,
        yearLabel: '2025',
        baseAmount: 100000,
        rate: 10,
        annuity: 10000,
        cumulativeAnnuity: 16666.67,
        netBookValue: 83333.33,
      );

      expect(partialYearRow.isPartialYear, isTrue);
      expect(fullYearRow.isPartialYear, isFalse);
    });

    test('monthsInYear should extract months from label', () {
      final testCases = [
        ('2024 (8 mois)', 8),
        ('2025 (12 mois)', 12),
        ('2026 (1 mois)', 1),
        ('2027', 12), // No months specified = full year
        ('2028 (0 mois)', 12), // Invalid case should default to 12
        ('2029 (15 mois)', 12), // Invalid case should default to 12 (clamp)
      ];

      for (final (label, expectedMonths) in testCases) {
        final row = AmortizationRow(
          year: 2024,
          yearLabel: label,
          baseAmount: 100000,
          rate: 10,
          annuity: 10000,
          cumulativeAnnuity: 10000,
          netBookValue: 90000,
        );

        if (expectedMonths <= 12) {
          expect(row.monthsInYear, equals(expectedMonths));
        } else {
          expect(row.monthsInYear, equals(12)); // Should be clamped
        }
      }
    });
  });
}