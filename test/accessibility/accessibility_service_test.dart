import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:moroccanaccounting/services/accessibility_service.dart';
import 'package:moroccanaccounting/models/accessibility/accessibility_preferences.dart';
import 'package:moroccanaccounting/services/theme_service.dart';

import 'accessibility_service_test.mocks.dart';

// Generate mocks for external dependencies
@GenerateMocks([
  FlutterTts,
  DeviceInfoPlugin,
  ThemeService,
  Box<AccessibilityPreferences>,
])
void main() {
  group('AccessibilityService', () {
    late AccessibilityService accessibilityService;
    late MockFlutterTts mockTts;
    late MockDeviceInfoPlugin mockDeviceInfo;
    late MockThemeService mockThemeService;
    late MockBox<AccessibilityPreferences> mockBox;

    setUp(() async {
      // Initialize Hive for testing
      Hive.init('test');
      Hive.registerAdapter(AccessibilityPreferencesAdapter());
      Hive.registerAdapter(PreferredNavigationModeAdapter());

      // Create mocks
      mockTts = MockFlutterTts();
      mockDeviceInfo = MockDeviceInfoPlugin();
      mockThemeService = MockThemeService();
      mockBox = MockBox<AccessibilityPreferences>();

      // Setup default mock behaviors
      when(mockTts.setLanguage(any)).thenAnswer((_) async => 1);
      when(mockTts.setSpeechRate(any)).thenAnswer((_) async => 1);
      when(mockTts.setVolume(any)).thenAnswer((_) async => 1);
      when(mockTts.setPitch(any)).thenAnswer((_) async => 1);
      when(mockTts.getLanguages).thenAnswer((_) async => ['fr-FR', 'en-US']);
      when(mockTts.speak(any)).thenAnswer((_) async => 1);
      when(mockTts.stop()).thenAnswer((_) async => 1);

      when(mockBox.get(any)).thenReturn(null);
      when(mockBox.put(any, any)).thenAnswer((_) async {});

      accessibilityService = AccessibilityService();
    });

    tearDown(() async {
      await Hive.deleteFromDisk();
    });

    group('Initialization', () {
      testWidgets('should initialize with default preferences', (tester) async {
        await accessibilityService.initialize();

        expect(accessibilityService.preferences, isA<AccessibilityPreferences>());
        expect(accessibilityService.fontScale, equals(1.0));
        expect(accessibilityService.isHighContrastEnabled, isFalse);
        expect(accessibilityService.isScreenReaderEnabled, isFalse);
        expect(accessibilityService.isKeyboardNavigationEnabled, isFalse);
        expect(accessibilityService.isReducedMotionEnabled, isFalse);
      });

      testWidgets('should handle initialization errors gracefully', (tester) async {
        // Simulate initialization error
        when(mockTts.setLanguage(any)).thenThrow(Exception('TTS initialization failed'));

        await accessibilityService.initialize();

        // Should still have default preferences
        expect(accessibilityService.preferences, isA<AccessibilityPreferences>());
        expect(accessibilityService.isTtsAvailable, isFalse);
      });

      testWidgets('should load stored preferences on initialization', (tester) async {
        final storedPreferences = AccessibilityPreferences(
          fontSize: 1.5,
          highContrastMode: true,
          screenReaderEnabled: true,
          keyboardNavigationEnabled: true,
          reducedMotion: false,
          preferredNavigationMode: PreferredNavigationMode.keyboard,
        );

        when(mockBox.get('user_accessibility_preferences')).thenReturn(storedPreferences);

        await accessibilityService.initialize();

        expect(accessibilityService.fontScale, equals(1.5));
        expect(accessibilityService.isHighContrastEnabled, isTrue);
        expect(accessibilityService.isScreenReaderEnabled, isTrue);
        expect(accessibilityService.isKeyboardNavigationEnabled, isTrue);
      });
    });

    group('Preference Storage and Retrieval', () {
      testWidgets('should save preferences to storage', (tester) async {
        await accessibilityService.initialize();

        await accessibilityService.updateFontScale(1.2);

        verify(mockBox.put('user_accessibility_preferences', any)).called(greaterThan(0));
      });

      testWidgets('should persist preferences across service instances', (tester) async {
        final preferences = AccessibilityPreferences(
          fontSize: 1.3,
          highContrastMode: true,
          screenReaderEnabled: false,
          keyboardNavigationEnabled: true,
          reducedMotion: true,
          preferredNavigationMode: PreferredNavigationMode.touch,
        );

        when(mockBox.get('user_accessibility_preferences')).thenReturn(preferences);

        await accessibilityService.initialize();

        expect(accessibilityService.fontScale, equals(1.3));
        expect(accessibilityService.isHighContrastEnabled, isTrue);
        expect(accessibilityService.isKeyboardNavigationEnabled, isTrue);
        expect(accessibilityService.isReducedMotionEnabled, isTrue);
      });

      testWidgets('should handle storage errors gracefully', (tester) async {
        when(mockBox.put(any, any)).thenThrow(Exception('Storage error'));

        await accessibilityService.initialize();

        // Should not throw when saving fails
        expect(() => accessibilityService.updateFontScale(1.1), returnsNormally);
      });
    });

    group('Font Scaling', () {
      testWidgets('should update font scale within valid range', (tester) async {
        await accessibilityService.initialize();

        await accessibilityService.updateFontScale(1.5);
        expect(accessibilityService.fontScale, equals(1.5));

        await accessibilityService.updateFontScale(0.8);
        expect(accessibilityService.fontScale, equals(0.8));

        await accessibilityService.updateFontScale(2.0);
        expect(accessibilityService.fontScale, equals(2.0));
      });

      testWidgets('should throw error for invalid font scale', (tester) async {
        await accessibilityService.initialize();

        expect(
          () => accessibilityService.updateFontScale(0.7),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => accessibilityService.updateFontScale(2.1),
          throwsA(isA<ArgumentError>()),
        );
      });

      testWidgets('should calculate scaled font sizes correctly', (tester) async {
        await accessibilityService.initialize();

        await accessibilityService.updateFontScale(1.5);

        expect(accessibilityService.getScaledFontSize(16.0), equals(24.0));
        expect(accessibilityService.getScaledFontSize(12.0), equals(18.0));
        expect(accessibilityService.getScaledFontSize(20.0), equals(30.0));
      });

      testWidgets('should notify listeners when font scale changes', (tester) async {
        await accessibilityService.initialize();

        bool notified = false;
        accessibilityService.addListener(() {
          notified = true;
        });

        await accessibilityService.updateFontScale(1.2);

        expect(notified, isTrue);
      });

      testWidgets('should trigger theme service update when font scale changes', (tester) async {
        await accessibilityService.initialize();
        accessibilityService.setThemeService(mockThemeService);

        await accessibilityService.updateFontScale(1.3);

        verify(mockThemeService.notifyListeners()).called(1);
      });
    });

    group('High Contrast Mode', () {
      testWidgets('should toggle high contrast mode', (tester) async {
        await accessibilityService.initialize();

        expect(accessibilityService.isHighContrastEnabled, isFalse);

        await accessibilityService.toggleHighContrast();
        expect(accessibilityService.isHighContrastEnabled, isTrue);

        await accessibilityService.toggleHighContrast();
        expect(accessibilityService.isHighContrastEnabled, isFalse);
      });

      testWidgets('should notify listeners when high contrast changes', (tester) async {
        await accessibilityService.initialize();

        bool notified = false;
        accessibilityService.addListener(() {
          notified = true;
        });

        await accessibilityService.toggleHighContrast();

        expect(notified, isTrue);
      });

      testWidgets('should trigger theme service update when high contrast changes', (tester) async {
        await accessibilityService.initialize();
        accessibilityService.setThemeService(mockThemeService);

        await accessibilityService.toggleHighContrast();

        verify(mockThemeService.notifyListeners()).called(1);
      });

      testWidgets('should return correct high contrast status', (tester) async {
        await accessibilityService.initialize();

        expect(accessibilityService.shouldUseHighContrast(), isFalse);

        await accessibilityService.toggleHighContrast();
        expect(accessibilityService.shouldUseHighContrast(), isTrue);
      });
    });

    group('Screen Reader Integration', () {
      testWidgets('should enable and disable screen reader', (tester) async {
        await accessibilityService.initialize();

        expect(accessibilityService.isScreenReaderEnabled, isFalse);

        await accessibilityService.enableScreenReader(true);
        expect(accessibilityService.isScreenReaderEnabled, isTrue);

        await accessibilityService.enableScreenReader(false);
        expect(accessibilityService.isScreenReaderEnabled, isFalse);
      });

      testWidgets('should announce messages when screen reader is enabled', (tester) async {
        await accessibilityService.initialize();
        await accessibilityService.enableScreenReader(true);

        await accessibilityService.announceToScreenReader('Test message');

        verify(mockTts.speak('Test message')).called(1);
      });

      testWidgets('should not announce when screen reader is disabled', (tester) async {
        await accessibilityService.initialize();
        await accessibilityService.enableScreenReader(false);

        await accessibilityService.announceToScreenReader('Test message');

        verifyNever(mockTts.speak(any));
      });

      testWidgets('should announce navigation changes', (tester) async {
        await accessibilityService.initialize();
        await accessibilityService.enableScreenReader(true);

        await accessibilityService.announceNavigationChange('Home');

        verify(mockTts.speak('Navigation vers Home')).called(1);
      });

      testWidgets('should announce content loading states', (tester) async {
        await accessibilityService.initialize();
        await accessibilityService.enableScreenReader(true);

        await accessibilityService.announceContentLoading();
        verify(mockTts.speak('Chargement du contenu')).called(1);

        await accessibilityService.announceContentLoaded('Guide');
        verify(mockTts.speak('Guide chargé')).called(1);
      });

      testWidgets('should announce errors and success messages', (tester) async {
        await accessibilityService.initialize();
        await accessibilityService.enableScreenReader(true);

        await accessibilityService.announceError('Connection failed');
        verify(mockTts.speak('Erreur: Connection failed')).called(1);

        await accessibilityService.announceSuccess('Data saved');
        verify(mockTts.speak('Succès: Data saved')).called(1);
      });
    });

    group('Semantic Label Generation', () {
      testWidgets('should generate button labels correctly', (tester) async {
        await accessibilityService.initialize();

        expect(
          accessibilityService.generateButtonLabel('Save', 'form'),
          equals('Save, bouton, form'),
        );

        expect(
          accessibilityService.generateButtonLabel('Cancel', ''),
          equals('Cancel, bouton'),
        );
      });

      testWidgets('should generate navigation labels correctly', (tester) async {
        await accessibilityService.initialize();

        expect(
          accessibilityService.generateNavigationLabel('Home'),
          equals('Naviguer vers Home'),
        );

        expect(
          accessibilityService.generateNavigationLabel('Settings'),
          equals('Naviguer vers Settings'),
        );
      });

      testWidgets('should generate content labels based on type', (tester) async {
        await accessibilityService.initialize();

        expect(
          accessibilityService.generateContentLabel('Main Title', 'header'),
          equals('Main Title, titre'),
        );

        expect(
          accessibilityService.generateContentLabel('Click here', 'link'),
          equals('Click here, lien'),
        );

        expect(
          accessibilityService.generateContentLabel('Enter name', 'input'),
          equals('Enter name, champ de saisie'),
        );

        expect(
          accessibilityService.generateContentLabel('Profile picture', 'image'),
          equals('Image: Profile picture'),
        );

        expect(
          accessibilityService.generateContentLabel('', 'image'),
          equals('Image'),
        );
      });

      testWidgets('should generate quiz option labels correctly', (tester) async {
        await accessibilityService.initialize();

        expect(
          accessibilityService.generateQuizOptionLabel('Paris', 0, false, false),
          equals('Option A: Paris'),
        );

        expect(
          accessibilityService.generateQuizOptionLabel('London', 1, true, false),
          equals('Option B: London, sélectionné'),
        );

        expect(
          accessibilityService.generateQuizOptionLabel('Madrid', 2, true, true),
          equals('Option C: Madrid, sélectionné, correct'),
        );
      });

      testWidgets('should generate form field labels with validation', (tester) async {
        await accessibilityService.initialize();

        expect(
          accessibilityService.generateFormFieldLabel('Email', true, null, 'Enter your email'),
          equals('Email, requis, Enter your email'),
        );

        expect(
          accessibilityService.generateFormFieldLabel('Password', true, 'Too short', null),
          equals('Password, requis, erreur: Too short'),
        );

        expect(
          accessibilityService.generateFormFieldLabel('Name', false, null, null),
          equals('Name'),
        );
      });
    });

    group('Keyboard Shortcut Management', () {
      testWidgets('should register and execute keyboard shortcuts', (tester) async {
        await accessibilityService.initialize();

        bool shortcutExecuted = false;
        void testCallback() {
          shortcutExecuted = true;
        }

        accessibilityService.registerKeyboardShortcut('test_shortcut', testCallback);

        expect(accessibilityService.registeredShortcuts.containsKey('test_shortcut'), isTrue);

        final executed = accessibilityService.executeKeyboardShortcut('test_shortcut');
        expect(executed, isTrue);
        expect(shortcutExecuted, isTrue);
      });

      testWidgets('should unregister keyboard shortcuts', (tester) async {
        await accessibilityService.initialize();

        void testCallback() {}
        accessibilityService.registerKeyboardShortcut('test_shortcut', testCallback);

        expect(accessibilityService.registeredShortcuts.containsKey('test_shortcut'), isTrue);

        accessibilityService.unregisterKeyboardShortcut('test_shortcut');

        expect(accessibilityService.registeredShortcuts.containsKey('test_shortcut'), isFalse);
      });

      testWidgets('should return false for non-existent shortcuts', (tester) async {
        await accessibilityService.initialize();

        final executed = accessibilityService.executeKeyboardShortcut('non_existent');
        expect(executed, isFalse);
      });
    });

    group('Focus Management', () {
      testWidgets('should register and manage focus nodes', (tester) async {
        await accessibilityService.initialize();

        final focusNode1 = FocusNode();
        final focusNode2 = FocusNode();

        accessibilityService.registerFocusNode('node1', focusNode1);
        accessibilityService.registerFocusNode('node2', focusNode2);

        expect(accessibilityService.getNextFocusNode('node1'), equals(focusNode2));
        expect(accessibilityService.getPreviousFocusNode('node2'), equals(focusNode1));
      });

      testWidgets('should unregister focus nodes', (tester) async {
        await accessibilityService.initialize();

        final focusNode = FocusNode();
        accessibilityService.registerFocusNode('test_node', focusNode);

        accessibilityService.unregisterFocusNode('test_node');

        expect(accessibilityService.getNextFocusNode('test_node'), isNull);
      });

      testWidgets('should handle focus traversal edge cases', (tester) async {
        await accessibilityService.initialize();

        final focusNode = FocusNode();
        accessibilityService.registerFocusNode('only_node', focusNode);

        expect(accessibilityService.getNextFocusNode('only_node'), isNull);
        expect(accessibilityService.getPreviousFocusNode('only_node'), isNull);
      });
    });

    group('Keyboard Navigation', () {
      testWidgets('should update keyboard navigation preference', (tester) async {
        await accessibilityService.initialize();

        expect(accessibilityService.isKeyboardNavigationEnabled, isFalse);

        await accessibilityService.updateKeyboardNavigation(true);
        expect(accessibilityService.isKeyboardNavigationEnabled, isTrue);

        await accessibilityService.updateKeyboardNavigation(false);
        expect(accessibilityService.isKeyboardNavigationEnabled, isFalse);
      });

      testWidgets('should notify listeners when keyboard navigation changes', (tester) async {
        await accessibilityService.initialize();

        bool notified = false;
        accessibilityService.addListener(() {
          notified = true;
        });

        await accessibilityService.updateKeyboardNavigation(true);

        expect(notified, isTrue);
      });
    });

    group('Reduced Motion', () {
      testWidgets('should update reduced motion preference', (tester) async {
        await accessibilityService.initialize();

        expect(accessibilityService.isReducedMotionEnabled, isFalse);

        await accessibilityService.updateReducedMotion(true);
        expect(accessibilityService.isReducedMotionEnabled, isTrue);

        await accessibilityService.updateReducedMotion(false);
        expect(accessibilityService.isReducedMotionEnabled, isFalse);
      });

      testWidgets('should notify listeners when reduced motion changes', (tester) async {
        await accessibilityService.initialize();

        bool notified = false;
        accessibilityService.addListener(() {
          notified = true;
        });

        await accessibilityService.updateReducedMotion(true);

        expect(notified, isTrue);
      });
    });

    group('Navigation Mode', () {
      testWidgets('should update navigation mode preference', (tester) async {
        await accessibilityService.initialize();

        await accessibilityService.updateNavigationMode(PreferredNavigationMode.keyboard);
        expect(accessibilityService.preferences.preferredNavigationMode, equals(PreferredNavigationMode.keyboard));

        await accessibilityService.updateNavigationMode(PreferredNavigationMode.touch);
        expect(accessibilityService.preferences.preferredNavigationMode, equals(PreferredNavigationMode.touch));
      });
    });

    group('Touch Target Size', () {
      testWidgets('should calculate minimum touch target size correctly', (tester) async {
        await accessibilityService.initialize();

        // Default font scale (1.0) on phone
        expect(accessibilityService.getMinimumTouchTargetSize(), equals(44.0));

        await accessibilityService.updateFontScale(1.5);
        expect(accessibilityService.getMinimumTouchTargetSize(), equals(66.0));
      });
    });

    group('Reset to Defaults', () {
      testWidgets('should reset all preferences to defaults', (tester) async {
        await accessibilityService.initialize();

        // Change some preferences
        await accessibilityService.updateFontScale(1.5);
        await accessibilityService.toggleHighContrast();
        await accessibilityService.enableScreenReader(true);

        // Reset to defaults
        await accessibilityService.resetToDefaults();

        expect(accessibilityService.fontScale, equals(1.0));
        expect(accessibilityService.isHighContrastEnabled, isFalse);
        expect(accessibilityService.isScreenReaderEnabled, isFalse);
      });

      testWidgets('should notify listeners when resetting to defaults', (tester) async {
        await accessibilityService.initialize();

        bool notified = false;
        accessibilityService.addListener(() {
          notified = true;
        });

        await accessibilityService.resetToDefaults();

        expect(notified, isTrue);
      });
    });

    group('Theme Integration', () {
      testWidgets('should integrate with theme service', (tester) async {
        await accessibilityService.initialize();
        accessibilityService.setThemeService(mockThemeService);

        await accessibilityService.updateFontScale(1.2);
        verify(mockThemeService.notifyListeners()).called(1);

        await accessibilityService.toggleHighContrast();
        verify(mockThemeService.notifyListeners()).called(2);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle TTS errors gracefully', (tester) async {
        when(mockTts.speak(any)).thenThrow(Exception('TTS error'));

        await accessibilityService.initialize();
        await accessibilityService.enableScreenReader(true);

        // Should not throw
        expect(
          () => accessibilityService.announceToScreenReader('Test'),
          returnsNormally,
        );
      });

      testWidgets('should handle storage errors gracefully', (tester) async {
        when(mockBox.put(any, any)).thenThrow(Exception('Storage error'));

        await accessibilityService.initialize();

        // Should not throw
        expect(
          () => accessibilityService.updateFontScale(1.2),
          returnsNormally,
        );
      });

      testWidgets('should handle invalid focus node requests', (tester) async {
        await accessibilityService.initialize();

        // Should not throw for non-existent focus node
        expect(
          () => accessibilityService.requestFocus('non_existent'),
          returnsNormally,
        );
      });
    });

    group('Performance and Memory', () {
      testWidgets('should dispose resources properly', (tester) async {
        await accessibilityService.initialize();

        // Register some resources
        accessibilityService.registerFocusNode('test', FocusNode());
        accessibilityService.registerKeyboardShortcut('test', () {});

        accessibilityService.dispose();

        verify(mockTts.stop()).called(1);
        expect(accessibilityService.registeredShortcuts.isEmpty, isTrue);
      });

      testWidgets('should handle multiple rapid preference changes', (tester) async {
        await accessibilityService.initialize();

        // Rapidly change font scale multiple times
        for (int i = 0; i < 10; i++) {
          await accessibilityService.updateFontScale(1.0 + (i * 0.1));
        }

        expect(accessibilityService.fontScale, equals(1.9));
      });

      testWidgets('should handle large numbers of focus nodes', (tester) async {
        await accessibilityService.initialize();

        // Register many focus nodes
        for (int i = 0; i < 100; i++) {
          accessibilityService.registerFocusNode('node_$i', FocusNode());
        }

        // Should still work correctly
        expect(accessibilityService.getNextFocusNode('node_0')?.debugLabel, isNull);
        expect(accessibilityService.getPreviousFocusNode('node_99')?.debugLabel, isNull);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle extreme font scaling edge cases', (tester) async {
        await accessibilityService.initialize();

        // Test boundary values
        await accessibilityService.updateFontScale(0.8);
        expect(accessibilityService.getScaledFontSize(10.0), equals(8.0));

        await accessibilityService.updateFontScale(2.0);
        expect(accessibilityService.getScaledFontSize(10.0), equals(20.0));
      });

      testWidgets('should handle empty and null strings in label generation', (tester) async {
        await accessibilityService.initialize();

        expect(
          accessibilityService.generateButtonLabel('', 'context'),
          equals(', bouton, context'),
        );

        expect(
          accessibilityService.generateContentLabel('', 'text'),
          equals(''),
        );
      });

      testWidgets('should handle special characters in announcements', (tester) async {
        await accessibilityService.initialize();
        await accessibilityService.enableScreenReader(true);

        const specialMessage = 'Test with émojis 🎉 and àccénts';
        await accessibilityService.announceToScreenReader(specialMessage);

        verify(mockTts.speak(specialMessage)).called(1);
      });
    });

    group('Context Extension', () {
      testWidgets('should provide accessibility context extension', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                // Test context extension methods
                expect(context.isScreenReaderEnabled, isFalse);
                expect(context.isHighContrastEnabled, isFalse);
                expect(context.fontScale, equals(1.0));
                expect(context.getScaledFontSize(16.0), equals(16.0));

                return Container();
              },
            ),
          ),
        );
      });
    });
  });

  group('Integration Tests', () {
    testWidgets('should work with real Hive storage', (tester) async {
      // Test with actual Hive storage (not mocked)
      final service = AccessibilityService();
      await service.initialize();

      await service.updateFontScale(1.3);
      await service.toggleHighContrast();

      // Create new service instance to test persistence
      final newService = AccessibilityService();
      await newService.initialize();

      expect(newService.fontScale, equals(1.3));
      expect(newService.isHighContrastEnabled, isTrue);
    });

    testWidgets('should integrate with Flutter semantics', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Semantics(
            label: 'Test widget',
            child: Container(),
          ),
        ),
      );

      final semantics = tester.getSemantics(find.byType(Container));
      expect(semantics.label, equals('Test widget'));
    });
  });
}