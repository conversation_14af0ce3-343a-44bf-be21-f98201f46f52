[warning] The value of the field '_currentError' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/custom_text_field.dart:121:11)
[warning] Unused import: '../../models/adaptive_learning/spaced_repetition_item.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/adaptive_learning/spaced_repetition_review_widget.dart:6:8)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/personalized_recommendations.dart:121:78)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/personalized_recommendations.dart:157:42)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/achievement_notification.dart:111:41)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/achievement_notification.dart:181:43)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/achievement_badge.dart:55:72)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/achievement_badge.dart:87:78)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/achievement_badge.dart:107:39)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/gamification_dashboard.dart:77:56)
[warning] Unused import: 'learning_streak_widget.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/gamification_dashboard.dart:12:8)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/gamification/learning_streak_widget.dart:70:54)
[warning] Unused import: '../../theme/semantic_colors.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/difficulty_indicator.dart:3:8)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/prerequisite_checker.dart:196:30)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/prerequisite_checker.dart:198:49)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/prerequisite_checker.dart:309:57)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/prerequisite_checker.dart:380:55)
[warning] The declaration 'statusIndicator' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/prerequisite_checker.dart:455:17)
[warning] Unused import: '../../../theme/semantic_colors.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/accounting_simulation_widget.dart:2:8)
[warning] Unused import: 'step_by_step_walkthrough.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/accounting_simulation_widget.dart:3:8)
[warning] The value of the field '_currentEntryIndex' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/accounting_simulation_widget.dart:37:13)
[warning] The value of the field '_showValidation' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/accounting_simulation_widget.dart:38:14)
[warning] The value of the field '_simulationComplete' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/accounting_simulation_widget.dart:39:14)
[warning] Unused import: '../../../services/guide_progress_service.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/step_by_step_walkthrough.dart:3:8)
[warning] The value of the local variable 'index' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/step_by_step_walkthrough.dart:406:17)
[warning] The value of the local variable 'variables' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/interactive_examples/step_by_step_walkthrough.dart:480:11)
[warning] The value of the local variable 'milestone' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/guide_progress_indicator.dart:298:23)
[warning] Unused import: '../../theme/design_tokens.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/bookmark_button.dart:3:8)
[warning] The declaration 'compact' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/bookmark_button.dart:325:17)
[warning] The declaration 'labeled' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/bookmark_button.dart:345:17)
[warning] The value of the local variable 'labels' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/visual_learning_aids/diagram_renderer.dart:219:11)
[warning] The value of the local variable 'note' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/personal_notes/note_editor.dart:972:13)
[warning] The declaration 'compact' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/personal_notes/notes_list_view.dart:819:17)
[warning] The value of the field '_expandedSection' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/guide_accordion_view.dart:37:11)
[warning] The value of the field '_contentService' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/guide/guide_search_delegate.dart:23:29)
[warning] The value of the local variable 'isPending' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/is/is_wizard_progress.dart:120:11)
[warning] Unused import: '../../utils/calculation_utils.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/is/sector_selection_card.dart:6:8)
[warning] Unused import: '../../theme/app_theme.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/is/sector_selection_card.dart:7:8)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/is/sector_selection_card.dart:242:11)
[warning] Unused import: '../../models/salary/salary_data.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/ir/family_status_card.dart:3:8)
[warning] Unused import: '../../models/salary/salary_data.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/ir/bonuses_card.dart:3:8)
[warning] The value of the field '_lastFocusedItem' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/adaptive_navigation.dart:53:19)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/cpc_widget.dart:265:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/cpc_widget.dart:267:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/cpc_widget.dart:304:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/cpc_widget.dart:322:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/balance_widget.dart:280:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/balance_widget.dart:380:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/balance_widget.dart:398:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/bilan_widget.dart:299:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/financial_statements/bilan_widget.dart:316:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/inventory/evaluation_method_card.dart:115:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/inventory/method_card_widget.dart:17:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/inventory/inventory_system_card.dart:125:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/inventory/technical_aspect_card.dart:21:11)
[warning] The value of the local variable 'rightWidth' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/exam_layout_widgets.dart:42:15)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/calculation_history_widget.dart:363:30)
[info] The private field _selectedItems could be 'final' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/calculation_history_widget.dart:20:32)
[info] The private field _selectedRegion could be 'final' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/tax_optimization_wizard.dart:31:10)
[info] The private field _existingDeductions could be 'final' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/tax_optimization_wizard.dart:40:16)
[info] The private field _selectedObjectives could be 'final' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/tax_optimization_wizard.dart:43:31)
[info] The private field _excludedStrategies could be 'final' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/tax_optimization_wizard.dart:53:16)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/guided_wizard_scaffold.dart:155:40)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/guided_wizard_scaffold.dart:236:33)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/financial_ratios_calculator.dart:610:5)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/financial_ratios_calculator.dart:613:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/calculators/financial_ratios_calculator.dart:617:5)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/tva/tva_invoice_summary.dart:34:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/immobilisations/comparison_view_widget.dart:544:11)
[warning] The operand can't be 'null', so the condition is always 'true' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/immobilisations/amortization_chart_widget.dart:408:58)
[info] The import of 'dart:typed_data' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/services.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/immobilisations/amortization_chart_widget.dart:3:8)
[warning] The member 'sortDescendants' can only be used within instance members of subclasses of 'package:flutter/src/widgets/focus_traversal.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/focus_traversal_group.dart:440:19)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/focus_traversal_group.dart:700:48)
[warning] The value of the local variable 'isCtrlPressed' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/focus_traversal_group.dart:311:11)
[info] Invalid use of a private type in a public API (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/focus_traversal_group.dart:740:3)
[info] Constructors in '@immutable' classes should be declared as 'const' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/focus_traversal_group.dart:594:3)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:310:20)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:311:16)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:314:24)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:315:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:316:35)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:317:58)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:319:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:320:35)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:321:58)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:323:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:324:35)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:325:58)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:329:24)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:330:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:331:35)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:332:58)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:337:18)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:339:29)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:340:29)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:345:14)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:352:21)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:353:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:354:35)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:355:58)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:357:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:358:35)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:359:58)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:361:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:362:35)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:363:58)
[info] 'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:368:13)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:369:29)
[info] 'MaterialState' is deprecated and shouldn't be used. Use WidgetState instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:378:36)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:379:25)
[warning] The value of the field '_isHovered' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:187:8)
[warning] The value of the field '_isPressed' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_button.dart:188:8)
[warning] Unused import: '../custom_text_field.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/accessible_text_field.dart:4:8)
[warning] Unused import: '../../theme/semantic_colors.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/accessibility/semantic_wrapper.dart:5:8)
[warning] The '!' will have no effect because the receiver can't be null (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/analytics/performance_analytics_dashboard.dart:66:45)
[warning] The '!' will have no effect because the receiver can't be null (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/analytics/performance_analytics_dashboard.dart:76:50)
[warning] The '!' will have no effect because the receiver can't be null (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/analytics/performance_analytics_dashboard.dart:86:43)
[warning] Duplicate import (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/analytics/performance_analytics_dashboard.dart:8:8)
[warning] Unused import: '../../services/adaptive_learning_service.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/widgets/analytics/performance_analytics_dashboard.dart:11:8)
[warning] The value of the local variable 'goalStats' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/gamification/daily_goals_screen.dart:165:11)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/gamification/achievements_screen.dart:107:45)
[warning] The value of the field '_selectedAchievement' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/gamification/achievements_screen.dart:23:26)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/gamification/achievements_screen.dart:73:34)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/gamification/achievements_screen.dart:74:42)
[warning] Dead code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:261:27)
[warning] Dead code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:269:33)
[warning] Dead code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:273:54)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:321:38)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:325:39)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:330:41)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:332:34)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:342:37)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:344:32)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:355:37)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:357:32)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:368:37)
[warning] The receiver can't be null, so the null-aware operator '?.' is unnecessary (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:370:32)
[warning] The value of the local variable 'accessibility' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:53:11)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:47:5)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:1023:35)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/profile/profile_screen.dart:1024:42)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_question_screen.dart:81:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_question_screen.dart:83:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_question_screen.dart:400:36)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_question_screen.dart:402:36)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_question_screen.dart:417:35)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_question_screen.dart:419:36)
[warning] The value of the local variable 'startNewQuiz' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_screen.dart:45:10)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/quiz/quiz_screen.dart:92:9)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/offline/offline_content_screen.dart:991:71)
[info] The import of 'package:flutter/services.dart' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/material.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/offline/offline_content_screen.dart:3:8)
[warning] The value of the local variable 'cacheKey' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/offline/offline_content_screen.dart:465:13)
[info] The private field _downloadTimers could be 'final' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/offline/offline_content_screen.dart:121:22)
[info] Function literals shouldn't be passed to 'forEach' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/offline/offline_content_screen.dart:142:28)
[info] Invalid use of a private type in a public API (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/plan_comptable_screen.dart:13:3)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/plan_comptable_screen.dart:74:7)
[warning] The value of the local variable 'currentStep' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/tax_optimization_screen.dart:30:11)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/calculation_history_screen.dart:169:49)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/calculation_history_screen.dart:394:56)
[warning] The value of the local variable 'historyActions' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/calculation_history_screen.dart:33:11)
[warning] The value of the local variable 'historyActions' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/calculation_history_screen.dart:518:11)
[warning] The value of the local variable 'backup' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/calculation_history_screen.dart:571:13)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/calculation_history_screen.dart:573:28)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/calculators/calculation_history_screen.dart:580:28)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/splash_screen.dart:70:22)
[info] 'WillPopScope' is deprecated and shouldn't be used. Use PopScope instead. The Android predictive back feature will not work with WillPopScope. This feature was deprecated after v3.12.0-1.0.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_approfondie/provisions/provisions_screen.dart:45:12)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_approfondie/provisions/provisions_screen.dart:42:11)
[warning] The declaration '_CompanyBox' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_approfondie/consolidation/widgets/consolidation_widgets.dart:301:7)
[warning] The value of the field '_expandedSections' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_approfondie/consolidation/sections/methodes_section.dart:12:27)
[warning] The value of the local variable 'entries' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_approfondie/immobilisations/widgets/section_base.dart:110:15)
[warning] This default clause is covered by the previous cases (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/bookmarks_screen.dart:1032:9)
[warning] Unused import: '../../theme/semantic_colors.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/bookmarks_screen.dart:9:8)
[warning] The value of the local variable 'content' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/bookmarks_screen.dart:1393:11)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/bookmarks_screen.dart:1464:29)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/bookmarks_screen.dart:1466:36)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/bookmarks_screen.dart:1485:9)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/bookmarks_screen.dart:1500:28)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/gestion_stocks_screen.dart:181:11)
[warning] The declaration '_buildExpandableCard' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/operations/effets_commerce_screen.dart:188:10)
[warning] The declaration '_buildFlowDiagram' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/operations/effets_commerce_screen.dart:250:10)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/operations/effets_commerce_screen.dart:526:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/documents_comptables_screen.dart:191:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/documents_comptables_screen.dart:192:11)
[warning] The declaration '_buildStructureSection' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/documents_comptables_screen.dart:476:10)
[warning] The declaration '_buildCharacteristicsList' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/documents_comptables_screen.dart:529:10)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/plan_comptable_screen.dart:144:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/plan_comptable_screen.dart:335:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/plan_comptable_screen.dart:415:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/plan_comptable_screen.dart:494:11)
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_generale/plan_comptable_screen.dart:601:11)
[info] 'dataRowHeight' is deprecated and shouldn't be used. Migrate to use dataRowMinHeight and dataRowMaxHeight instead. This feature was deprecated after v3.7.0-5.0.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_societes/affectation_resultats/affectation_resultats_screen.dart:760:29)
[info] 'dataRowHeight' is deprecated and shouldn't be used. Migrate to use dataRowMinHeight and dataRowMaxHeight instead. This feature was deprecated after v3.7.0-5.0.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_societes/modification_capital/modification_capital_screen.dart:475:29)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_societes/comptabilite_societes_screen.dart:12:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_societes/comptabilite_societes_screen.dart:13:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_societes/fusion/sections/fusion_absorption_section.dart:92:11)
[info] 'dataRowHeight' is deprecated and shouldn't be used. Migrate to use dataRowMinHeight and dataRowMaxHeight instead. This feature was deprecated after v3.7.0-5.0.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/comptabilite_societes/dissolution/dissolution_screen.dart:520:29)
[warning] The value of the local variable 'isJournalEntry' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/is/sections/exercices_is_section.dart:126:10)
[warning] The left operand can't be null, so the right operand is never executed (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/is/sections/reintegrations_deductions_section.dart:179:75)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/is/sections/calculator_section.dart:191:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/is/sections/calculator_section.dart:192:11)
[info] Invalid use of a private type in a public API (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/ir/sections/cnss_section.dart:10:3)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/ir/sections/cnss_section.dart:35:28)
[info] Invalid use of a private type in a public API (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/ir/sections/deductions_section.dart:10:3)
[info] Invalid use of a private type in a public API (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/ir/sections/exercices_section.dart:10:3)
[warning] The value of the field '_montantHT' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/widgets/ras_tva_checker_widget.dart:15:10)
[info] Invalid use of a private type in a public API (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/widgets/ras_tva_checker_widget.dart:7:3)
[warning] The value of the field '_advancedItem' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/sections/calculateur_section.dart:26:16)
[warning] Unused import: '../../../../../utils/calculation_utils.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/sections/calculatrice_tva_section.dart:7:8)
[warning] The value of the field '_tvaService' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/sections/calculatrice_tva_section.dart:22:20)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/sections/calculatrice_tva_section.dart:98:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/sections/calculatrice_tva_section.dart:99:11)
[warning] The value of the local variable 'autresExclus' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/tva/sections/prorata_tva_section.dart:65:13)
[warning] The value of the field '_guideData' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/liasse_fiscale/sections/guide_section.dart:13:25)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/cooperatives_section.dart:8:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/cooperatives_section.dart:9:11)
[warning] The value of the local variable 'reducedRate' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/calculateurs_agricoles_section.dart:225:11)
[warning] The value of the local variable 'totalIncome' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/calculateurs_agricoles_section.dart:279:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/ir_agricole_section.dart:8:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/ir_agricole_section.dart:9:11)
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/tva_agricole_section.dart:8:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/guides/fiscalite/agriculture/sections/tva_agricole_section.dart:9:11)
[info] Don't use 'BuildContext's across async gaps (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/references_screen.dart:25:28)
[warning] The value of the local variable 'analyticsState' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/performance_analytics_screen.dart:29:11)
[warning] The value of the local variable 'textTheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/home/<USER>
[warning] The value of the local variable 'isDark' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/home/<USER>
[warning] The value of the local variable 'colorScheme' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/home_screen.dart:478:11)
[info] Use interpolation to compose strings and values (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/home_screen.dart:233:13)
[info] 'surfaceVariant' is deprecated and shouldn't be used. Use surfaceContainerHighest instead. This feature was deprecated after v3.18.0-0.1.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/accessibility/accessibility_settings_screen.dart:994:54)
[info] The import of 'package:flutter/services.dart' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/material.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/accessibility/accessibility_settings_screen.dart:2:8)
[info] The private field _assessmentAnswers could be 'final' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/accessibility/accessibility_settings_screen.dart:39:24)
[info] Unnecessary use of 'toList' in a spread (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/accessibility/accessibility_settings_screen.dart:1191:16)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/accessibility/accessibility_settings_screen.dart:1491:30)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/screens/accessibility/accessibility_settings_screen.dart:1492:38)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/theme/accessibility_theme_extension.dart:170:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/theme/app_typography.dart:244:33)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/theme/app_typography.dart:255:33)
[warning] The value of the field '_spacing' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/theme/app_theme.dart:14:23)
[warning] The value of the field '_smallSpacing' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/theme/app_theme.dart:15:23)
[warning] The left operand can't be null, so the right operand is never executed (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/bookmark_service.dart:141:73)
[warning] This cast always throws an exception because the expression always evaluates to 'null' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/question_selection_service.dart:200:23)
[warning] The declaration '_updateRecentlyUsedCache' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/question_selection_service.dart:431:8)
[warning] The value of the field '_userProgressService' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/adaptive_difficulty_service.dart:10:29)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:14:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:20:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:23:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:25:9)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:35:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:37:9)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:44:9)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:51:11)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:53:11)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:64:11)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:71:15)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:73:15)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:77:11)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:86:13)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:88:13)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:94:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/exam/exam_data_service.dart:100:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:24:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:29:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:34:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:37:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:44:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:49:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:54:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:57:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:64:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:69:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:74:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:77:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:84:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:89:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:94:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:97:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:104:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:109:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:114:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:117:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:124:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:129:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:134:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:137:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/tva_service.dart:143:5)
[warning] The value of the local variable 'cacheStats' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/guide_content_service.dart:447:11)
[warning] The value of the local variable 'hourlyRate' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/salary_calculator_service.dart:47:13)
[warning] The declaration '_calculateProfessionalExpenses' isn't referenced (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/salary_calculator_service.dart:241:10)
[info] 'surfaceVariant' is deprecated and shouldn't be used. Use surfaceContainerHighest instead. This feature was deprecated after v3.18.0-0.1.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/theme_service.dart:315:9)
[info] 'surfaceVariant' is deprecated and shouldn't be used. Use surfaceContainerHighest instead. This feature was deprecated after v3.18.0-0.1.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/theme_service.dart:327:7)
[info] The imported package 'uuid' isn't a dependency of the importing package (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:2:8)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:86:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:88:8)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:126:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:128:8)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:142:5)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:147:5)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:149:5)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:157:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:159:8)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:172:5)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:176:5)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:178:5)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:185:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/user_progress_service.dart:187:8)
[info] The import of 'dart:typed_data' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/foundation.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/offline_cache_service.dart:4:8)
[info] The imported package 'archive' isn't a dependency of the importing package (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/offline_cache_service.dart:5:8)
[info] The imported package 'crypto' isn't a dependency of the importing package (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/offline_cache_service.dart:11:8)
[info] Unnecessary braces in a string interpolation (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/offline_cache_service.dart:312:52)
[info] Unnecessary braces in a string interpolation (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/offline_cache_service.dart:316:51)
[info] Unnecessary braces in a string interpolation (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/offline_cache_service.dart:338:67)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/agriculture_service.dart:57:7)
[info] Statements in an if should be enclosed in a block (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/performance_analytics_service.dart:354:34)
[info] Statements in an if should be enclosed in a block (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/performance_analytics_service.dart:355:32)
[info] Statements in an if should be enclosed in a block (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/services/performance_analytics_service.dart:356:12)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/providers/guide_progress_provider.dart:163:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/providers/calculator_providers.dart:163:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/providers/calculator_providers.dart:181:7)
[info] Don't invoke 'print' in production code (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/providers/calculator_providers.dart:199:7)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/utils/keyboard_shortcuts.dart:752:37)
[info] 'surfaceVariant' is deprecated and shouldn't be used. Use surfaceContainerHighest instead. This feature was deprecated after v3.18.0-0.1.pre (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/utils/keyboard_shortcuts.dart:818:44)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/utils/keyboard_shortcuts.dart:892:48)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/utils/keyboard_shortcuts.dart:895:50)
[warning] The value of the local variable 'guideContentService' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/main.dart:200:9)
[warning] The value of the local variable 'questionSelectionService' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/main.dart:234:9)
[info] 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/models/guide/bookmark_data.dart:76:49)
[info] 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/models/guide/bookmark_data.dart:93:22)
[error] The method '_$CalculationContextFromJson' isn't defined for the type 'CalculationContext' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/models/calculators/enhanced_depreciation_data.dart:375:7)
[error] The method '_$CalculationContextToJson' isn't defined for the type 'CalculationContext' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/models/calculators/enhanced_depreciation_data.dart:377:36)
[error] A nullable expression can't be used as a condition (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/models/calculators/enhanced_depreciation_data.dart:716:16)
[warning] The left operand can't be null, so the right operand is never executed (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/models/calculators/enhanced_depreciation_data.dart:716:68)
[info] The member 'key' overrides an inherited member but isn't annotated with '@override' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/lib/models/hive/cache_entry.dart:9:16)
[info] 'opacity' is deprecated and shouldn't be used. Use .a (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/widget/fusion_screen_test.dart:41:38)
[info] 'opacity' is deprecated and shouldn't be used. Use .a (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/widget/fusion_screen_test.dart:81:42)
[info] 'opacity' is deprecated and shouldn't be used. Use .a (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/widget/fusion_screen_test.dart:89:32)
[info] 'red' is deprecated and shouldn't be used. Use (*.r * 255.0).round() & 0xff (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/widget/semantic_colors_test.dart:218:44)
[info] 'green' is deprecated and shouldn't be used. Use (*.g * 255.0).round() & 0xff (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/widget/semantic_colors_test.dart:219:44)
[info] 'blue' is deprecated and shouldn't be used. Use (*.b * 255.0).round() & 0xff (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/widget/semantic_colors_test.dart:220:44)
[warning] The value of the local variable 'noteWithSpecialChars' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/services/note_sharing_service_test.dart:235:15)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:64:24)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:201:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:234:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:274:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:296:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:344:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:381:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:406:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:600:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:632:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:682:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:745:30)
[error] The name 'CacheEntry' isn't a type, so it can't be used as a type argument (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:856:45)
[error] Undefined class 'CacheEntry' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:861:3)
[error] Undefined class 'CacheEntry' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:880:35)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:202:30)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:275:30)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:299:30)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:309:28)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:384:25)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:409:32)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:419:35)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:635:25)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:685:25)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:748:30)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:768:23)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:789:30)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:803:28)
[error] The function 'CacheEntry' isn't defined (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:817:26)
[error] The method 'CacheEntry' isn't defined for the type 'CacheEntryAdapter' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:866:12)
[warning] The value of the local variable 'mockFile' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:26:19)
[warning] The value of the local variable 'invalidDir' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/offline/offline_cache_service_test.dart:85:15)
[warning] Unused import: 'package:flutter/semantics.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/accessibility/accessibility_service_test.dart:2:8)
[warning] Unused import: 'package:flutter/services.dart' (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/accessibility/accessibility_service_test.dart:3:8)
[warning] The value of the local variable 'mockDeviceInfo' isn't used (/home/<USER>/Documents/repos/flutter/Hielcompta/moroccanaccounting/test/accessibility/accessibility_service_test.dart:28:31)